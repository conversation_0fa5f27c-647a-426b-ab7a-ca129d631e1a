package com.jp.med.ams.modules.config.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 存放地点配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-12 10:23:40
 */
@Data
@TableName("ams_storage_cfg" )
public class AmsStorageCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 存放地点编码 */
    @TableField(value = "storage_area_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String storageAreaCode;

    /** 存放地点 */
    @TableField(value = "storage_area",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String storageArea;

    /** 上级编码 */
    @TableField(value = "parent_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String parentId;

    /** 医疗机构编码 */
    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

    /** 有效标志 */
    @TableField(value = "active_flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

}
