package com.jp.med.erp.modules.config.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.mapper.write.ErpVcrSalaryConfigDetailWriteMapper;
import com.jp.med.erp.modules.config.service.write.ErpVcrSalaryConfigDetailWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 财务核算-工资凭证科目映射配置明细
 * <AUTHOR>
 * @email -
 * @date 2024-08-22 09:54:23
 */
@Service
@Transactional(readOnly = false)
public class ErpVcrSalaryConfigDetailWriteServiceImpl extends ServiceImpl<ErpVcrSalaryConfigDetailWriteMapper, ErpVcrSalaryConfigDetailDto> implements ErpVcrSalaryConfigDetailWriteService {
}
