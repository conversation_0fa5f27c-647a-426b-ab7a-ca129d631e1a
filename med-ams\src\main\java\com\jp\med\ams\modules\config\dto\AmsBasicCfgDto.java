package com.jp.med.ams.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 资产配置表
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:47:25
 */
@Data
@TableName("ams_basic_cfg" )
public class AmsBasicCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 编码 */
    @TableField("code")
    private String code;

    /** 名称 */
    @TableField("name")
    private String name;

    /** 说明 */
    @TableField("dscr")
    private String dscr;

    /** 类型 */
    @TableField("type")
    private String type;

    /** 状态 */
    @TableField("flag")
    private String flag;


    /**
     * ref split_code 拆分代码
     */
    @TableField(exist = false)
    private String split_code;

    @TableField(exist = false)
    private String year;

    @TableField(exist = false)
    private String qs;
}
