-- =================================================================================================
-- 出库数据快速修复脚本
-- 功能：修复组织字段映射，使用正确的字段名
-- 执行时机：在当前事务中继续执行
-- =================================================================================================

-- 检查hrm_org表中的组织结构（使用正确字段名）
SELECT 
    'hrm_org表检查' as check_type,
    COUNT(*) as total_orgs,
    COUNT(CASE WHEN org_parent_id IS NULL OR org_parent_id = '' THEN 1 END) as root_orgs,
    COUNT(CASE WHEN org_parent_id IS NOT NULL AND org_parent_id != '' THEN 1 END) as child_orgs
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' AND active_flag = '1';

-- 清空out_target_org_id字段，准备重新填充
UPDATE mmis_temp_xinxike_outbound_six 
SET out_target_org_id = NULL;

-- 通过组织名称查询组织ID，优先选择叶子节点
UPDATE mmis_temp_xinxike_outbound_six t
SET out_target_org_id = (
    WITH leaf_orgs AS (
        SELECT 
            o.org_id,
            o.org_name,
            CASE 
                WHEN NOT EXISTS (
                    SELECT 1 FROM hrm_org child 
                    WHERE child.org_parent_id = o.org_id 
                      AND child.hospital_id = 'zjxrmyy' 
                      AND child.active_flag = '1'
                ) THEN 1 
                ELSE 0 
            END as is_leaf
        FROM hrm_org o
        WHERE o.hospital_id = 'zjxrmyy' 
          AND o.active_flag = '1'
          AND o.org_name = t.out_org
    )
    SELECT org_id 
    FROM leaf_orgs 
    ORDER BY is_leaf DESC, org_id 
    LIMIT 1
)
WHERE t.out_org IS NOT NULL AND TRIM(t.out_org) != '';

-- 对于仍然没有匹配的，设置默认值（信息科）
UPDATE mmis_temp_xinxike_outbound_six 
SET out_target_org_id = '521001'  -- 信息科
WHERE out_target_org_id IS NULL 
  AND out_org IS NOT NULL 
  AND TRIM(out_org) != '';

-- 验证修复结果
SELECT 
    '修复结果' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) as has_org_id,
    ROUND(COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL;

-- 显示映射结果
SELECT 
    '组织映射结果' as result_type,
    out_org,
    out_target_org_id,
    o.org_name as verified_name,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_org o ON t.out_target_org_id = o.org_id
WHERE t.out_org IS NOT NULL
GROUP BY out_org, out_target_org_id, o.org_name
ORDER BY record_count DESC;

-- 提交事务
COMMIT;
