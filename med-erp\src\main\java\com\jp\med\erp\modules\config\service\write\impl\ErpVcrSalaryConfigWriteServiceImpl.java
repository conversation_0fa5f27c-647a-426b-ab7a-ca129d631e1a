package com.jp.med.erp.modules.config.service.write.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDto;
import com.jp.med.erp.modules.config.mapper.write.ErpVcrSalaryConfigDetailWriteMapper;
import com.jp.med.erp.modules.config.mapper.write.ErpVcrSalaryConfigWriteMapper;
import com.jp.med.erp.modules.config.service.write.ErpVcrSalaryConfigWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 工资凭证配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 00:52:39
 */
@Service
@Transactional(readOnly = false)
public class ErpVcrSalaryConfigWriteServiceImpl extends ServiceImpl<ErpVcrSalaryConfigWriteMapper, ErpVcrSalaryConfigDto> implements ErpVcrSalaryConfigWriteService {

    @Autowired
    private ErpVcrSalaryConfigDetailWriteMapper erpVcrSalaryConfigDetailWriteMapper;
    @Override
    public void saveDetails(ErpVcrSalaryConfigDetailDto dto) {
        if (ObjectUtil.isNull(dto.getSalaryConfigId())) {
            throw new AppException("配置项目参数不完整");
        }
        //设置工资配置项目id
        dto.getErpAsstDetails().stream().forEach(e -> e.setSalaryConfigId(dto.getSalaryConfigId()));

        //删除旧配置项目明细
        LambdaUpdateWrapper<ErpVcrSalaryConfigDetailDto> detailsWrapper = Wrappers.lambdaUpdate(ErpVcrSalaryConfigDetailDto.class);
        detailsWrapper.eq(ErpVcrSalaryConfigDetailDto::getSalaryConfigId,dto.getSalaryConfigId());
        erpVcrSalaryConfigDetailWriteMapper.delete(detailsWrapper);
        //新增配置项目明细
        BatchUtil.batch("saveDetail",dto.getErpAsstDetails(),ErpVcrSalaryConfigDetailWriteMapper.class);
    }

    /**
     * 生成配置项
     * @param dto
     */
    @Override
    public void toCfgTempReduce(ErpVcrSalaryConfigDto dto) {
        //插入配置项
        save(dto);

        //插入配置项明细
        ErpVcrSalaryConfigDetailDto detail = dto.getDetails().get(0);
        List<ErpVcrSalaryConfigDetailDto> details = new ArrayList<>();
        //财务-借
        ErpVcrSalaryConfigDetailDto cwCR = new ErpVcrSalaryConfigDetailDto();
        cwCR.setSalaryConfigId(dto.getId());
        cwCR.setActigSys(MedConst.TYPE_1);
        cwCR.setActigAmtType(MedConst.TYPE_1);
        //临时扣款不使用借方，任意设置
        cwCR.setActigSubCode("1001");
        cwCR.setActigSubName("库存现金");
        cwCR.setCashFlowCode("01");
        cwCR.setCashFlowName("日常活动产生的现金流量");
        details.add(cwCR);
        //财务-贷
        details.add(detail);
        //预算-借
        ErpVcrSalaryConfigDetailDto ysCR = new ErpVcrSalaryConfigDetailDto();
        ysCR.setSalaryConfigId(dto.getId());
        ysCR.setActigSys(MedConst.TYPE_2);
        ysCR.setActigAmtType(MedConst.TYPE_1);
        details.add(ysCR);
        //预算-贷
        detail.setSalaryConfigId(dto.getId());
        ErpVcrSalaryConfigDetailDto ysDR = new ErpVcrSalaryConfigDetailDto();
        ysDR.setSalaryConfigId(dto.getId());
        ysDR.setActigSys(MedConst.TYPE_2);
        ysDR.setActigAmtType(MedConst.TYPE_2);
        details.add(ysDR);
        //新增配置项目明细
        BatchUtil.batch("saveDetail",details,ErpVcrSalaryConfigDetailWriteMapper.class);
    }

    @Override
    public void toUpdTempReduce(ErpVcrSalaryConfigDto dto) {
        //获取配置项明细
        ErpVcrSalaryConfigDetailDto detailDto = dto.getDetails().get(0);
        //设置salaryConfigId
        detailDto.setSalaryConfigId(dto.getId());
        LambdaUpdateWrapper<ErpVcrSalaryConfigDetailDto> detailUpdWrapper = Wrappers.lambdaUpdate(ErpVcrSalaryConfigDetailDto.class);
        detailUpdWrapper.eq(ErpVcrSalaryConfigDetailDto::getSalaryConfigId,dto.getId())
                .eq(ErpVcrSalaryConfigDetailDto::getActigSys,MedConst.TYPE_1)
                .eq(ErpVcrSalaryConfigDetailDto::getActigAmtType,MedConst.TYPE_2);
        erpVcrSalaryConfigDetailWriteMapper.update(detailDto,detailUpdWrapper);
    }
}
