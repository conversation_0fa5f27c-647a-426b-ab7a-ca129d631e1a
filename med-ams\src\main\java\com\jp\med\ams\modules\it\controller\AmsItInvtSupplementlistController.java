package com.jp.med.ams.modules.it.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItInvtSupplementlistDto;
import com.jp.med.ams.modules.it.service.read.AmsItInvtSupplementlistReadService;
import com.jp.med.ams.modules.it.service.write.AmsItInvtSupplementlistWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;


/**

 * <AUTHOR>
 * @email -
 * @date 2023-12-14 17:42:02
 */
@Api(value = "", tags = "")
@RestController
@RequestMapping("amsItInvtSupplementlist")
public class AmsItInvtSupplementlistController {

    @Autowired
    private AmsItInvtSupplementlistReadService amsItInvtSupplementlistReadService;

    @Autowired
    private AmsItInvtSupplementlistWriteService amsItInvtSupplementlistWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItInvtSupplementlistDto dto){
        return CommonResult.paging(amsItInvtSupplementlistReadService.querySupplement(dto));
    }
    /**
     * 列表
     */
    @ApiOperation("查询")
    @PostMapping("/queryRecords")
    public CommonResult<?> queryRecords(@RequestBody AmsItInvtSupplementlistDto dto){
        return CommonResult.paging(amsItInvtSupplementlistReadService.queryDetials(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody List<AmsItInvtSupplementlistDto> dtos){
        amsItInvtSupplementlistWriteService.saveSpullements(dtos);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItInvtSupplementlistDto dto){
        amsItInvtSupplementlistWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItInvtSupplementlistDto dto){
        amsItInvtSupplementlistWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("批量更新考核附件")
    @PutMapping("/updateFiles")
    public CommonResult<?> updateAssessFiles(AmsItInvtSupplementlistDto dto) {
        amsItInvtSupplementlistWriteService.updateAssessFiles(dto);
        return CommonResult.success();
    }
}
