package com.jp.med.ams.modules.changes.service.read;

import com.jp.med.ams.modules.changes.dto.AmsAssetBorrowingStatsDto;
import com.jp.med.ams.modules.changes.vo.AmsAssetBorrowingDashboardVo;

import java.util.List;

/**
 * 资产借用仪表盘服务接口
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
public interface AmsAssetBorrowingDashboardService {

    /**
     * 获取仪表盘完整数据
     *
     * @param dto 查询条件
     * @return 仪表盘数据
     */
    AmsAssetBorrowingDashboardVo getDashboardData(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取统计概览
     *
     * @param dto 查询条件
     * @return 统计概览
     */
    AmsAssetBorrowingDashboardVo.StatsOverview getStatsOverview(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取我的申请统计
     *
     * @param dto 查询条件
     * @return 我的申请统计
     */
    AmsAssetBorrowingDashboardVo.MyApplicationStats getMyApplicationStats(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取待审核统计
     *
     * @param dto 查询条件
     * @return 待审核统计
     */
    AmsAssetBorrowingDashboardVo.PendingAuditStats getPendingAuditStats(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取我的借用统计
     *
     * @param dto 查询条件
     * @return 我的借用统计
     */
    AmsAssetBorrowingDashboardVo.MyBorrowingStats getMyBorrowingStats(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取快捷操作数据
     *
     * @param dto 查询条件
     * @return 快捷操作数据
     */
    AmsAssetBorrowingDashboardVo.QuickActions getQuickActions(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取最近活动列表
     *
     * @param dto 查询条件
     * @return 最近活动列表
     */
    List<AmsAssetBorrowingDashboardVo.RecentActivity> getRecentActivities(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取过期提醒列表
     *
     * @param dto 查询条件
     * @return 过期提醒列表
     */
    List<AmsAssetBorrowingDashboardVo.ExpiryReminder> getExpiryReminders(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取热门资产统计
     *
     * @param dto 查询条件
     * @return 热门资产列表
     */
    List<AmsAssetBorrowingDashboardVo.PopularAsset> getPopularAssets(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取科室借用排行
     *
     * @param dto 查询条件
     * @return 科室借用排行
     */
    List<AmsAssetBorrowingDashboardVo.DeptBorrowingRank> getDeptBorrowingRanks(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取趋势分析数据
     *
     * @param dto 查询条件
     * @return 趋势分析数据
     */
    AmsAssetBorrowingDashboardVo.TrendAnalysis getTrendAnalysis(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取资产可用性统计
     *
     * @param dto 查询条件
     * @return 可用资产数量
     */
    Integer getAvailableAssetsCount(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取用户借用历史统计
     *
     * @param dto 查询条件
     * @return 用户历史统计
     */
    AmsAssetBorrowingDashboardVo.MyApplicationStats getUserBorrowingHistory(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取实时通知数据
     *
     * @param dto 查询条件
     * @return 通知数据
     */
    List<AmsAssetBorrowingDashboardVo.ExpiryReminder> getRealTimeNotifications(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取资产利用率统计
     *
     * @param dto 查询条件
     * @return 资产利用率数据
     */
    List<AmsAssetBorrowingDashboardVo.PopularAsset> getAssetUtilizationStats(AmsAssetBorrowingStatsDto dto);

    /**
     * 获取审核效率统计
     *
     * @param dto 查询条件
     * @return 审核效率数据
     */
    AmsAssetBorrowingDashboardVo.PendingAuditStats getAuditEfficiencyStats(AmsAssetBorrowingStatsDto dto);
}
