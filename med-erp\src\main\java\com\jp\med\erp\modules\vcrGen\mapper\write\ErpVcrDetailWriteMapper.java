package com.jp.med.erp.modules.vcrGen.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.dto.ecs.EcsReimFileRecordDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrDetailDto;
import com.jp.med.erp.modules.vcrGen.entity.Certificate;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;
import java.util.List;

/**
 * 凭证信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Mapper
public interface ErpVcrDetailWriteMapper extends BaseMapper<ErpVcrDetailDto> {

    /** 插入凭证申请信息 */
    void saveVcrApplyMsg(Certificate dto);

    /**
     * 删除凭证申请信息
     * @param ids
     */
    void delVcrApplyMsg(List<String> ids);

    /**
     * 插入辅助项目信息
     * @param dto
     */
    void insertReimAsst(ErpReimAsstDto dto);

    /**
     * 删除辅助项目信息  by  reimDetailid
     * @param dto
     */
    void deleteVcrReimAsst(Certificate dto);

    /**
     * 删除辅助项目信息 by id
     * @param id
     */
    void deleteVcrReimAsstById(Serializable id);

    /**
     * 更新凭证辅助项目金额
     * @param dto
     */
    void updateVcrAsstAmt(Certificate dto);

    /**
     * 更新折旧任务
     * @param dto
     */
    void updateDeprTask(EcsReimDeprTaskDto dto);

    /**
     * 更新资产折旧扎帐状态
     * @param dto
     */
    @Update("update ams_monthly_depreciation_posting set posting_status = '1' where posting_date = #{launchDate,jdbcType=VARCHAR}")
    void updateMonthlyDepr(EcsReimDeprTaskDto dto);

    @Insert("insert into ecs_reim_file_record (att,att_name,type,att_code) values (#{att,jdbcType=VARCHAR},#{attName,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{attCode,jdbcType=VARCHAR})")
    void insertFileRecord(EcsReimFileRecordDto dto);

    void deleteAsstByVpzh(@Param("vpzhs")List<String> vpzhs);

    void updateVcrPZstatus(@Param("ids")List<Integer> ids,@Param("status") String status);
}
