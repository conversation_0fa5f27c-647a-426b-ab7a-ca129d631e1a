package com.jp.med.ams.modules.amsPropertyInAndOut.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资产入库二维码配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
@Data
public class AmsInQrcodeConfigVo {

    /**
     * $column.comments
     */
    private Integer id;

    /**
     * 名称
     */
    private String qrCodeName;

    /**
     * 描述
     */
    private String description;

    /**
     * 有效开始时间
     */
    private Date startDate;

    /**
     * 有效结束时间
     */
    private Date endDate;

    /**
     * 二维码图片base64编码格式
     */
    private String qrCodeBase64;

    /**
     * 创建人
     */
    private String createUser;
    private String createUserName;

    private String qrId;

    private String hospitalId;
    private String inDeptName;
    private String inDept;

    private Boolean stop;

    private List<Date> validTime;

    @SuppressWarnings("unused")
    public List<Date> getValidTime() {
        validTime = List.of(startDate, endDate);
        return validTime;
    }
}
