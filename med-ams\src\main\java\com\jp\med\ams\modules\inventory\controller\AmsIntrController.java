package com.jp.med.ams.modules.inventory.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.inventory.dto.AmsIntrDto;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrReadService;
import com.jp.med.ams.modules.inventory.service.write.AmsIntrWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Map;


/**
 * 资产盘点表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Api(value = "资产盘点表", tags = "资产盘点表")
@RestController
@RequestMapping("amsIntr")
public class AmsIntrController {

    @Autowired
    private AmsIntrReadService amsIntrReadService;

    @Autowired
    private AmsIntrWriteService amsIntrWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产盘点表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsIntrDto dto){
        return CommonResult.paging(amsIntrReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产盘点表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsIntrDto dto){
        amsIntrWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产盘点表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsIntrDto dto){
        amsIntrWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产盘点表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsIntrDto dto){
        amsIntrWriteService.removeById(dto);
        return CommonResult.success();
    }


}
