package com.jp.med.erp.modules.vcrGen.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrPreviewDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpVcrPreviewVo;

import java.util.List;

/**
 * 凭证预览映射表
 * <AUTHOR>
 * @email -
 * @date 2025-03-11 17:00:33
 */
public interface ErpVcrPreviewReadService extends IService<ErpVcrPreviewDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrPreviewVo> queryList(ErpVcrPreviewDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<ErpVcrPreviewVo> queryPageList(ErpVcrPreviewDto dto);
}

