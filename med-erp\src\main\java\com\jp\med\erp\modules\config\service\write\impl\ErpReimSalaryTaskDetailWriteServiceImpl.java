package com.jp.med.erp.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.erp.modules.config.mapper.write.ErpReimSalaryTaskDetailWriteMapper;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.service.write.ErpReimSalaryTaskDetailWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应发工资报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Service
@Transactional(readOnly = false)
public class ErpReimSalaryTaskDetailWriteServiceImpl extends ServiceImpl<ErpReimSalaryTaskDetailWriteMapper, ErpReimSalaryTaskDetailDto> implements ErpReimSalaryTaskDetailWriteService {
}
