package com.jp.med.ams.modules.changes.strategy.impl;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.strategy.AbstractChangeRecordProcessor;
import com.jp.med.ams.modules.changes.strategy.ProcessContext;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 批量报废处理器
 * 处理批量报废业务逻辑
 * 优先级：1（最高）
 */
@Component
@Order(1)
public class ScrapBatchProcessor extends AbstractChangeRecordProcessor {

    @Override
    public boolean supports(AmsChgRcdDto dto) {
        return isBatchProcessing(dto) && "5".equals(dto.getRedcWay());
    }

    @Override
    protected AmsPropertyDto createPropertyUpdate(AmsChgRcdDto dto, String faCode, ProcessContext context) {
        // 批量报废时需要直接更新资产状态
        AmsPropertyDto propertyUpdate = new AmsPropertyDto();
        propertyUpdate.setFaCode(faCode);
        propertyUpdate.setIsCanc("1"); // 设置为已注销
        propertyUpdate.setRedcWay("5"); // 设置减少方式为报废
        return propertyUpdate;
    }
}
