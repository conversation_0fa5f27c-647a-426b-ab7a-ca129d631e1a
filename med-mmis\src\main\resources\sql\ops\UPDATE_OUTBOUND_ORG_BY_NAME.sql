-- =================================================================================================
-- 出库申请表组织ID更新脚本
-- 功能：根据out_target_org_id去hrm_org匹配org_name，选择层级更深的组织ID更新回out_target_org_id
-- 作者：数据更新
-- 创建时间：2025年7月16日
-- =================================================================================================

BEGIN;

-- =================================================================================================
-- 第一步：数据检查 🔍
-- =================================================================================================

-- 检查更新前的数据状态
SELECT 
    '更新前数据检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_target_org_id,
    COUNT(CASE WHEN out_taget_org IS NOT NULL AND TRIM(out_taget_org) != '' THEN 1 END) as has_taget_org,
    COUNT(DISTINCT out_target_org_id) as unique_target_org_ids
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy';

-- 显示当前的组织数据样例
SELECT 
    '更新前数据样例' as sample_type,
    docment_num,
    out_target_org_id,
    out_taget_org,
    bill_date
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' 
  AND out_target_org_id IS NOT NULL
ORDER BY docment_num
LIMIT 10;

-- =================================================================================================
-- 第二步：组织匹配分析 🔗
-- =================================================================================================

-- 分析当前out_target_org_id在hrm_org中的匹配情况
SELECT 
    '组织匹配分析' as analysis_type,
    oa.out_target_org_id as current_org_id,
    COUNT(ho.org_id) as matching_orgs_count,
    STRING_AGG(ho.org_name, ', ') as matching_org_names,
    STRING_AGG(ho.org_id, ', ') as matching_org_ids
FROM mmis_outbound_apply oa
LEFT JOIN hrm_org ho ON ho.org_name = oa.out_target_org_id 
    AND ho.hospital_id = 'zjxrmyy' 
    AND ho.active_flag = '1'
WHERE oa.hospital_id = 'zjxrmyy' 
  AND oa.out_target_org_id IS NOT NULL
GROUP BY oa.out_target_org_id
ORDER BY matching_orgs_count DESC;

-- =================================================================================================
-- 第三步：数据备份 💾
-- =================================================================================================

-- 添加备份字段（如果不存在）
ALTER TABLE mmis_outbound_apply 
ADD COLUMN IF NOT EXISTS out_target_org_id_before_update varchar(255);

-- 备份当前的out_target_org_id数据
UPDATE mmis_outbound_apply 
SET out_target_org_id_before_update = out_target_org_id
WHERE hospital_id = 'zjxrmyy' 
  AND out_target_org_id IS NOT NULL;

-- =================================================================================================
-- 第四步：创建临时表存储最佳匹配结果 📊
-- =================================================================================================

-- 创建临时表存储每个组织名称对应的最佳组织ID
CREATE TEMP TABLE temp_best_org_mapping AS
WITH org_matches AS (
    -- 找到所有匹配的组织
    SELECT DISTINCT
        oa.out_target_org_id as org_name_to_match,
        ho.org_id,
        ho.org_name,
        ho.org_parent_id,
        -- 计算org_parent_id的长度作为层级深度指标
        CASE 
            WHEN ho.org_parent_id IS NULL THEN 0
            ELSE LENGTH(ho.org_parent_id)
        END as parent_id_length,
        -- 计算层级深度（通过递归查询父级）
        (
            WITH RECURSIVE org_depth AS (
                SELECT org_id, org_parent_id, 0 as depth
                FROM hrm_org 
                WHERE org_id = ho.org_id
                
                UNION ALL
                
                SELECT p.org_id, p.org_parent_id, od.depth + 1
                FROM hrm_org p
                INNER JOIN org_depth od ON p.org_id = od.org_parent_id
                WHERE od.depth < 10  -- 防止无限递归
            )
            SELECT MAX(depth) FROM org_depth
        ) as org_depth
    FROM mmis_outbound_apply oa
    INNER JOIN hrm_org ho ON ho.org_name = oa.out_target_org_id
        AND ho.hospital_id = 'zjxrmyy' 
        AND ho.active_flag = '1'
    WHERE oa.hospital_id = 'zjxrmyy' 
      AND oa.out_target_org_id IS NOT NULL
),
ranked_matches AS (
    -- 为每个组织名称的匹配结果排序
    SELECT 
        org_name_to_match,
        org_id,
        org_name,
        org_parent_id,
        parent_id_length,
        org_depth,
        -- 优先选择parent_id_length更长的（层级更深），然后选择org_depth更大的
        ROW_NUMBER() OVER (
            PARTITION BY org_name_to_match 
            ORDER BY parent_id_length DESC, org_depth DESC, org_id
        ) as rank_num
    FROM org_matches
)
SELECT 
    org_name_to_match,
    org_id as best_org_id,
    org_name as best_org_name,
    org_parent_id,
    parent_id_length,
    org_depth
FROM ranked_matches 
WHERE rank_num = 1;

-- 显示最佳匹配结果
SELECT 
    '最佳匹配结果' as result_type,
    org_name_to_match,
    best_org_id,
    best_org_name,
    parent_id_length,
    org_depth,
    CASE 
        WHEN parent_id_length > 0 THEN '✅ 子级组织'
        ELSE '⚠️ 顶级组织'
    END as org_level_type
FROM temp_best_org_mapping
ORDER BY parent_id_length DESC, org_depth DESC;

-- =================================================================================================
-- 第五步：更新组织ID 🔄
-- =================================================================================================

-- 根据最佳匹配结果更新out_target_org_id
UPDATE mmis_outbound_apply oa
SET out_target_org_id = tbom.best_org_id
FROM temp_best_org_mapping tbom
WHERE oa.hospital_id = 'zjxrmyy'
  AND oa.out_target_org_id = tbom.org_name_to_match;

-- =================================================================================================
-- 第六步：验证更新结果 ✅
-- =================================================================================================

-- 检查更新后的数据状态
SELECT 
    '更新后数据检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_target_org_id,
    COUNT(CASE WHEN out_target_org_id != out_target_org_id_before_update THEN 1 END) as updated_records
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy';

-- 显示更新后的数据样例
SELECT 
    '更新后数据样例' as sample_type,
    docment_num,
    out_target_org_id_before_update as old_org_id,
    out_target_org_id as new_org_id,
    out_taget_org,
    ho.org_name as verified_org_name,
    CASE 
        WHEN out_target_org_id != out_target_org_id_before_update THEN '✅ 已更新'
        ELSE '📝 未变更'
    END as update_status
FROM mmis_outbound_apply oa
LEFT JOIN hrm_org ho ON oa.out_target_org_id = ho.org_id 
    AND ho.hospital_id = 'zjxrmyy' 
    AND ho.active_flag = '1'
WHERE oa.hospital_id = 'zjxrmyy' 
  AND oa.out_target_org_id_before_update IS NOT NULL
ORDER BY docment_num
LIMIT 10;

-- 统计更新结果
SELECT 
    '更新结果统计' as result_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id != out_target_org_id_before_update THEN 1 END) as successfully_updated,
    COUNT(CASE WHEN out_target_org_id = out_target_org_id_before_update THEN 1 END) as unchanged_records,
    ROUND(
        COUNT(CASE WHEN out_target_org_id != out_target_org_id_before_update THEN 1 END) * 100.0 / COUNT(*), 2
    ) as update_success_rate
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' 
  AND out_target_org_id_before_update IS NOT NULL;

-- 验证更新后的组织ID有效性
SELECT 
    '组织ID有效性验证' as validation_type,
    oa.out_target_org_id,
    ho.org_name as verified_org_name,
    ho.org_parent_id,
    COUNT(*) as record_count,
    CASE 
        WHEN ho.org_id IS NOT NULL THEN '✅ 有效组织ID'
        ELSE '❌ 无效组织ID'
    END as validity_status
FROM mmis_outbound_apply oa
LEFT JOIN hrm_org ho ON oa.out_target_org_id = ho.org_id 
    AND ho.hospital_id = 'zjxrmyy' 
    AND ho.active_flag = '1'
WHERE oa.hospital_id = 'zjxrmyy' 
  AND oa.out_target_org_id IS NOT NULL
GROUP BY oa.out_target_org_id, ho.org_name, ho.org_parent_id
ORDER BY record_count DESC;

-- =================================================================================================
-- 第七步：清理临时数据 🧹
-- =================================================================================================

-- 删除临时表
DROP TABLE IF EXISTS temp_best_org_mapping;

-- =================================================================================================
-- 第八步：最终总结 📊
-- =================================================================================================

-- 最终总结
SELECT 
    '🎯 更新总结' as summary_type,
    '组织ID更新完成' as operation,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id != out_target_org_id_before_update THEN 1 END) as updated_records,
    COUNT(CASE WHEN out_target_org_id = out_target_org_id_before_update THEN 1 END) as unchanged_records,
    CASE 
        WHEN COUNT(CASE WHEN out_target_org_id != out_target_org_id_before_update THEN 1 END) > 0
        THEN '✅ 更新成功'
        ELSE '📝 无需更新'
    END as update_status,
    ROUND(
        COUNT(CASE WHEN out_target_org_id != out_target_org_id_before_update THEN 1 END) * 100.0 / COUNT(*), 2
    ) as update_percentage
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' 
  AND out_target_org_id_before_update IS NOT NULL;

-- =================================================================================================
-- 事务控制 ⚠️
-- =================================================================================================

-- 提交事务（如果一切正常）
COMMIT;

-- 如果需要回滚，使用以下命令：
-- ROLLBACK;
