package com.jp.med.ams.modules.config.mapper.write;

import com.jp.med.ams.modules.config.dto.AmsStockCfgDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资产卡片配置
 * <AUTHOR>
 * @email -
 * @date 2023-10-10 15:21:09
 */
@Mapper
public interface AmsStockCfgWriteMapper extends BaseMapper<AmsStockCfgDto> {
    /**
     * 通过编号删除数据
     * @param amsStockCfgDto
     * @return
     */
    int deleteByCode(AmsStockCfgDto amsStockCfgDto);
}
