<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.vcrGen.mapper.read.ErpReimPayReceiptReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo" id="reimPayReceiptMap">
        <result property="id" column="id"/>
        <result property="reimDetailId" column="reim_detail_id"/>
        <result property="reimAsstId" column="reim_asst_id"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="payDate" column="pay_date"/>
        <result property="payAmt" column="pay_amt"/>
        <result property="status" column="status"/>
        <result property="crter" column="crter"/>
        <result property="crterTime" column="crter_time"/>
        <result property="upder" column="upder"/>
        <result property="upderTime" column="upder_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo">
        select
            a.id as id,
            a.reim_detail_id as reimDetailId,
            a.reim_asst_id as reimAsstId,
            a.att as att,
            a.att_name as attName,
            a.pay_date as payDate,
            a.pay_amt as payAmt,
            a.status as status,
            a.crter as crter,
            a.crter_time as crterTime,
            a.upder as upder,
            a.upder_time as upderTime
        from erp_reim_pay_receipt a
        where
            a.sup_type = #{supType,jdbcType=VARCHAR}
        <choose>
            <when test="reimDetailIds != null and reimDetailIds.size() != 0">
                and a.reim_detail_id in
                <foreach collection="reimDetailIds" item="reimDetailId" open="(" separator="," close=")">
                    #{reimDetailId,jdbcType=INTEGER}
                </foreach>
            </when>
            <otherwise>
                and a.reim_detail_id = #{reimDetailId,jdbcType=INTEGER}
            </otherwise>
        </choose>
        order by a.id
    </select>

    <select id="queryListById" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo">
        select
            a.id as id,
            a.reim_detail_id as reimDetailId,
            a.reim_asst_id as reimAsstId,
            a.att as att,
            a.att_name as attName,
            a.pay_date as payDate,
            a.pay_amt as payAmt,
            a.status as status,
            a.crter as crter,
            a.crter_time as crterTime,
            a.upder as upder,
            a.upder_time as upderTime
        from erp_reim_pay_receipt a
        where
            a.sup_type = #{supType,jdbcType=VARCHAR}
            and a.reim_detail_id = #{id,jdbcType=INTEGER}
    </select>
</mapper>
