package com.jp.med.ams.modules.changes.strategy.impl;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.strategy.AbstractChangeRecordProcessor;
import com.jp.med.ams.modules.changes.strategy.ProcessContext;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 普通变更处理器
 * 处理非批量的普通变更业务逻辑
 * 优先级：999（最低，作为默认处理器）
 */
@Component
@Order(999)
public class NormalChangeProcessor extends AbstractChangeRecordProcessor {

    @Override
    public boolean supports(AmsChgRcdDto dto) {
        // 支持所有非拆分的普通变更
        return !"8".equals(dto.getRedcWay());
    }

    @Override
    protected AmsPropertyDto createPropertyUpdate(AmsChgRcdDto dto, String faCode, ProcessContext context) {
        // 普通变更不需要立即更新资产状态，通过审批流程处理
        return null;
    }
}
