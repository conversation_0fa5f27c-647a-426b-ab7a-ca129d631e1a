package com.jp.med.ams.modules.changes.strategy;

import com.jp.med.ams.modules.property.service.read.impl.AmsPropertyReadServiceImpl;
import com.jp.med.ams.modules.property.service.write.impl.AmsPropertyWriteServiceImpl;

/**
 * 处理上下文
 * 包含处理过程中需要的服务和参数
 */
public class ProcessContext {
    private final int maxChgCode;
    private final String formattedDateTime;
    private final AmsPropertyReadServiceImpl amsPropertyReadService;
    private final AmsPropertyWriteServiceImpl amsPropertyWriteService;
    
    public ProcessContext(int maxChgCode, String formattedDateTime, 
                         AmsPropertyReadServiceImpl amsPropertyReadService,
                         AmsPropertyWriteServiceImpl amsPropertyWriteService) {
        this.maxChgCode = maxChgCode;
        this.formattedDateTime = formattedDateTime;
        this.amsPropertyReadService = amsPropertyReadService;
        this.amsPropertyWriteService = amsPropertyWriteService;
    }
    
    public int getMaxChgCode() {
        return maxChgCode;
    }
    
    public String getFormattedDateTime() {
        return formattedDateTime;
    }
    
    public AmsPropertyReadServiceImpl getAmsPropertyReadService() {
        return amsPropertyReadService;
    }
    
    public AmsPropertyWriteServiceImpl getAmsPropertyWriteService() {
        return amsPropertyWriteService;
    }
}
