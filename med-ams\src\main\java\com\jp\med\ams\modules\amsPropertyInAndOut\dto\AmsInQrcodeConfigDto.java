package com.jp.med.ams.modules.amsPropertyInAndOut.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资产入库二维码配置
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
@Data
@TableName("ams_in_qrcode_config" )
public class AmsInQrcodeConfigDto extends CommonQueryDto {


    @TableId("id")
    private Integer id;

    /** 名称 */
    @TableField("qr_code_name")
    private String qrCodeName;

    /** 入库科室*/
    @TableField("in_dept")
    private String inDept;
    @TableField(exist = false)
    private String inDeptName;

    /** 描述 */
    @TableField("description")
    private String description;

    /** 有效开始时间 */
    @TableField("start_date")
    private Date startDate;

    /** 有效结束时间 */
    @TableField("end_date")
    private Date endDate;

    /** 二维码图片base64编码格式 */
    @TableField("qr_code_base64")
    private String qrCodeBase64;

    /** 二维码code */
    @TableField("qr_id")
    private String qrId;

    /** 创建人 */
    @TableField("create_user")
    private String createUser;

    /**停止录入*/
    @TableField("stop")
    private Boolean stop;

    @TableField(exist = false)
    private String createUserName;

    /** 前端前缀 */
    @TableField(exist = false)
    private String urlPreFix;


    /** 有效时间*/
    @TableField(exist = false)
    private List<Date> validTime;

    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("医疗机构编码")
    private String hospitalId;
}
