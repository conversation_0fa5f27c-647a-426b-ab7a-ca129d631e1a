package com.jp.med.ams.modules.it.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItInvtAddDto;
import com.jp.med.ams.modules.it.service.read.AmsItInvtAddReadService;
import com.jp.med.ams.modules.it.service.write.AmsItInvtAddWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 耗材入库日志
 *
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Api(value = " 耗材入库日志", tags = " 耗材入库日志")
@RestController
@RequestMapping("amsItInvtAdd")
public class AmsItInvtAddController {

    @Autowired
    private AmsItInvtAddReadService amsItInvtAddReadService;

    @Autowired
    private AmsItInvtAddWriteService amsItInvtAddWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询 耗材入库日志")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItInvtAddDto dto) {
        return CommonResult.paging(amsItInvtAddReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增 耗材入库日志")
    @PostMapping("/save")
    public CommonResult<?> save(AmsItInvtAddDto dto) {
        amsItInvtAddWriteService.saveProduct(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改 耗材入库日志")
    @PutMapping("/update")
    public CommonResult<?> update(AmsItInvtAddDto dto) {
        amsItInvtAddWriteService.updateInvtById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除 耗材入库日志")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItInvtAddDto dto) {
        amsItInvtAddWriteService.removeInvtById(dto);
        return CommonResult.success();
    }

}
