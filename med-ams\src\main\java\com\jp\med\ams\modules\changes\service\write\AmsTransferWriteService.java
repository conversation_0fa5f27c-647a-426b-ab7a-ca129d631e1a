package com.jp.med.ams.modules.changes.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.changes.dto.AmsTransferDto;

/**
 * 资产转移
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
public interface AmsTransferWriteService extends IService<AmsTransferDto> {

    /**
     * 写入审核表
     *
     * @param dto
     */
    void saveTransfer(AmsTransferDto dto);

    void removeTransfer(AmsTransferDto dto);
}

