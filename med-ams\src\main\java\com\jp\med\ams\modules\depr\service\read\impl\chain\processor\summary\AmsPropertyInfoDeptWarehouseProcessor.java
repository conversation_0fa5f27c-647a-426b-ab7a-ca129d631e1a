package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * 信息科库房资产折旧处理器
 * <p>
 * 负责处理信息科库房资产的特殊折旧逻辑：
 * 1. 信息科库房资产（isInfoDeptWarehouse=1）且type=1
 * 2. 没有使用科室（deptUse为空）的信息科库房资产不折旧
 * 3. 但需要计入原值统计
 * 4. 有使用科室的信息科库房资产正常折旧
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Component
public class AmsPropertyInfoDeptWarehouseProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    /**
     * 信息科库房资产标识
     */
    private static final String INFO_DEPT_WAREHOUSE_FLAG = "1";

    /**
     * 检查资产是否为信息科库房资产（静态方法，供其他类使用）
     *
     * @param asset 资产对象
     * @return true表示是信息科库房资产，false表示不是
     */
    public static boolean isInfoDeptWarehouseAssetStatic(AmsPropertyVo asset) {
        return asset != null
                && INFO_DEPT_WAREHOUSE_FLAG.equals(asset.getIsInfoDeptWarehouse())
                && "1".equals(asset.getType());
    }

    /**
     * 获取信息科库房资产的原值总和（不包括折旧）
     *
     * @param assets 资产列表
     * @return 信息科库房资产的原值总和
     */
    public static BigDecimal getInfoDeptWarehouseOriginalValueSum(List<AmsPropertyVo> assets) {
        if (assets == null || assets.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return assets.stream()
                .filter(AmsPropertyInfoDeptWarehouseProcessor::isInfoDeptWarehouseAssetStatic)
                .filter(asset -> asset.getDeptUse() == null || asset.getDeptUse().trim().isEmpty())
                .map(AmsPropertyVo::getAssetNav)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理信息科库房资产折旧逻辑");

        // 处理普通资产中的信息科库房资产
        processInfoDeptWarehouseAssets(context.getNormalAssets());

        // 处理房屋维修资产中的信息科库房资产（如果有的话）
        processInfoDeptWarehouseAssets(context.getHouseRepairAssets());

        // 处理已生成的折旧结果中的信息科库房资产
        processInfoDeptWarehouseDeprResults(context.getResult());

        log.debug("信息科库房资产折旧处理完成");
    }

    /**
     * 处理资产列表中的信息科库房资产
     *
     * @param assets 资产列表
     */
    private void processInfoDeptWarehouseAssets(List<AmsPropertyVo> assets) {
        if (assets == null || assets.isEmpty()) {
            return;
        }

        Iterator<AmsPropertyVo> iterator = assets.iterator();
        while (iterator.hasNext()) {
            AmsPropertyVo asset = iterator.next();

            // 检查是否为信息科库房资产
            if (isInfoDeptWarehouseAsset(asset)) {
                // 检查是否有使用科室
                if (isEmptyDeptUse(asset.getDeptUse())) {
                    // 没有使用科室的信息科库房资产：设置月折旧额为0，但保留原值
                    asset.setDeprMon(BigDecimal.ZERO);
                    asset.setDep(BigDecimal.ZERO); // 累计折旧也为0

                    log.debug("信息科库房资产 {} 无使用科室，设置月折旧额为0，原值保留: {}",
                            asset.getFaCode(), asset.getAssetNav());
                } else {
                    // 有使用科室的信息科库房资产：正常折旧
                    log.debug("信息科库房资产 {} 有使用科室 {}，正常折旧",
                            asset.getFaCode(), asset.getDeptUse());
                }
            }
        }
    }

    /**
     * 处理折旧结果中的信息科库房资产
     *
     * @param deprResults 折旧结果列表
     */
    private void processInfoDeptWarehouseDeprResults(List<AmsPropertyDepr2Vo> deprResults) {
        if (deprResults == null || deprResults.isEmpty()) {
            return;
        }

        for (AmsPropertyDepr2Vo deprResult : deprResults) {
            // 检查折旧结果中是否包含信息科库房资产
            if (containsInfoDeptWarehouseAssets(deprResult)) {
                // 根据是否有使用科室来调整折旧计算
                adjustInfoDeptWarehouseDepreciation(deprResult);
            }
        }
    }

    /**
     * 检查是否为信息科库房资产
     *
     * @param asset 资产对象
     * @return true表示是信息科库房资产，false表示不是
     */
    private boolean isInfoDeptWarehouseAsset(AmsPropertyVo asset) {
        return asset != null
                && INFO_DEPT_WAREHOUSE_FLAG.equals(asset.getIsInfoDeptWarehouse())
                && "1".equals(asset.getType());
    }

    /**
     * 检查使用科室是否为空
     *
     * @param deptUse 使用科室
     * @return true表示为空，false表示不为空
     */
    private boolean isEmptyDeptUse(String deptUse) {
        return deptUse == null || deptUse.trim().isEmpty();
    }

    /**
     * 检查折旧结果是否包含信息科库房资产
     *
     * @param deprResult 折旧结果
     * @return true表示包含，false表示不包含
     */
    private boolean containsInfoDeptWarehouseAssets(AmsPropertyDepr2Vo deprResult) {
        // 这里可以通过资产代码列表来检查，或者通过其他标识
        // 由于折旧结果中没有直接的信息科库房标识，我们需要通过其他方式判断
        // 暂时返回false，具体实现需要根据业务需求调整
        return false;
    }

    /**
     * 调整信息科库房资产的折旧计算
     *
     * @param deprResult 折旧结果
     */
    private void adjustInfoDeptWarehouseDepreciation(AmsPropertyDepr2Vo deprResult) {
        // 如果确认包含无使用科室的信息科库房资产，需要调整折旧额
        // 具体实现需要根据业务逻辑来确定如何识别和调整

        log.debug("调整信息科库房资产折旧计算: 科室={}, 资产类型={}",
                deprResult.getDeptUseCode(), deprResult.getAssetTypeCode());

        // 示例：如果科室代码为空且包含信息科库房资产，则只保留原值，折旧额设为0
        if (isEmptyDeptUse(deprResult.getDeptUseCode())) {
            // 保留原值（monthDeprAmt），但将折旧相关金额设为0
            if (deprResult.getDeprAmt() != null) {
                deprResult.setDeprAmt(BigDecimal.ZERO);
            }
            if (deprResult.getDeprAmtSum() != null) {
                deprResult.setDeprAmtSum(BigDecimal.ZERO);
            }

            log.debug("信息科库房资产无使用科室，调整折旧额为0，保留原值: {}",
                    deprResult.getMonthDeprAmt());
        }
    }
}
