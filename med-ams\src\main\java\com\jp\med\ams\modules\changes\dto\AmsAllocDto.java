package com.jp.med.ams.modules.changes.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.ams.modules.changes.entity.AmsTransferChkEntity;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;

import java.util.List;

/**
 * 资产划拨
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
@Data
@TableName("ams_alloc")
public class AmsAllocDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 审核批次号
     */
    @TableField("bchno")
    private String bchno;

    /**
     * 资产表id
     */
    @TableField("fa_code")
    private String faCode;

    /**
     * 转出科室
     */
    @TableField("traf_out_dept")
    private String trafOutDept;

    /**
     * 转入管理科室代码
     */
    @TableField("traf_in_dept_manage")
    private String trafInDeptManage;

    /**
     * 转入存放位置代码
     */
    @TableField("traf_in_storage_area")
    private String trafInStorageArea;

    /**
     * 转入保存地点代码
     */
    @TableField("traf_in_storage_location")
    private String trafInStorageLocation;

    /**
     * 转入使用科室
     */
    @TableField("traf_in_dept")
    private String trafInDept;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 接收时间
     */
    @TableField("rec_time")
    private String recTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 接收备注
     */
    @TableField("rec_remarks")
    private String recRemarks;

    /**
     * 业务状态
     * statusOptions: ref<Option[]>([
     * { label: '审核中', value: '1' },
     * { label: '确认中', value: '2' },
     * { label: '申请成功', value: '3' },
     * { label: '申请失败', value: '4' },
     * ]),
     */
    @TableField("prosstas")
    private String prosstas;

    /**
     * 医疗机构id
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 有效标准
     */
    @TableField("active_flag")
    private String activeFlag;

    /*
     * 转移资产类型 1 固定资产 8 低值资产 @see amsPropertyDto
     */
    private String type = "1";

    /**
     * 审核流程详情
     */
    @TableField(exist = false)
    private List<AuditDetail> auditDetails;

    /**
     * 审核表id
     */
    @TableField(exist = false)
    private List<Long> ids;

    /**
     * 状态
     */
    @TableField(exist = false)
    private String status;

    /**
     * 查询科室字段
     */
    @TableField(exist = false)
    private String deptField;

    /**
     * 查询科室
     */
    @TableField(exist = false)
    private String deptCode;

    /**
     * 最多审核人数
     */
    @TableField(exist = false)
    private Integer curChkSeq;

    /**
     * 需要更新的审核信息
     */
    @TableField(exist = false)
    private AmsTransferChkEntity chk;

    /**
     * 转出科室名称
     */
    @TableField(exist = false)
    private String trafOutDeptName;

    /**
     * 转入科室名称
     */
    @TableField(exist = false)
    private String trafInDeptName;

    /**
     * 资产名称
     */
    @TableField(exist = false)
    private String faCodeName;

    /**
     * 资产faCodeList
     */
    @TableField(exist = false)
    private List<String> faCodes;

    /**
     * 转入管理科室名称
     */
    @TableField(exist = false)
    private String trafInDeptManageName;

    /**
     * 转入存放位置名称
     */
    @TableField(exist = false)
    private String trafInStorageAreaName;

    /**
     * 转入保存地点名称
     */
    @TableField(exist = false)
    private String trafInstorageLocationName;

    /**
     * 不查询已通过的
     */
    @TableField(exist = false)
    private boolean notQueryPassed;

}
