package com.jp.med.erp.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Data
public class ErpReimSalaryTaskVo {

	/** id */
	private Integer id;

	/** 工资任务发放月份 */
	private String ffMth;

	/** 工资条数 */
	private Integer num;

	/** 应发合计汇总 */
	private BigDecimal shouldPay;

	/** 扣款合计汇总 */
	private BigDecimal reducePay;

	/** 实发合计汇总 */
	private BigDecimal realPay;

	/** 备注 */
	private String remark;

	/** 制表人 */
	private String crter;

	/** 制表时间 */
	private String crteTime;

	/** 是否报销 0:未报销 1:已报销 */
	private String reimFlag;

	/** 工资任务id */
	private Integer salaryId;

	/** 报销id */
	private Integer reimId;

	/** 工资类型 */
	private String type;

}
