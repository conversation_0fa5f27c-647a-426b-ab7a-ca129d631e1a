package com.jp.med.erp.modules.vcrGen.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpReimAsstDto {

    private Integer id;

    /** 报销详情id */
    private Integer reimDetailId;

    /** 支付类型代码 */
    private String payTypeCode;

    /** 支付类型名称 */
    private String payTypeName;

    /** 会计科目代码 */
    @ExcelProperty("会计科目代码")
    private String actigSubCode;

    /** 会计科目名称 */
    @ExcelProperty("会计科目名称")
    private String actigSubName;

    /** 科室代码 */
    @ExcelProperty("科室代码")
    private String deptCode;

    /** 科室名称 */
    @ExcelProperty("科室名称")
    private String deptName;

    /** 往来单位代码 */
    @ExcelProperty("往来单位代码")
    private String relCoCode;

    /** 往来单位名称 */
    @ExcelProperty("往来单位名称")
    private String relCoName;

    /** 功能科目代码 */
    @ExcelProperty("功能科目代码")
    private String funSubCode;

    /** 功能科目名称 */
    @ExcelProperty("功能科目名称")
    private String funSubName;

    /** 经济科目代码 */
    @ExcelProperty("经济科目代码")
    private String econSubCode;

    /** 经济科目名称 */
    @ExcelProperty("经济科目名称")
    private String econSubName;

    /** 项目代码 */
    @ExcelProperty("项目代码")
    private String projCode;

    /** 项目名称 */
    @ExcelProperty("项目名称")
    private String projName;

    /** 现金流量代码 */
    @ExcelProperty("现金流量代码")
    private String cashFlowCode;

    /** 现金流量名称 */
    @ExcelProperty("现金流量名称")
    private String cashFlowName;

    /** 金额类型 */
    private String actigAmtType;

    /** 金额 */
    @ExcelProperty("金额")
    private BigDecimal actigAmt;

    /** 创建人 */
    private String crter;

    /** 创建时间 */
    private String createTime;

    /** 医疗机构id */
    private String hospitalId;

    /** 会计体系 */
    private String actigSys;

    /** 报销科室编码 */
    private String reimDeptCode;

    /** 报销科室 */
    private String reimDeptName;

    /** 摘要 */
    @ExcelProperty("摘要")
    private String abst;

    /** 报销项序号 **/
    private Integer asstNo;

    /** 上级类型 **/
    private String supType;

    /** 资金性质 **/
    private String fundType;

    /** 资金性质名称 **/
    private String fundTypeName;

    /** hrp凭证号 **/
    private String vpzh;
}
