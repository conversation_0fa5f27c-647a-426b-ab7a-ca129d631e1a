package com.jp.med.ams.modules.changes.strategy.borrowing;

import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.strategy.borrowing.context.AmsBorrowingAuditContext;

/**
 * 资产借用审核策略接口
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
public interface AmsBorrowingAuditStrategy {

    /**
     * 执行审核策略
     *
     * @param context 审核上下文，包含审核人信息、借用记录等
     * @param dto     审核数据传输对象
     * @return 审核结果，true表示成功，false表示失败
     */
    boolean executeAudit(AmsBorrowingAuditContext context, AmsChngBrwgDto dto);

    /**
     * 获取策略类型
     *
     * @return 策略类型标识
     */
    String getStrategyType();

    /**
     * 验证审核参数
     *
     * @param context 审核上下文
     * @param dto     审核数据传输对象
     * @return 验证结果，true表示参数有效
     */
    boolean validateAuditParams(AmsBorrowingAuditContext context, AmsChngBrwgDto dto);
}
