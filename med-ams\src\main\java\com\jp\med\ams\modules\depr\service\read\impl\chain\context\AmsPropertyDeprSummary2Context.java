package com.jp.med.ams.modules.depr.service.read.impl.chain.context;

import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 资产折旧汇总2处理上下文
 * 专门用于 queryDeprSummary2 方法的责任链处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
public class AmsPropertyDeprSummary2Context {

    // ========== 输入参数 ==========
    /** 查询条件DTO */
    private AmsPropertyDeprDto queryDto;

    /** 当前月份 */
    private String currentYm;

    /** 上个月份 */
    private String lastMonthYm;

    /** 是否使用新资产类型代码 */
    private boolean useTypeNCode;

    // ========== 月度数据 ==========
    /** 本月折旧汇总数据 */
    private List<AmsPropertyDepr2Vo> currentMonthPropertyDepr2Vos;

    /** 上月折旧汇总数据 */
    private List<AmsPropertyDepr2Vo> lastMonthPropertyDepr2Vos;

    // ========== 数据映射 ==========
    /** 本月数据映射 (合并Key -> 折旧数据) */
    private Map<String, AmsPropertyDepr2Vo> currentMonthMap;

    /** 上月数据映射 (合并Key -> 折旧数据) */
    private Map<String, AmsPropertyDepr2Vo> lastMonthMap;

    // ========== 配置数据 ==========
    /** 资金来源配置列表 */
    private List<AmsBasicCfgVo> sourceList;

    /** 资金来源代码映射 (code -> name) */
    private Map<String, String> sourceCodeMap;

    /** 财政补助资金代码 */
    private String finaSubsidyCode;

    // ========== 处理结果 ==========
    /** 最终处理结果 */
    private List<AmsPropertyDepr2Vo> result;

    // ========== 构造方法 ==========
    public AmsPropertyDeprSummary2Context(AmsPropertyDeprDto queryDto) {
        this.queryDto = queryDto;
        this.currentYm = queryDto.getYm();
        this.useTypeNCode = queryDto.getTypen();
    }

    // ========== 便利方法 ==========

    /**
     * 获取科室代码
     */
    public String getDeptCode() {
        return queryDto.getDeptCode();
    }

    /**
     * 获取资产类型代码
     */
    public String getAssetTypeCode() {
        return queryDto.getAssetTypeCode();
    }
}
