package com.jp.med.ams.modules.changes.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.changes.dto.AmsTransferDto;
import com.jp.med.ams.modules.changes.mapper.read.AmsTransferReadMapper;
import com.jp.med.ams.modules.changes.service.read.AmsTransferReadService;
import com.jp.med.ams.modules.changes.vo.AmsPropertyInfo;
import com.jp.med.ams.modules.changes.vo.AmsTransferVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Transactional(readOnly = true)
@Service
public class AmsTransferReadServiceImpl extends
        ServiceImpl<AmsTransferReadMapper, AmsTransferDto> implements AmsTransferReadService {

    @Autowired
    private AmsTransferReadMapper amsTransferReadMapper;

    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    @Override
    public List<AmsTransferVo> queryList(AmsTransferDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 1. 查询转移记录列表
        List<AmsTransferVo> queryList = amsTransferReadMapper.queryList(dto);

        if (queryList.isEmpty()) {
            return queryList;
        }

        // 2. 批量收集所有transferId
        List<Long> transferIds = queryList.stream()
                .map(AmsTransferVo::getId)
                .collect(Collectors.toList());

        // 3. 批量查询所有transfer对应的faCode
        Map<Long, List<String>> transferFaCodeMap = batchQueryTransferFaCodes(transferIds);

        // 4. 收集所有唯一的faCode
        Set<String> allFaCodes = transferFaCodeMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        // 5. 批量查询所有资产信息
        Map<String, AmsPropertyVo> assetMap = batchQueryAssets(new ArrayList<>(allFaCodes));

        // 6. 在内存中组装数据
        queryList.forEach(item -> {
            List<String> faCodes = transferFaCodeMap.getOrDefault(item.getId(), Collections.emptyList());
            List<AmsPropertyVo> propertyDetails = faCodes.stream()
                    .map(assetMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 设置资产名称字符串
            String assetNames = propertyDetails.stream()
                    .map(asset -> asset.getAssetName() + "(" + asset.getFaCode() + ")")
                    .collect(Collectors.joining(","));

            // 设置资产信息列表
            List<AmsPropertyInfo> propertyInfos = propertyDetails.stream()
                    .map(asset -> {
                        AmsPropertyInfo amsPropertyInfo = new AmsPropertyInfo();
                        amsPropertyInfo.setAssetName(asset.getAssetName());
                        amsPropertyInfo.setFaCode(asset.getFaCode());
                        return amsPropertyInfo;
                    }).collect(Collectors.toList());

            item.setPropertyInfos(propertyInfos);
            item.setAssetNames(assetNames);
            item.setFaCodes(faCodes);
        });

        return queryList;
    }

    @Override
    public List<AmsPropertyVo> queryPropertyDetail(AmsTransferDto dto) {
        List<AmsPropertyDto> amsPropertyDtos = amsTransferReadMapper.selectTransferPropertyRefFacode(dto);
        if (!amsPropertyDtos.isEmpty()) {
            List<String> collect = amsPropertyDtos.stream().map(AmsPropertyDto::getFaCode).collect(Collectors.toList());
            AmsPropertyDto amsPropertyDto = new AmsPropertyDto();
            amsPropertyDto.setFaCodes(collect);
            return amsPropertyReadMapper.queryMainList(amsPropertyDto);
        }
        return List.of();
    }

    @Override
    public Integer queryAuditCount(AmsTransferDto dto) {
        return amsTransferReadMapper.queryAuditCount(dto);
    }

    /**
     * 批量查询转移记录对应的资产代码
     *
     * @param transferIds 转移记录ID列表
     * @return 转移记录ID -> 资产代码列表的映射
     */
    private Map<Long, List<String>> batchQueryTransferFaCodes(List<Long> transferIds) {
        if (transferIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 一次性查询所有transfer对应的faCode
        List<AmsPropertyDto> allTransferRefs = amsTransferReadMapper.batchSelectTransferPropertyRefFacode(transferIds);

        // 按transferId分组
        return allTransferRefs.stream()
                .collect(Collectors.groupingBy(
                        ref -> ref.getId().longValue(), // 转换Integer为Long
                        Collectors.mapping(AmsPropertyDto::getFaCode, Collectors.toList())
                ));
    }

    /**
     * 批量查询资产信息
     *
     * @param faCodes 资产代码列表
     * @return 资产代码 -> 资产信息的映射
     */
    private Map<String, AmsPropertyVo> batchQueryAssets(List<String> faCodes) {
        if (faCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        AmsPropertyDto queryDto = new AmsPropertyDto();
        queryDto.setFaCodes(faCodes);
        List<AmsPropertyVo> assets = amsPropertyReadMapper.queryMainList(queryDto);

        return assets.stream()
                .collect(Collectors.toMap(
                        AmsPropertyVo::getFaCode,
                        asset -> asset,
                        (existing, replacement) -> existing // 处理重复key的情况
                ));
    }

}
