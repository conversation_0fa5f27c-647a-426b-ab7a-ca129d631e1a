package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.property.dto.AmsPropertyMonthlySnapshotDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyMonthlySnapshotReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 房屋维修资产查询处理器
 * 负责查询房屋维修资产数据
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyHouseRepairQueryProcessor
        extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Autowired
    private AmsPropertyMonthlySnapshotReadMapper amsPropertyMonthlySnapshotReadMapper;

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始查询房屋维修资产");

        // 构建房屋维修资产查询条件
        AmsPropertyMonthlySnapshotDto houseRepairQuery = new AmsPropertyMonthlySnapshotDto();
        houseRepairQuery.setType("1");
        houseRepairQuery.setIsCanc("0");
        houseRepairQuery.setIsChk("1");
        houseRepairQuery.setDeptUse("1000");
        houseRepairQuery.setMonthlySnapshot(context.getYm());

        // 根据是否使用新类型设置资产类型条件
        if (context.isUseNewType()) {
            houseRepairQuery.setAssetTypeN(context.getAssetTypeCode());
        } else {
            houseRepairQuery.setAssetType(context.getAssetTypeCode());
        }

        // 执行查询
        List<AmsPropertyVo> houseRepairAssets = amsPropertyMonthlySnapshotReadMapper.queryList(houseRepairQuery);
        context.setHouseRepairAssets(houseRepairAssets);

        log.debug("房屋维修资产查询完成，共查询到 {} 条记录", houseRepairAssets.size());
    }
}
