package com.jp.med.ams.modules.changes.mapper.read;

import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 资产借用信息表
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 16:01:14
 */
@Mapper
public interface AmsChngBrwgReadMapper extends BaseMapper<AmsChngBrwgDto> {

    /**
     * 查询列表
     * @param dto
     * @return∂z
    */
    List<AmsChngBrwgVo> queryList(AmsChngBrwgDto dto);

    Integer queryWarnNum(AmsChngBrwgDto dto);


    /**
     * 通过单据查询借用的设备
     * @param dto
     * @return
     */
    List<AmsChngBrwgVo> queryById(AmsChngBrwgDto dto);

    /**
     * 查询汇总情况
     * @param dto
     * @return
     */
    AmsChngBrwgVo queryCount(AmsChngBrwgDto dto);


}
