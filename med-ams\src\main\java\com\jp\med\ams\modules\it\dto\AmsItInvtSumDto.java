package com.jp.med.ams.modules.it.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 耗材库存汇总
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
@TableName("ams_it_invt_sum" )
public class AmsItInvtSumDto extends CommonQueryDto {


    @TableId("id")
    private Integer id;

    /** 名称 */
    @TableField("name")
    private String name;

    /** 耗材名称 */
    @TableField("name2")
    private String name2;

    /** 耗材型号 */
    @TableField("model2")
    private String model2;

    /** 价格 */
    @TableField("price")
    private BigDecimal price;

    /** 物品类型(0:设备,1:耗材)*/
    @TableField("type")
    private String type;

    /** 供应商 */
    @TableField("provider")
    private String provider;

    /** 品牌 */
    @TableField("brand")
    private String brand;

    /** 生产厂家 */
    @TableField("factory")
    private String factory;

    /** 型号 */
    @TableField("model")
    private String model;

    /** 库存数量 */
    @TableField("num")
    private Integer num;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 更新人 */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

    /** 删除人 */
    @TableField("delter")
    private String delter;

    /** 删除时间 */
    @TableField("delete_time")
    private String deleteTime;

    /** 有效标志(1:删除) */
    @TableField("active_flag")
    private String activeFlag;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 出库数量 */
    @TableField(exist = false)
    private Integer outNum;

    /**
     * 更新时间范围-起时间
     */
    @TableField(exist = false)
    private String updateStartTime;

    /**
     * 更新时间范围-结束时间
     */
    @TableField(exist = false)
    private String updateEndTime;

    /**
     * 耗材汇总-查询截止时间
     */
    @TableField(exist = false)
    private String endDate;

    /**
     * 耗材补货状态 0: 库存充足 1: 需要补货 2: 未填写阈值信息 3: 从未入过库
     */
    @TableField(exist = false)
    private Integer stockStatus;

    /**生成耗材要货单选中的id集合*/
    @TableField(exist = false)
    @ExcelIgnore
    private Integer[] ids;
}
