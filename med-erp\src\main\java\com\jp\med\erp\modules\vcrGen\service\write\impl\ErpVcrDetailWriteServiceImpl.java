package com.jp.med.erp.modules.vcrGen.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDetailDto;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.dto.ecs.EcsReimFileRecordDto;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDto;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.common.dto.emp.HrmOrgAgencyMapDto;
import com.jp.med.common.dto.emp.HrmOrgDto;
import com.jp.med.common.dto.erp.ErpPropertyDeprDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.EmpEmployeeFeignService;
import com.jp.med.common.feign.HrmOrgFeignService;
import com.jp.med.common.feign.HrmOrgMapFeignService;
import com.jp.med.common.feign.ams.AmsDeprDeptReadServiceFeignApi;
import com.jp.med.common.feign.ecs.EcsReimFeignService;
import com.jp.med.common.feign.hrm.HrmEmployeeSalaryFeignService;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.common.vo.*;
import com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;
import com.jp.med.common.vo.ecs.drug.EcsStoinVo;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDto;
import com.jp.med.erp.modules.config.mapper.read.ErpReimSalaryTaskDetailReadMapper;
import com.jp.med.erp.modules.config.mapper.read.ErpReimSalaryTaskReadMapper;
import com.jp.med.erp.modules.config.mapper.read.ErpVcrSalaryConfigDetailReadMapper;
import com.jp.med.erp.modules.config.mapper.read.ErpVcrSalaryConfigReadMapper;
import com.jp.med.erp.modules.config.mapper.write.ErpReimSalaryTaskWriteMapper;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.*;
import com.jp.med.erp.modules.vcrGen.entity.*;
import com.jp.med.erp.modules.vcrGen.enums.FeeNameEnum;
import com.jp.med.erp.modules.vcrGen.enums.ShareRelCoEnum;
import com.jp.med.erp.modules.vcrGen.enums.ShareTypeEnum;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpReimPayReceiptReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrItemDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrPreviewReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.write.ErpVcrDetailWriteMapper;
import com.jp.med.erp.modules.vcrGen.mapper.write.ErpVcrItemDetailWriteMapper;
import com.jp.med.erp.modules.vcrGen.mapper.write.ErpVcrPreviewWriteMapper;
import com.jp.med.erp.modules.vcrGen.service.write.ErpVcrDetailWriteService;
import com.jp.med.erp.modules.vcrGen.vo.*;
import io.seata.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 凭证信息
 *
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Service
@Transactional(readOnly = false)
@Slf4j
public class ErpVcrDetailWriteServiceImpl extends ServiceImpl<ErpVcrDetailWriteMapper, ErpVcrDetailDto> implements ErpVcrDetailWriteService {

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    @Autowired
    private ErpVcrDetailWriteMapper erpVcrDetailWriteMapper;

    @Autowired
    private ErpVcrItemDetailWriteMapper erpVcrItemDetailWriteMapper;

    @Autowired
    private HrmOrgMapFeignService hrmOrgMapFeignService;

    @Autowired
    private ErpVcrSalaryConfigReadMapper erpVcrSalaryConfigReadMapper;

    @Autowired
    private ErpVcrSalaryConfigDetailReadMapper erpVcrSalaryConfigDetailReadMapper;

    @Autowired
    private ErpReimPayReceiptReadMapper erpReimPayReceiptReadMapper;

    @Autowired
    private ErpReimSalaryTaskReadMapper erpReimSalaryTaskReadMapper;

    @Autowired
    private ErpReimSalaryTaskDetailReadMapper erpReimSalaryTaskDetailReadMapper;

    @Autowired
    private HrmOrgFeignService hrmOrgFeignService;

    @Autowired
    private EmpEmployeeFeignService empEmployeeFeignServic;

    @Autowired
    private EcsReimFeignService ecsReimFeignService;

    @Autowired
    private ErpReimSalaryTaskWriteMapper erpReimSalaryTaskWriteMapper;

    @Autowired
    private AmsDeprDeptReadServiceFeignApi amsDeprDeptReadServiceFeignApi;

    @Autowired
    private HrmEmployeeSalaryFeignService hrmEmployeeSalaryFeignService;

    @Autowired
    private ErpVcrPreviewWriteMapper erpVcrPreviewWriteMapper;

    @Autowired
    private ErpVcrItemDetailReadMapper erpVcrItemDetailReadMapper;

    @Autowired
    private ErpVcrPreviewReadMapper erpVcrPreviewReadMapper;

    // 凭证生成地址
    @Value("${urls.mid.vcr}")
    private String vcrGenUrl;

    @Value("${urls.mid.vcr-del}")
    private String vcrDelUrl;

    @Value("${urls.mid.recogn}")
    private String recognUrl;
    //阿里通用文字识别地址
//    private static final String recognUrl = "http://127.0.0.1:9529/recogGnr/identify";

    @Override
    public void generateVcr(Certificate dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new AppException("请选择报销项目");
        }

        //获取报销项对应辅助项目信息
        List<CertificateDetail> certificateDetails;

        if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_2)) {     //药品
            certificateDetails = erpVcrDetailReadMapper.queryDrugAsstList(dto);
            if (CollectionUtils.isEmpty(certificateDetails)) {
                throw new AppException("报销项明细不存在!");
            }
        } else {
            if(StringUtils.equals(dto.getType(),MedConst.TYPE_8) || StringUtils.equals(dto.getType(),MedConst.TYPE_10)) {
                certificateDetails = erpVcrDetailReadMapper.queryDrugAsstList(dto);
                if (CollectionUtils.isEmpty(certificateDetails)) {
                    throw new AppException("报销项明细不存在!");
                }
            } else {
                certificateDetails = erpVcrDetailReadMapper.queryReimAsstList(dto);
                if (CollectionUtils.isEmpty(certificateDetails)) {
                    throw new AppException("报销项明细不存在!");
                }

                //确认选择的报销都生成了报销明细
                Set<String> pzids = certificateDetails.stream().map(item -> item.getPzid()).collect(Collectors.toSet());
                Set<String> reimIds = dto.getIds().stream().map(item -> String.valueOf(item)).collect(Collectors.toSet());
                if (!pzids.containsAll(reimIds)) {
                    throw new AppException("存在未生成报销明细的报销，请重新生成!");
                }
            }

        }

        //辅助项目信息中的部门需对应为用友中的部门
        CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());

        if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
            throw new AppException("当前未维护科室关系映射!");
        }
        List<HrmOrgAgencyMapVo> orgAgencyMapVos = result.getData();
        certificateDetails.stream().forEach(item -> {
            //所有的日期都设置为凭证日期
            item.setRq(dto.getCertificateDate());
            if (!StringUtils.equals(dto.getType(), MedConst.TYPE_4) && StringUtils.isNotEmpty(item.getKsdm())) {
                //分摊费用不需要转换部门代码
                Optional<HrmOrgAgencyMapVo> first = orgAgencyMapVos.stream().filter(orgMap -> StringUtils.equals(orgMap.getHrpOrgCode(), item.getKsdm())).findFirst();
                if (first.isEmpty()) {
                    log.error("当前未维护科室关系映射,请维护!,部门{}", item.getKsdm());
                    throw new AppException("存在未维护科室关系的部门!");
                }
                item.setKsdm(first.get().getYyOrgCode());
                item.setKsmc(first.get().getYyOrgName());
            }
        });

        //校验辅助项合理性
        checkValid(certificateDetails);

        dto.setCertificateDetail(certificateDetails);

        log.info("----------凭证生成请求信息---------", dto);

        /*if (true) {
            throw new AppException("测试异常回退");
        }*/

        ErpVcrDetailDto vcrDto = yyVcr(dto);

        log.info("-------返回信息-------", JSON.toJSON(vcrDto));
        //插入凭证申请信息
        try {
            dto.setIdpzh(vcrDto.getIdpzh());
            dto.setCreateTime(DateUtil.getCurrentTime(null));
            //更新报销是否生成凭证的状态
            erpVcrDetailWriteMapper.updateVcrPZstatus(dto.getIds(),MedConst.TYPE_1);
            erpVcrDetailWriteMapper.saveVcrApplyMsg(dto);
            //插入凭证信息
            erpVcrDetailReadMapper.insert(vcrDto);
            //插入凭证明细信息
            BatchUtil.batch("insertVcrItemDetail", vcrDto.getDetail(), ErpVcrItemDetailWriteMapper.class);
        } catch(Exception e) {
            //如果插入失败，删除第三方生成的数据
            List<String> idpzhs = Arrays.asList(vcrDto.getIdpzh());
            yyVcrDel(idpzhs);
            log.error("凭证生成失败",e);
            throw new AppException("凭证生成失败");
        }
    }

    @Override
    public String saveDrugVcrAssts(ErpVcrDetailDto dto) {

        //查询药品报销的固定项目
        CommonResult<List<EcsReimFixedAsstVo>> result = ecsReimFeignService.list(new EcsReimFixedAsstDto());
        if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
            log.error("未查询到药品报销固定项目配置，药品报销id:{}",dto.getIds());
            throw new AppException("未查询到药品报销固定项目配置");
        }
        List<EcsReimFixedAsstVo> fixedAsstVos = result.getData();
        //查询药品报销
        EcsDrugReimDetaiDto drugReimParam = new EcsDrugReimDetaiDto();
        drugReimParam.setIds(dto.getIds());
        CommonResult<List<EcsDrugReimDetaiVo>> drugReimResult = ecsReimFeignService.listNoPage(drugReimParam);
        if (Objects.isNull(drugReimResult) || CollectionUtil.isEmpty(drugReimResult.getData())) {
            log.error("未查询到药品报销数据，药品报销id:{}",dto.getIds().toString());
            throw new AppException("未查询到药品报销数据");
        }
        List<EcsDrugReimDetaiVo> drugReims = drugReimResult.getData();

        //查询供应商-往来单位映射数据
        /*//中药
        List<EcsReimFixedAsstVo> drugCfg1 = fixedAsstVos.stream()
                .filter(item -> StringUtils.equals(item.getFixedSubCode(), ErpConstants.PAY_TYPECODE_DRUG)
                        && StringUtils.equals(item.getSubType(), MedConst.TYPE_1))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(drugCfg1) || CollectionUtil.isEmpty(drugCfg1.get(0).getFixedAsstDetails())) {
            log.error("药品报销中药库固定科目未配置");
            throw new AppException("药品报销中药库固定科目未配置");
        }
        //西药
        List<EcsReimFixedAsstVo> drugCfg2 = fixedAsstVos.stream()
                .filter(item -> StringUtils.equals(item.getFixedSubCode(), ErpConstants.PAY_TYPECODE_DRUG)
                        && StringUtils.equals(item.getSubType(), MedConst.TYPE_2))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(drugCfg2) || CollectionUtil.isEmpty(drugCfg2.get(0).getFixedAsstDetails())) {
            log.error("药品报销西药库固定科目未配置");
            throw new AppException("药品报销西药库固定科目未配置");
        }
        //消毒
        List<EcsReimFixedAsstVo> drugCfg3 = fixedAsstVos.stream()
                .filter(item -> StringUtils.equals(item.getFixedSubCode(), ErpConstants.PAY_TYPECODE_DRUG)
                        && StringUtils.equals(item.getSubType(), MedConst.TYPE_3))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(drugCfg3) || CollectionUtil.isEmpty(drugCfg3.get(0).getFixedAsstDetails())) {
            log.error("药品报销消毒用品库固定科目未配置");
            throw new AppException("药品报销消毒用品库固定科目未配置");
        }*/
        //查询报销对应的入库单数据
        EcsStoinDto stoinParam = new EcsStoinDto();
        stoinParam.setDrugReimDetailIds(dto.getIds().stream().map(Integer::longValue).collect(Collectors.toList()));
        CommonResult<List<EcsStoinVo>> stoinResult = ecsReimFeignService.listNoPage(stoinParam);
        if (Objects.isNull(stoinResult) || CollectionUtil.isEmpty(stoinResult.getData())) {
            log.error("当前药品报销对应入库单数据为空，药品报销id:{}", dto.getIds().toString());
            throw new AppException("当前药品报销对应入库单数据为空");
        }
        List<EcsStoinVo> stoinTotalDatas = stoinResult.getData();


        //先删除预览凭证记录和辅助项目
        //删除旧辅助项目
        ErpVcrDetailDto drugParam = new ErpVcrDetailDto();
        drugParam.setIds(dto.getIds());
        drugParam.setSupType(MedConst.TYPE_2);  //药品supType 为2
        drugParam.setType(MedConst.TYPE_1);     //药品type 默认为1
        delDrugVpzhMsg(drugParam);

        //生成药品报销的预览凭证记录  对应一个凭证
        List<ErpVcrPreviewDto> previews = new ArrayList<>();
        String hrppzh = ULIDUtil.generate();
        for (int i = 0; i < dto.getIds().size(); i++) {
            ErpVcrPreviewDto previewDto = new ErpVcrPreviewDto();
            previewDto.setPayType(MedConst.TYPE_1);
            previewDto.setModuleId(dto.getIds().get(i).intValue());
            previewDto.setSupType(MedConst.TYPE_2);
            previewDto.setPushFlag(MedConst.TYPE_0);
            previewDto.setVpzh(hrppzh);
            previewDto.setCrter(dto.getSysUser().getUsername());
            previewDto.setCreateTime(DateUtil.getCurrentTime(null));
            previews.add(previewDto);
        }
        BatchUtil.batch(previews, ErpVcrPreviewWriteMapper.class);

        int asstNo = 1;
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        //生成财务借  财务-借不 不区分库房，任取类型库房即可
        for (int i = 0; i < drugReims.size(); i++) {
            EcsDrugReimDetaiVo drugReim = drugReims.get(i);

            Optional<EcsReimFixedAsstVo> zyOpt = fixedAsstVos.stream().filter(item -> StringUtils.equals(item.getFixedSubCode(),ErpConstants.PAY_TYPECODE_DRUG)
//                    && StringUtils.equals(item.getSubType(), MedConst.TYPE_1)
                    && StringUtils.equals(item.getAbst(), drugReim.getSpler())).findFirst();
            if (zyOpt.isEmpty()) {
                log.error("当前供应商{}-项目未配置",drugReim.getSpler());
                throw new AppException(String.format("当前供应商%s,项目未配置",drugReim.getSpler()));
            }
            EcsReimFixedAsstVo zy = zyOpt.get();
            List<EcsReimFixedAsstDetailVo> fixedAsstDetails = zy.getFixedAsstDetails();
            if (CollectionUtil.isEmpty(fixedAsstDetails)) {
                log.error("不存在当前供应商-{}-的项目明细配置",drugReim.getSpler());
                throw new AppException(String.format("不存在当前供应商-%s的项目明细配置",drugReim.getSpler()));
            }
            //财务借
            EcsReimFixedAsstDetailVo cwCRCfg = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            ErpReimAsstDto cr = new ErpReimAsstDto();
            BeanUtils.copyProperties(cwCRCfg,cr);
            cr.setSupType(MedConst.TYPE_2);
            cr.setVpzh(hrppzh);
            cr.setRelCoCode(cwCRCfg.getRelCoCode());
            cr.setRelCoName(cwCRCfg.getRelCoName());
            cr.setActigAmt(drugReim.getSum());
            cr.setAsstNo(asstNo++);
            cr.setAbst("付药品、卫生材料款-"+ drugReim.getSpler());
            insertAssts.add(cr);

            //财务贷，如果药品报销支付类型为现金或者复明工程，财务贷方按照报销金额生成，科目为 10010101（日常公用开支）  现金流量：010201（购买商品、接受劳务支付的现金）
            if (!StringUtils.equals(dto.getPayMethod(), MedConst.TYPE_0)) {
                EcsReimFixedAsstDetailVo cwDRCfg = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(cwDRCfg,dr);
                dr.setSupType(MedConst.TYPE_2);
                dr.setVpzh(hrppzh);
                dr.setActigSubCode("10010101");
                dr.setActigSubName("日常公用开支");
                dr.setCashFlowCode("010201");
                dr.setCashFlowName("购买商品、接受劳务支付的现金");
                dr.setActigAmt(drugReim.getSum());
                dr.setAsstNo(asstNo++);
                dr.setAbst("付药品、卫生材料款-"+drugReim.getSpler());
                insertAssts.add(dr);
            }
        }

        //默认为当前时间
        String abst = DateUtil.getCurrentTime(null).substring(0, 10) + "付" + drugReims.get(0).getIssue() + "药品、卫生材料";

        //如果药品支付类型为现金，则不按照付款单生成财务贷方，按照药品报销金额生成，在上方已经生成，否则，按照付款单生成
        if (StringUtils.equals(dto.getPayMethod(), MedConst.TYPE_0)) {
            //查询当前报销所有的付款文件识别信息
            List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getDrugReceiptInfos(dto.getSupType(), drugReims.stream()
                    .map(e -> e.getId().intValue()).collect(Collectors.toList()));
            //按照付款证明文件生成药品贷方，贷方科目不区分，任取即可
            List<EcsReimFixedAsstDetailVo> drAsstDetails = fixedAsstVos.stream().filter(item -> StringUtils.equals(item.getFixedSubCode(), ErpConstants.PAY_TYPECODE_DRUG)
                    && StringUtils.equals(item.getSubType(), MedConst.TYPE_1)).findFirst().get().getFixedAsstDetails();
            EcsReimFixedAsstDetailVo drFixedAsstDetailVo = drAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
            for (int i = 0; i < erpReimPayReceiptVos.size(); i++) {
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(drFixedAsstDetailVo,dr);
                dr.setSupType(MedConst.TYPE_2);
//            dr.setReimDetailId(drugReims.get(0).getId().intValue());   不绑定报销，绑定到凭证
                dr.setVpzh(hrppzh);
                dr.setActigAmt(erpReimPayReceiptVos.get(i).getPayAmt());
                dr.setAsstNo(asstNo++);
                dr.setAbst("付药品、卫生材料款");
                insertAssts.add(dr);
            }

            abst = erpReimPayReceiptVos.get(0).getPayDate() + "付" + drugReims.get(0).getIssue() + "药品、卫生材料";
        }


        //预算-借  中药库：1099  西药库：1094 消毒用品库 1241
//          筛选药品类的入库单（中药、西药）
//        List<String> drugType = Arrays.asList("1099", "1094");
//        List<EcsStoinVo> drugStoins = stoinTotalDatas.stream().filter(e -> drugType.contains(e.getStoinType())).collect(Collectors.toList());
        //筛选中药入库单
        List<EcsStoinVo> zStoins = stoinTotalDatas.stream().filter(e -> StringUtils.equals(e.getStoinType(), "1099")).collect(Collectors.toList());
        //筛选西药入库单
        List<EcsStoinVo> wStoins = stoinTotalDatas.stream().filter(e -> StringUtils.equals(e.getStoinType(), "1094")).collect(Collectors.toList());
        //筛选消毒用品类的入库单
        List<EcsStoinVo> mStoins = stoinTotalDatas.stream().filter(e -> StringUtils.equals(e.getStoinType(), "1241")).collect(Collectors.toList());
        //预算 辅助项目配置任取，在代码中手动配置
        Optional<EcsReimFixedAsstVo> zyOpt = fixedAsstVos.stream()
                .filter(item -> StringUtils.equals(item.getFixedSubCode(),ErpConstants.PAY_TYPECODE_DRUG)).findFirst();
        if (zyOpt.isEmpty()) {
            log.error("药品固定项目未配置");
            throw new AppException("药品固定项目未配置");
        }
        EcsReimFixedAsstVo ysAsstCfg = zyOpt.get();
        List<EcsReimFixedAsstDetailVo> ysAsstDetailCfg = ysAsstCfg.getFixedAsstDetails();
        if (CollectionUtil.isEmpty(ysAsstDetailCfg)) {
            log.error("不存在药品项目明细配置");
            throw new AppException("不存在药品项目明细配置");
        }
        EcsReimFixedAsstDetailVo ysCRCfg = ysAsstDetailCfg.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
        EcsReimFixedAsstDetailVo ysDRCfg  = ysAsstDetailCfg.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
        //摘要取付款单日期最大的
        //中药-预算借
        if (CollectionUtil.isNotEmpty(zStoins)) {
            ErpReimAsstDto ysCR = new ErpReimAsstDto();
            BeanUtils.copyProperties(ysCRCfg,ysCR);
            ysCR.setSupType(MedConst.TYPE_2);
//                ysCR.setReimDetailId(drugReim.getId().intValue());        不绑定报销，绑定到凭证
            ysCR.setVpzh(hrppzh);
            BigDecimal amt = zStoins.stream()
                    .map(e -> {
                        if (StringUtils.equals(e.getIsBack(),MedConst.TYPE_1)) {
                            //如果是退货则减去
                            return e.getRtalAmt().negate();
                        }
                        return e.getRtalAmt();})
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            ysCR.setEconSubCode("302180602");
            ysCR.setEconSubName("中草药");
            ysCR.setActigAmt(amt);
            ysCR.setAsstNo(asstNo++);
            ysCR.setAbst(abst);
            insertAssts.add(ysCR);
        }

        //西药-预算借
        if (CollectionUtil.isNotEmpty(wStoins)) {
            ErpReimAsstDto ysCR = new ErpReimAsstDto();
            BeanUtils.copyProperties(ysCRCfg,ysCR);
            ysCR.setSupType(MedConst.TYPE_2);
//                ysCR.setReimDetailId(drugReim.getId().intValue());        不绑定报销，绑定到凭证
            ysCR.setVpzh(hrppzh);
            BigDecimal amt = wStoins.stream()
                    .map(e -> {
                        if (StringUtils.equals(e.getIsBack(),MedConst.TYPE_1)) {
                            //如果是退货则减去
                            return e.getRtalAmt().negate();
                        }
                        return e.getRtalAmt();})
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            ysCR.setEconSubCode("302180601");
            ysCR.setEconSubName("西药");
            ysCR.setActigAmt(amt);
            ysCR.setAsstNo(asstNo++);
            ysCR.setAbst(abst);
            insertAssts.add(ysCR);
        }

        //消毒用品-预算借
        if (CollectionUtil.isNotEmpty(mStoins)) {
            ErpReimAsstDto ysCR1 = new ErpReimAsstDto();
            BeanUtils.copyProperties(ysCRCfg,ysCR1);
            ysCR1.setSupType(MedConst.TYPE_2);
            ysCR1.setVpzh(hrppzh);
            BigDecimal amt1 = mStoins.stream()
                    .map(e -> {
                        if (StringUtils.equals(e.getIsBack(),MedConst.TYPE_1)) {
                            //如果是退货则减去
                            return e.getRtalAmt().negate();
                        }
                        return e.getRtalAmt();})
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            ysCR1.setEconSubCode("302180305");
            ysCR1.setEconSubName("其他卫生材料");
            ysCR1.setActigAmt(amt1);
            ysCR1.setAsstNo(asstNo++);
            ysCR1.setAbst(abst);
            insertAssts.add(ysCR1);
        }

        //预算贷方，计算总入库单金额
        ErpReimAsstDto ysDR = new ErpReimAsstDto();
        BeanUtils.copyProperties(ysDRCfg,ysDR);
        ysDR.setSupType(MedConst.TYPE_2);
        ysDR.setVpzh(hrppzh);
        BigDecimal amt2 = stoinTotalDatas.stream()
                .map(e -> {
                    if (StringUtils.equals(e.getIsBack(),MedConst.TYPE_1)) {
                        //如果是退货则减去
                        return e.getRtalAmt().negate();
                    }
                    return e.getRtalAmt();})
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        ysDR.setActigAmt(amt2);
        ysDR.setAsstNo(asstNo++);
        ysDR.setAbst(abst);
        insertAssts.add(ysDR);



        //新增
        insertAssts.stream().forEach(asst -> {
            asst.setCrter(dto.getSysUser().getUsername());
            asst.setCreateTime(DateUtil.getCurrentTime(null));
            asst.setHospitalId("zjxrmyy");
        });

        log.info("--------------需要插入的辅助项{}", insertAssts.size());

        /*if (true) {
            throw new AppException("测试异常");
        }*/

        //插入
        BatchUtil.batch("insertReimAsst", insertAssts, ErpVcrDetailWriteMapper.class);

        return hrppzh;
    }

    /**
     * 删除药品报销已生成的vpzh相关数据
     * @param dto
     */
    @Override
    public void delDrugVpzhMsg(ErpVcrDetailDto dto) {
        //查询所有的vpzh
        LambdaQueryWrapper<ErpVcrPreviewDto> preWrapper = Wrappers.lambdaQuery(ErpVcrPreviewDto.class);
        preWrapper.in(ErpVcrPreviewDto::getModuleId,dto.getIds())
                .eq(ErpVcrPreviewDto::getSupType,dto.getSupType())
                .eq(ErpVcrPreviewDto::getPayType,dto.getType());
        List<ErpVcrPreviewDto> erpVcrPreviewDtos = erpVcrPreviewWriteMapper.selectList(preWrapper);
        Set<String> vpzhs = erpVcrPreviewDtos.stream().map(item -> item.getVpzh()).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(vpzhs)) {
            //删除vpzh预览凭证映射表
            LambdaUpdateWrapper<ErpVcrPreviewDto> preUpdWrapper = Wrappers.lambdaUpdate(ErpVcrPreviewDto.class);
            preUpdWrapper.in(ErpVcrPreviewDto::getVpzh,vpzhs);
            erpVcrPreviewWriteMapper.delete(preWrapper);
            //删除vpzh对应的辅助项
            erpVcrDetailWriteMapper.deleteAsstByVpzh(new ArrayList<>(vpzhs));
        }
    }

    /**
     * @param supType 类型
     * @param drugReimIds 药品报销id
     * @return
     */
    private List<ErpReimPayReceiptVo> getDrugReceiptInfos(String supType,List<Integer> drugReimIds) {
        Set<Integer> drugPayIds = new HashSet<>();
        if (StringUtils.equals(supType,MedConst.TYPE_2)) {      //药品
            List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(drugReimIds);
            //获取付款单drug_pay_id
            drugPayIds = drugVos.stream().map(ErpDrugReimDetailVo::getDrugPayId).collect(Collectors.toSet());
        } else {                                                //其他 现只有采购
            List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailList(drugReimIds);
            drugPayIds = erpReimDetailVos.stream().map(ErpReimDetailVo::getPayRcptId).collect(Collectors.toSet());
        }

        //查询药品报销文件中间表
        EcsDrugPayDetailDto param = new EcsDrugPayDetailDto();
        param.setIds(new ArrayList<>(drugPayIds));
        List<EcsDrugPayDetailVo> ecsDrugPayDetailDtos = erpVcrDetailReadMapper.queryDrugPayDetail(param);

        //已识别的付款文件信息
        LambdaQueryWrapper<ErpReimPayReceiptDto> payReceipWrapper = Wrappers.lambdaQuery(ErpReimPayReceiptDto.class);
        payReceipWrapper.eq(ErpReimPayReceiptDto::getSupType,supType)
                .in(ErpReimPayReceiptDto::getReimDetailId,drugPayIds);
        List<ErpReimPayReceiptDto> erpReimPayReceiptDtos = erpReimPayReceiptReadMapper.selectList(payReceipWrapper);

        Set<String> attCodes = ecsDrugPayDetailDtos.stream().map(EcsDrugPayDetailVo::getAttCode).collect(Collectors.toSet());
        List<FileRecordEntity> fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(new ArrayList<>(attCodes));
        List<FileRecordEntity> collect = fileRecordEntities
                .stream()
                .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2))
                .collect(Collectors.toList());
        if (erpReimPayReceiptDtos.size() != collect.size()) {
            //数量不一致
            throw new AppException("存在未识别付款证明文件的报销");
        }
        erpReimPayReceiptDtos.stream().forEach(item -> {
            if (Objects.isNull(item.getPayAmt()) || StringUtils.isEmpty(item.getPayDate())) {
                throw new AppException("存在金额或者日期为空的付款文件识别信息，请修正");
            }
        });
        return erpReimPayReceiptDtos.stream().map(e -> {
            ErpReimPayReceiptVo vo = new ErpReimPayReceiptVo();
            BeanUtils.copyProperties(e,vo);
            return vo;
        }).collect(Collectors.toList());
    }


    @Override
    public void deleteVcr(ErpVcrDetailDto dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new AppException("未选择删除凭证信息");
        }
        dto.setSqlAutowiredHospitalCondition(true);
        //通过id查询凭证信息
        List<ErpVcrDetailVo> erpVcrDetailVos = erpVcrDetailReadMapper.queryList(dto);
        //获取凭证号
        List<String> idPzhs = erpVcrDetailVos.stream().map(item -> item.getIdpzh()).collect(Collectors.toList());
        //如果是 零星采购、物资报销 需要更新报销中的hasPZ字段
        if (StringUtils.equals(erpVcrDetailVos.get(0).getSupType(),MedConst.TYPE_1)
                && (StringUtils.equals(erpVcrDetailVos.get(0).getType(), MedConst.TYPE_8)
                || StringUtils.equals(erpVcrDetailVos.get(0).getType(), MedConst.TYPE_10))) {
            LambdaQueryWrapper<ErpVcrItemDetailDto> vcrItemQueryWrapper = Wrappers.lambdaQuery(ErpVcrItemDetailDto.class);
            vcrItemQueryWrapper.in(ErpVcrItemDetailDto::getIdpzh, idPzhs);
            List<ErpVcrItemDetailDto> erpVcrItemDetailVos = erpVcrItemDetailReadMapper.selectList(vcrItemQueryWrapper);
            //获取pzid
            Set<String> pzIdSet = erpVcrItemDetailVos.stream().map(item -> item.getPzid()).collect(Collectors.toSet());
            //查询凭证映射
            LambdaQueryWrapper<ErpVcrPreviewDto> previewQueryWrapper = Wrappers.lambdaQuery(ErpVcrPreviewDto.class);
            previewQueryWrapper.in(ErpVcrPreviewDto::getVpzh, pzIdSet);
            List<ErpVcrPreviewDto> erpVcrPreviewDtos = erpVcrPreviewReadMapper.selectList(previewQueryWrapper);
            //获取报销id
            List<Integer> moduleIds = erpVcrPreviewDtos.stream().map(item -> item.getModuleId()).collect(Collectors.toList());
            //更新状态
            if (CollectionUtil.isNotEmpty(moduleIds)) {
                erpVcrDetailWriteMapper.updateVcrPZstatus(moduleIds,MedConst.TYPE_0);
            }
        }

        //删除凭证申请信息
        erpVcrDetailWriteMapper.delVcrApplyMsg(idPzhs);

        //删除本地Vcr
        LambdaQueryWrapper<ErpVcrDetailDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ErpVcrDetailDto::getIdpzh, idPzhs);
        erpVcrDetailWriteMapper.delete(queryWrapper);

        //删除本地Vcr 明细
        LambdaQueryWrapper<ErpVcrItemDetailDto> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper.in(ErpVcrItemDetailDto::getIdpzh, idPzhs);
        erpVcrItemDetailWriteMapper.delete(itemQueryWrapper);

        /*if (true) {
            throw new AppException("删除失败");
        }*/
        //删除凭证
        yyVcrDel(idPzhs);
    }

    /**
     * 处理差旅报销或者旧培训(时间在非本年度)
     */
    private List<ErpReimAsstDto> handleTravelAndOldTrainingAssts(List<ErpReimAsstDto> reimAsstDetails,Certificate dto,List<ErpReimDetailVo> erpReimDetailVos) {
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();
        //是否含有财务会计
        boolean cwActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_1, MedConst.TYPE_1);
        //是否含有预算会计
        boolean ysActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_2, MedConst.TYPE_1);
        boolean cwActigDept = false;
        boolean ysActigDept = false;
        //判断财务会计是否含有部门，有财务会计时，变量才有效
        if (cwActig) {
            cwActigDept = hasDeptAsst(dto, MedConst.TYPE_1, MedConst.TYPE_1);
        }
        //判断预算会计是否含有部门，有预算会计时，变量才有效
        if (ysActig) {
            ysActigDept = hasDeptAsst(dto, MedConst.TYPE_2, MedConst.TYPE_1);
        }

        //报销项序号
        int asstNo = 1;

        for (int i = 0; i < erpReimDetailVos.size(); i++) {
            ErpReimDetailVo reimDetailVo = erpReimDetailVos.get(i);
            //调用阿里接口，识别信息
//                List<Map<String, String>> maps = handleAlyPayInfoMaps(reimDetailVo.getId());
            //查询对应报销付款证明信息
            List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getPayReceiptInfos(dto.getSupType(), reimDetailVo.getId());
            //随行人员明细
            List<ErpReimPsnDetail> erpReimPsnDetails = erpVcrDetailReadMapper.queryPsnDetail(Arrays.asList(reimDetailVo.getId()));
            Map<String, List<ErpReimPsnDetail>> psnDetailGroup = erpReimPsnDetails.stream().
                    filter(psnItem -> psnItem.getReimDetailId().equals(reimDetailVo.getId())).collect(Collectors.groupingBy(ErpReimPsnDetail::getDept));
            //随行人员名字
            String psnNames = erpReimPsnDetails.stream()
                    .limit(8)
                    .map(psnItem -> psnItem.getTripPsnName())
                    .collect(Collectors.joining("、"));
            //摘要
            String abs = getAbs(erpReimPayReceiptVos, StringUtils.substring(reimDetailVo.getAppyerTime(), 0, 10), psnNames, FeeNameEnum.getByType(dto.getType()).getFeeName());
            if (cwActig) {
                ErpReimAsstDto cwCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                ErpReimAsstDto cwDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                //生成财务借
                if (cwActigDept) {
                    for (String key : psnDetailGroup.keySet()) {
                        List<ErpReimPsnDetail> psnDetails = psnDetailGroup.get(key);
                        BigDecimal groupSum = psnDetails.stream().map(ErpReimPsnDetail::getReimAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        ErpReimAsstDto cr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwCR, cr);
                        cr.setSupType(MedConst.TYPE_1);
                        cr.setDeptCode(psnDetails.get(0).getDept());
                        cr.setDeptName(psnDetails.get(0).getDeptName());
                        cr.setActigSubCode(changeCWActigCode(psnDetails.get(0).getDept(), cr.getActigSubCode(), dto.getType()));
                        cr.setReimDetailId(reimDetailVo.getId());
                        cr.setAsstNo(asstNo++);
                        cr.setActigAmt(groupSum);
                        cr.setAbst(abs);
                        insertAssts.add(cr);
                        //如果非冲抵借款且支付方式为现金支付或者复明工程，则没有付款证明文件
                        if (StringUtils.equals(reimDetailVo.getIsLoan(),MedConst.TYPE_0) && !StringUtils.equals(reimDetailVo.getPayMethod(),MedConst.TYPE_0)) {
                            ErpReimAsstDto dr = new ErpReimAsstDto();
                            BeanUtils.copyProperties(cwDR,dr);
                            dr.setSupType(MedConst.TYPE_1);
                            dr.setActigSubCode("10010101");
                            dr.setActigSubName("日常公用开支");
                            dr.setCashFlowCode("010201");
                            dr.setCashFlowName("购买商品、接受劳务支付的现金");
                            dr.setActigAmt(groupSum);
                            dr.setAsstNo(asstNo++);
                            dr.setReimDetailId(reimDetailVo.getId());
                            dr.setAbst(abs);
                            insertAssts.add(dr);
                        }
                    }
                } else {
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwCR, cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setActigAmt(reimDetailVo.getSum());
                    cr.setReimDetailId(reimDetailVo.getId());
                    cr.setAsstNo(asstNo++);
                    cr.setAbst(abs);
                    insertAssts.add(cr);
                    //如果非冲抵借款且支付方式为现金支付或者复明工程，则没有付款证明文件
                    if (StringUtils.equals(reimDetailVo.getIsLoan(),MedConst.TYPE_0) && !StringUtils.equals(reimDetailVo.getPayMethod(),MedConst.TYPE_0)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwDR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(reimDetailVo.getSum());
                        dr.setAsstNo(asstNo++);
                        dr.setReimDetailId(reimDetailVo.getId());
                        dr.setAbst(abs);
                        insertAssts.add(dr);
                    }
                }
                if (StringUtils.equals(reimDetailVo.getIsLoan(),MedConst.TYPE_0)) {
                    //如果非冲抵借款且支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
                    if ((StringUtils.isEmpty(reimDetailVo.getPayMethod()) || StringUtils.equals(reimDetailVo.getPayMethod(),MedConst.TYPE_0))) {
                        genCWDRAssts(insertAssts, reimDetailVo, cwDR, asstNo, psnNames, FeeNameEnum.getByType(dto.getType()).getFeeName(), erpReimPayReceiptVos);
                        asstNo+=erpReimDetailVos.size();
                    }
                } else {
                    //查询冲抵借款报销凭证
                    ErpReimAsstDto param = new ErpReimAsstDto();
                    param.setReimDetailId(reimDetailVo.getLoanReimId());
                    param.setSupType(MedConst.TYPE_1);
                    List<ErpReimAsstVo> erpReimAsstVos = erpVcrDetailReadMapper.queryReimAsstVoList(param);
                    if (CollectionUtil.isEmpty(erpReimAsstVos)) {
                        log.error("冲抵借款报销未生成凭证,借款报销id：{}",reimDetailVo.getLoanReimId());
                        throw new AppException(String.format("冲抵借款报销未生成凭证,借款报销id：%s",reimDetailVo.getLoanReimId()));
                    }
                    //获取借款报销财务借方数据，作为当前凭证的财务贷方数据
                    List<ErpReimAsstVo> loanAsstCrs = erpReimAsstVos.stream().filter(item -> StringUtils.equals(item.getActigSys(), MedConst.TYPE_1) && StringUtils.equals(item.getActigAmtType(), MedConst.TYPE_1)).collect(Collectors.toList());
                    FeeNameEnum byType = FeeNameEnum.getByType(dto.getType());
                    //修改对应借贷方和报销id
                    for (int k = 0; k < loanAsstCrs.size(); k++) {
                        ErpReimAsstVo loanReimVo = loanAsstCrs.get(k);
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(loanReimVo, dr);
//                        dr.setPayTypeCode(byType.getPayTypeCode());
//                        dr.setPayTypeName(byType.getFeeName());
                        dr.setActigSys(MedConst.TYPE_1);
                        dr.setActigAmtType(MedConst.TYPE_2);
                        dr.setActigAmt(reimDetailVo.getLoanAmt());
                        dr.setReimDetailId(reimDetailVo.getId());
                        dr.setAsstNo(asstNo++);
                        insertAssts.add(dr);
                    }
                }
            }

            if (ysActig) {
                ErpReimAsstDto ysCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                ErpReimAsstDto ysDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                if (ysActigDept) {
                    for (String key : psnDetailGroup.keySet()) {
                        List<ErpReimPsnDetail> psnDetails = psnDetailGroup.get(key);
                        BigDecimal groupSum = psnDetails.stream().map(ErpReimPsnDetail::getReimAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //预算-借贷
                        ErpReimAsstDto cr = new ErpReimAsstDto();
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(ysCR, cr);
                        BeanUtils.copyProperties(ysDR, dr);
                        cr.setSupType(MedConst.TYPE_1);
                        cr.setDeptCode(psnDetails.get(0).getDept());
                        cr.setDeptName(psnDetails.get(0).getDeptName());
                        cr.setActigSubCode(changeCWActigCode(psnDetails.get(0).getDept(), cr.getActigSubCode(), dto.getType()));
                        cr.setReimDetailId(reimDetailVo.getId());
                        cr.setAsstNo(asstNo++);
                        cr.setActigAmt(groupSum);
                        cr.setAbst(abs);
                        insertAssts.add(cr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setReimDetailId(reimDetailVo.getId());
                        dr.setAsstNo(asstNo++);
                        dr.setActigAmt(groupSum);
                        dr.setAbst(abs);
                        insertAssts.add(dr);
                    }
                } else {
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysCR, cr);
                    BeanUtils.copyProperties(ysDR, dr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setActigAmt(reimDetailVo.getSum());
                    cr.setReimDetailId(reimDetailVo.getId());
                    cr.setAsstNo(asstNo++);
                    cr.setAbst(abs);
                    insertAssts.add(cr);
                    dr.setSupType(MedConst.TYPE_1);
                    dr.setReimDetailId(reimDetailVo.getId());
                    dr.setAsstNo(asstNo++);
                    dr.setActigAmt(reimDetailVo.getSum());
                    dr.setAbst(abs);
                    insertAssts.add(dr);
                }
            }
        }
        return insertAssts;
    }

    @Override
    public String saveVcrAssts(Certificate dto) {
        log.info("------------------start生成报销assts-------------------------");
        if (CollectionUtil.isEmpty(dto.getIds())) {
            throw new AppException("请选择报销");
        }

        String resMsg = "1";
        List<ErpReimAsstDto> reimAsstDetails = dto.getReimAsstDetails();

        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_1)) {
            //查询报销明细
            List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailList(dto.getIds());
            //费用报销
            if (StringUtils.equals(dto.getType(), MedConst.TYPE_1)) {
                insertAssts.addAll(handleTravelAndOldTrainingAssts(reimAsstDetails,dto,erpReimDetailVos));
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_2)) {
                //查询固定项
                CommonResult<List<EcsReimFixedAsstVo>> result = ecsReimFeignService.list(new EcsReimFixedAsstDto());
                if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
                    throw new AppException("未查询到固定项目配置");
                }
                List<EcsReimFixedAsstVo> fixedAssts = result.getData();
                //查询差旅费辅助项目
                List<EcsReimFixedAsstVo> travelFixed = fixedAssts.stream()
                        .filter(e -> StringUtils.equals(e.getFixedSubCode(), ErpConstants.PAY_TYPECODE_TRAVEL)
                                && StringUtils.equals(e.getFundType(), MedConst.TYPE_1)).collect(Collectors.toList());
                //查询培训费辅助项目非
                List<EcsReimFixedAsstVo> trainFixed = fixedAssts.stream()
                        .filter(e -> StringUtils.equals(e.getFixedSubCode(), ErpConstants.PAY_TYPECODE_TRAINING)
                                && StringUtils.equals(e.getFundType(), MedConst.TYPE_1)).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(travelFixed) || CollectionUtil.isEmpty(trainFixed)) {
                    throw new AppException("未查询到差旅费或培训费辅助项目配置");
                }
                List<EcsReimFixedAsstDetailVo> travelFixedAsstDetails = travelFixed.get(0).getFixedAsstDetails();
                List<EcsReimFixedAsstDetailVo> trainFixedAsstDetails = trainFixed.get(0).getFixedAsstDetails();
                //循环报销
                for (int i = 0; i < erpReimDetailVos.size(); i++) {
                    ErpReimDetailVo erpReimDetailVo = erpReimDetailVos.get(i);
                    String appyerTime = erpReimDetailVo.getAppyerTime();
                    if (appyerTime == null || appyerTime.length() < 4) {
                        throw new AppException("appyerTime格式错误: 必须至少包含4位字符");
                    }
                    //判断时间
                    String year = appyerTime.substring(0, 4);
                    if (Integer.parseInt(year) <= 2024) {
                        //按照旧方式生成辅助项目
                        List<ErpReimAsstDto> trainAsstDtos = new ArrayList<>();
                        trainFixedAsstDetails.stream().forEach(e -> {
                            ErpReimAsstDto asst = new ErpReimAsstDto();
                            BeanUtils.copyProperties(e, asst);
                            trainAsstDtos.add(asst);
                        });
                        dto.setReimAsstDetails(trainAsstDtos);
                        insertAssts.addAll(handleTravelAndOldTrainingAssts(trainAsstDtos,dto,erpReimDetailVos));
                    } else {
                        //按照新方式生成辅助项目
                        //查询对应 报销付款证明 信息
                        List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getPayReceiptInfos(dto.getSupType(), erpReimDetailVo.getId());
                        //查询 随行人员
                        List<ErpReimPsnDetail> erpReimPsnDetails = erpVcrDetailReadMapper.queryPsnDetail(Arrays.asList(erpReimDetailVo.getId()));
                        Map<String, List<ErpReimPsnDetail>> psnDetailGroup = erpReimPsnDetails.stream().
                                filter(psnItem -> psnItem.getReimDetailId().equals(erpReimDetailVo.getId()))
                                .collect(Collectors.groupingBy(ErpReimPsnDetail::getDept));
                        //查询 项目信息
                        List<ErpReimItemDetail> reimItems = erpVcrDetailReadMapper.queryItemDetail(Arrays.asList(erpReimDetailVo.getId()));
                        //查询 补助项目信息
                        List<ErpReimSubsItemDetail> subsItemDetails = erpVcrDetailReadMapper.querySubItemDetail(Arrays.asList(erpReimDetailVo.getId()));
                        //获取 培训费项目
                        Stream<ErpReimItemDetail> trainItems = reimItems.stream().filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2));
                        //获取 非培训费项目
                        Stream<ErpReimItemDetail> travelItems = reimItems.stream().filter(item -> !StringUtils.equals(item.getType(), MedConst.TYPE_1));
                        //计算 项目总金额
                        BigDecimal itemsSum = reimItems.stream().map(item -> item.getAmt()).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //培训费金额, 归为培训费
                        BigDecimal trainSum = reimItems.stream()
                                .filter(item -> StringUtils.equals(item.getItem(), ErpConstants.TRAIN_ITEM_NAME))
                                .map(item -> item.getAmt()).filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //非培训费总金额
                        BigDecimal noTrainSum = itemsSum.subtract(trainSum);
                        //计算 补助项目总金额
                        BigDecimal subsSum = subsItemDetails.stream().map(item -> item.getAmt()).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //狭义培训费金额, 归为差旅费
                        BigDecimal narrowTrainSum = subsSum.add(noTrainSum);

                        //随行人员名字
                        String psnNames = erpReimPsnDetails.stream()
                                .limit(8)
                                .map(psnItem -> psnItem.getTripPsnName())
                                .collect(Collectors.joining("、"));
                        //差旅费摘要、培训费摘要
                        String trainAbs = getAbs(erpReimPayReceiptVos, StringUtils.substring(erpReimDetailVo.getAppyerTime(), 0, 10), psnNames, FeeNameEnum.getByType(MedConst.TYPE_2).getFeeName());
                        String narrowTrainAbs = getAbs(erpReimPayReceiptVos, StringUtils.substring(erpReimDetailVo.getAppyerTime(), 0, 10), psnNames, FeeNameEnum.getByType(MedConst.TYPE_1).getFeeName());

                        //培训费借贷
                        EcsReimFixedAsstDetailVo cwTrainCR = trainFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                        EcsReimFixedAsstDetailVo cwTrainDR = trainFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                        //差旅费借贷
                        EcsReimFixedAsstDetailVo cwNarrowTrainCR = travelFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                        EcsReimFixedAsstDetailVo cwNarrowTrainDR = travelFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                        int asstNo = 1;             //报销项序号
                        int avgNum = 1;             //当前处理部门个数
                        BigDecimal trainCaledAmount = BigDecimal.ZERO;       //当前培训费已计算金额
                        BigDecimal narrowtrainCaledAmount = BigDecimal.ZERO;         //当前狭义培训费已计算金额
                        //当前每个部门应均分培训金额
                        BigDecimal trainAvgAmount = trainSum.divide(new BigDecimal(psnDetailGroup.size()), 2, BigDecimal.ROUND_HALF_UP);
                        //当前每个部门应均分狭义培训金额
                        BigDecimal narrowTrainAvgAmount = narrowTrainSum.divide(new BigDecimal(psnDetailGroup.size()), 2, BigDecimal.ROUND_HALF_UP);
                        //生成部门培训财务借
                        for (String key : psnDetailGroup.keySet()){
                            BigDecimal curTrainAmount;
                            BigDecimal curNarrowTrainAmount;
                            if (avgNum == psnDetailGroup.size()) {
                                curTrainAmount = trainSum.subtract(trainCaledAmount);
                                curNarrowTrainAmount = narrowTrainSum.subtract(narrowtrainCaledAmount);
                            } else {
                                curTrainAmount = trainAvgAmount;
                                curNarrowTrainAmount = narrowTrainAvgAmount;
                                narrowtrainCaledAmount = narrowtrainCaledAmount.add(narrowTrainAvgAmount);
                            }
                            //培训费-借  金额为0 不生成
                            if (curTrainAmount.compareTo(BigDecimal.ZERO) != 0) {
                                ErpReimAsstDto trainCr = new ErpReimAsstDto();
                                BeanUtils.copyProperties(cwTrainCR,trainCr);
                                trainCr.setSupType(MedConst.TYPE_1);
                                trainCr.setDeptCode(psnDetailGroup.get(key).get(0).getDept());
                                trainCr.setDeptName(psnDetailGroup.get(key).get(0).getDeptName());
                                trainCr.setActigSubCode(changeCWActigCode(psnDetailGroup.get(key).get(0).getDept(),trainCr.getActigSubCode(),MedConst.TYPE_2));
                                trainCr.setReimDetailId(erpReimDetailVo.getId());
                                trainCr.setAsstNo(asstNo++);
                                trainCr.setActigAmt(curTrainAmount);
                                trainCr.setAbst(trainAbs);
                                insertAssts.add(trainCr);
                                //如果非冲抵借款且支付方式为现金支付，则按照对应金额生成贷方
                                if (StringUtils.equals(erpReimDetailVo.getIsLoan(),MedConst.TYPE_0) && !StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_0)) {
                                    ErpReimAsstDto dr = new ErpReimAsstDto();
                                    BeanUtils.copyProperties(cwTrainDR,dr);
                                    dr.setSupType(MedConst.TYPE_1);
                                    dr.setActigSubCode("10010101");
                                    dr.setActigSubName("日常公用开支");
                                    dr.setCashFlowCode("010201");
                                    dr.setCashFlowName("购买商品、接受劳务支付的现金");
                                    dr.setActigAmt(curTrainAmount);
                                    dr.setAsstNo(asstNo++);
                                    dr.setReimDetailId(erpReimDetailVo.getId());
                                    dr.setAbst(trainAbs);
                                    insertAssts.add(dr);
                                }
                            }

                            //狭义培训费  金额为0 不生成
                            if (curNarrowTrainAmount.compareTo(BigDecimal.ZERO) != 0) {
                                ErpReimAsstDto narrowTrainCr = new ErpReimAsstDto();
                                BeanUtils.copyProperties(cwNarrowTrainCR,narrowTrainCr);
                                narrowTrainCr.setSupType(MedConst.TYPE_1);
                                narrowTrainCr.setDeptCode(psnDetailGroup.get(key).get(0).getDept());
                                narrowTrainCr.setDeptName(psnDetailGroup.get(key).get(0).getDeptName());
                                narrowTrainCr.setActigSubCode(changeCWActigCode(psnDetailGroup.get(key).get(0).getDept(),narrowTrainCr.getActigSubCode(),MedConst.TYPE_1));
                                narrowTrainCr.setReimDetailId(erpReimDetailVo.getId());
                                narrowTrainCr.setAsstNo(asstNo++);
                                narrowTrainCr.setActigAmt(curNarrowTrainAmount);
                                narrowTrainCr.setAbst(narrowTrainAbs);
                                insertAssts.add(narrowTrainCr);
                                //如果非冲抵借款且为现金支付，则按照对应金额生成贷方
                                if (StringUtils.equals(erpReimDetailVo.getIsLoan(),MedConst.TYPE_0) && !StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_0)) {
                                    ErpReimAsstDto dr = new ErpReimAsstDto();
                                    BeanUtils.copyProperties(cwNarrowTrainDR,dr);
                                    dr.setSupType(MedConst.TYPE_1);
                                    dr.setActigSubCode("10010101");
                                    dr.setActigSubName("日常公用开支");
                                    dr.setCashFlowCode("010201");
                                    dr.setCashFlowName("购买商品、接受劳务支付的现金");
                                    dr.setActigAmt(curNarrowTrainAmount);
                                    dr.setAsstNo(asstNo++);
                                    dr.setReimDetailId(erpReimDetailVo.getId());
                                    dr.setAbst(narrowTrainAbs);
                                    insertAssts.add(dr);
                                }
                            }
                            avgNum++;
                        }

                        if (StringUtils.equals(erpReimDetailVo.getIsLoan(),MedConst.TYPE_0)) {
                            //如果非冲抵借款且支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
                            if (StringUtils.isEmpty(erpReimDetailVo.getPayMethod()) || StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_0)) {
                                for (ErpReimPayReceiptVo vo : erpReimPayReceiptVos) {
                                    String reimAbs = vo.getPayDate() + "付" + erpReimDetailVo.getAppyerName() + "培训费";
                                    //生成财务贷方
                                    ErpReimAsstDto dr = new ErpReimAsstDto();
                                    BeanUtils.copyProperties(cwTrainDR,dr);
                                    dr.setSupType(MedConst.TYPE_1);
                                    dr.setReimDetailId(erpReimDetailVo.getId());
                                    dr.setAsstNo(asstNo);
                                    dr.setActigAmt(vo.getPayAmt());
                                    dr.setAbst(reimAbs);
                                    insertAssts.add(dr);
                                    asstNo++;
                                }
                            }
                        } else {
                            //查询冲抵借款报销凭证
                            ErpReimAsstDto param = new ErpReimAsstDto();
                            param.setReimDetailId(erpReimDetailVo.getLoanReimId());
                            param.setSupType(MedConst.TYPE_1);
                            List<ErpReimAsstVo> erpReimAsstVos = erpVcrDetailReadMapper.queryReimAsstVoList(param);
                            if (CollectionUtil.isEmpty(erpReimAsstVos)) {
                                log.error("冲抵借款报销未生成凭证,借款报销id：{}",erpReimDetailVo.getLoanReimId());
                                throw new AppException(String.format("冲抵借款报销未生成凭证,借款报销id：%s",erpReimDetailVo.getLoanReimId()));
                            }
                            //获取借款报销财务借方数据，作为当前凭证的财务贷方数据
                            List<ErpReimAsstVo> loanAsstCrs = erpReimAsstVos.stream().filter(item -> StringUtils.equals(item.getActigSys(), MedConst.TYPE_1) && StringUtils.equals(item.getActigAmtType(), MedConst.TYPE_1)).collect(Collectors.toList());
                            FeeNameEnum byType = FeeNameEnum.getByType(dto.getType());
                            //修改对应借贷方和报销id
                            for (int k = 0; k < loanAsstCrs.size(); k++) {
                                ErpReimAsstVo loanReimVo = loanAsstCrs.get(k);
                                ErpReimAsstDto dr = new ErpReimAsstDto();
                                BeanUtils.copyProperties(loanReimVo, dr);
//                                dr.setPayTypeCode(byType.getPayTypeCode());
//                                dr.setPayTypeName(byType.getFeeName());
                                dr.setActigAmt(erpReimDetailVo.getLoanAmt());
                                dr.setActigSys(MedConst.TYPE_1);
                                dr.setActigAmtType(MedConst.TYPE_2);
                                dr.setReimDetailId(erpReimDetailVo.getId());
                                dr.setAsstNo(asstNo++);
                                insertAssts.add(dr);
                            }
                        }

                        //生成培训费预算借贷
                        EcsReimFixedAsstDetailVo ysTrainCR = trainFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                        EcsReimFixedAsstDetailVo ysTrainDR = trainFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                        ErpReimAsstDto trainCR = new ErpReimAsstDto();
                        ErpReimAsstDto trainDR = new ErpReimAsstDto();
                        BeanUtils.copyProperties(ysTrainCR,trainCR);
                        BeanUtils.copyProperties(ysTrainDR,trainDR);
                        trainCR.setSupType(MedConst.TYPE_1);
                        trainCR.setReimDetailId(erpReimDetailVo.getId());
                        trainCR.setActigAmt(erpReimDetailVo.getSum());
                        trainCR.setAsstNo(asstNo);
                        trainCR.setAbst(trainAbs);
                        insertAssts.add(trainCR);
                        trainDR.setSupType(MedConst.TYPE_1);
                        trainDR.setReimDetailId(erpReimDetailVo.getId());
                        trainDR.setActigAmt(erpReimDetailVo.getSum());
                        trainDR.setAsstNo(asstNo);
                        trainDR.setAbst(trainAbs);
                        insertAssts.add(trainDR);
                        asstNo++;
                        //生成狭义培训费预算借贷
                        /*EcsReimFixedAsstDetailVo ysNarrowTrainCR = travelFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                        EcsReimFixedAsstDetailVo ysNarrowTrainDR = travelFixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                        ErpReimAsstDto narrowTrainCR = new ErpReimAsstDto();
                        ErpReimAsstDto narrowTrainDR = new ErpReimAsstDto();
                        BeanUtils.copyProperties(ysNarrowTrainCR,narrowTrainCR);
                        BeanUtils.copyProperties(ysNarrowTrainDR,narrowTrainDR);
                        narrowTrainCR.setSupType(MedConst.TYPE_1);
                        narrowTrainCR.setReimDetailId(erpReimDetailVo.getId());
                        narrowTrainCR.setActigAmt(trainSum);
                        narrowTrainCR.setAsstNo(asstNo);
                        narrowTrainCR.setAbst(trainAbs);
                        insertAssts.add(narrowTrainCR);
                        narrowTrainDR.setSupType(MedConst.TYPE_1);
                        narrowTrainDR.setReimDetailId(erpReimDetailVo.getId());
                        narrowTrainDR.setActigAmt(narrowTrainSum);
                        narrowTrainDR.setAsstNo(asstNo);
                        narrowTrainDR.setAbst(narrowTrainAbs);
                        insertAssts.add(narrowTrainDR);*/
                    }
                }
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_3)) {
                //其他费用
                generalAsstGen(
                        erpReimDetailVos,
                        FeeNameEnum.getByType(dto.getType()).getFeeName(),
                        reimAsstDetails, insertAssts, dto.getCertificateDate(), dto);
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_4)) {
                //分摊不会多个报销
                ErpReimDetailVo erpReimDetailVo = erpReimDetailVos.get(0);
                //获取选择的辅助配置项
                if (CollectionUtils.isEmpty(reimAsstDetails)) {
                    log.error("会计科目信息不能为空");
                    throw new AppException("会计科目信息不能为空");
                }
                //调用阿里接口，识别信息
                //List<Map<String, String>> maps = handleAlyPayInfoMaps(erpReimDetailVo.getId());
                //查询对应报销付款证明信息
                List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getPayReceiptInfos(dto.getSupType(), erpReimDetailVo.getId());
                //根据报销id 读取科室分摊数据(注：此时的科室为用友中的部门code，todo 考虑在解析excel时，新增分摊数据时，就转为hrp的数据)
                List<ErpReimShareEntity> erpReimShareEntities = erpVcrDetailReadMapper.queryShareDetails(dto);

                //查询分摊报销对应往来单位信息
                List<ErpReimRelCoDetail> erpReimRelCoDetails = erpVcrDetailReadMapper.queryReimRelCoDetails(dto);
                //统一报销摘要，分摊不用生成申请人
                String abs = getAbs(erpReimPayReceiptVos, StringUtils.substring(erpReimDetailVo.getAppyerTime(), 0, 10), "", ShareTypeEnum.getByType(dto.getShareType()).getShareTypeName());
                //是否含有财务会计
                boolean cwActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_1, MedConst.TYPE_1);
                //是否含有预算会计
                boolean ysActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_2, MedConst.TYPE_1);
                boolean cwActigDept = false;
                boolean ysActigDept = false;
                //判断财务会计是否含有部门，有财务会计时，变量才有效
                if (cwActig) {
                    cwActigDept = hasDeptAsst(dto, MedConst.TYPE_1, MedConst.TYPE_1);
                }
                //判断预算会计是否含有部门，有预算会计时，变量才有效
                if (ysActig) {
                    ysActigDept = hasDeptAsst(dto, MedConst.TYPE_2, MedConst.TYPE_1);
                }
                //报销项序号
                int asstNo = 1;
                //1.生成财务会计
                //1.1 生成财务-借 （分摊类型单独生成财务贷，按照上传付款证明文件金额生成）
                if (cwActig) {
                    //1.1获取会计借，判断是否含有部门辅助项  含有/按部门生成辅助项;不含有/按总额生成辅助项
                    //财务会计科目  借和贷
                    ErpReimAsstDto cwCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto cwDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    //是否按部门生成
                    if (cwActigDept) {
                        //循环每个部门
                        for (int i = 0; i < erpReimShareEntities.size(); i++) {
                            ErpReimShareEntity ese = erpReimShareEntities.get(i);
                            //部门金额为零跳过
                            if (ese.getAmt().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            //财务借
                            ErpReimAsstDto targetDto = new ErpReimAsstDto();
                            BeanUtils.copyProperties(cwCR, targetDto);
                            targetDto.setSupType(MedConst.TYPE_1);
                            targetDto.setDeptCode(ese.getDeptCode());
                            targetDto.setDeptName(ese.getDeptName());
                            targetDto.setActigSubCode(changeCWActigCode(ese.getDeptCode(), targetDto.getActigSubCode(), dto.getType()));
                            targetDto.setReimDetailId(erpReimDetailVo.getId());
                            targetDto.setAsstNo(asstNo++);
                            targetDto.setActigAmt(ese.getAmt());
                            targetDto.setAbst(abs);
                            insertAssts.add(targetDto);
                        }
                    } else {
                        ErpReimAsstDto targetDto = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwCR, targetDto);
                        targetDto.setSupType(MedConst.TYPE_1);
                        targetDto.setActigAmt(erpReimDetailVo.getSum());
                        targetDto.setReimDetailId(erpReimDetailVo.getId());
                        targetDto.setAsstNo(asstNo++);
                        targetDto.setAbst(abs);
                        insertAssts.add(targetDto);
                    }
                    //2.分摊报销额外操作，按照上传往来单位生成财务借贷
                    //往来单位
                    ShareRelCoEnum relCoType = ShareRelCoEnum.getByType(dto.getShareType());
                    //有往来单位才生成往来单位借
                    if (CollectionUtil.isNotEmpty(erpReimRelCoDetails)) {
                        if (Objects.isNull(relCoType)) {
                            throw new AppException("往来单位科目信息未维护");
                        }
                        //财务-借
                        ErpReimAsstDto relCoCR = new ErpReimAsstDto();
                        relCoCR.setSupType(MedConst.TYPE_1);
                        relCoCR.setPayTypeCode(ErpConstants.PAY_TYPECODE_SHARE);
                        relCoCR.setPayTypeName("分摊费用");
                        relCoCR.setActigSys(MedConst.TYPE_1);
                        relCoCR.setActigAmtType(MedConst.TYPE_1);
                        relCoCR.setActigSubCode(relCoType.getActigCRCode());
                        relCoCR.setActigSubName(relCoType.getActigCRName());
                        //财务-贷
                        ErpReimAsstDto relCoDR = new ErpReimAsstDto();
                        relCoDR.setSupType(MedConst.TYPE_1);
                        relCoDR.setPayTypeCode(ErpConstants.PAY_TYPECODE_SHARE);
                        relCoDR.setPayTypeName("分摊费用");
                        relCoDR.setActigSys(MedConst.TYPE_1);
                        relCoDR.setActigAmtType(MedConst.TYPE_2);
                        relCoDR.setActigSubCode(relCoType.getActigDRCode());
                        relCoDR.setActigSubName(relCoType.getActigDRName());
                        relCoDR.setCashFlowCode(relCoType.getCashFlowCode());
                        relCoDR.setCashFlowName(relCoType.getCashFlowName());
                        //循环往来单位金额
                        for (int i = 0; i < erpReimRelCoDetails.size(); i++) {
                            ErpReimRelCoDetail relCoItem = erpReimRelCoDetails.get(i);
                            //往来单位金额为0，则跳过
                            if (relCoItem.getAmt().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            ErpReimAsstDto cr = new ErpReimAsstDto();

                            BeanUtils.copyProperties(relCoCR, cr);
                            cr.setSupType(MedConst.TYPE_1);
                            cr.setRelCoCode(relCoItem.getRelCoCode());
                            cr.setRelCoName(relCoItem.getRelCoName());
                            cr.setReimDetailId(erpReimDetailVo.getId());
                            cr.setAsstNo(asstNo++);
                            cr.setActigAmt(relCoItem.getAmt());
                            cr.setAbst(abs);
                            insertAssts.add(cr);
                            /*
                            往来单位不生成贷方
                            ErpReimAsstDto dr = new ErpReimAsstDto();
                            BeanUtils.copyProperties(relCoDR,dr);
                            dr.setReimDetailId(erpReimDetailVo.getId());
                            dr.setAsstNo(asstNo);
                            dr.setActigAmt(relCoItem.getAmt());
                            dr.setAbst(reimAbs);
                            insertAssts.add(dr);*/
                        }
                    }

                    //1.2.获取会计贷，获取付款证明文件，通过会计贷生成其辅助项目，分摊摘要不需生成申请人
                    genCWDRAssts(insertAssts, erpReimDetailVo, cwDR, asstNo, "", ShareTypeEnum.getByType(dto.getShareType()).getShareTypeName(), erpReimPayReceiptVos);
                    asstNo+=erpReimDetailVos.size();
                }

                //计算往来单位总金额
                BigDecimal relCoAmt = erpReimRelCoDetails.stream()
                        .map(ErpReimRelCoDetail::getAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal ysAmt = erpReimDetailVo.getSum().subtract(relCoAmt);
                //3.生成预算会计,有预算会计则生成，没有则不生成
                //3.1 生成预算-借
                if (ysActig) {
                    //3.1 获取预算借，判断是否含有部门辅助项  含有/按部门生成辅助项；不含有/按总额生成辅助项
                    //预算会计科目  借和贷
                    ErpReimAsstDto ysCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto ysDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    //是否按部门生成
                    if (ysActigDept) {
                        //循环每个部门
                        for (int i = 0; i < erpReimShareEntities.size(); i++) {
                            ErpReimShareEntity ese = erpReimShareEntities.get(i);
                            if (ese.getAmt().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            //预算借
                            ErpReimAsstDto targetDto = new ErpReimAsstDto();
                            BeanUtils.copyProperties(ysCR, targetDto);
                            targetDto.setSupType(MedConst.TYPE_1);
                            targetDto.setDeptCode(ese.getDeptCode());
                            targetDto.setDeptName(ese.getDeptName());
                            targetDto.setActigSubCode(changeCWActigCode(ese.getDeptCode(), targetDto.getActigSubCode(), dto.getType()));
                            targetDto.setReimDetailId(erpReimDetailVo.getId());
                            targetDto.setAsstNo(asstNo++);
                            targetDto.setActigAmt(ese.getAmt());
                            targetDto.setAbst(abs);
                            insertAssts.add(targetDto);
                            //预算贷
                            ErpReimAsstDto dr = new ErpReimAsstDto();
                            BeanUtils.copyProperties(ysDR, dr);
                            dr.setSupType(MedConst.TYPE_1);
                            dr.setReimDetailId(erpReimDetailVo.getId());
                            dr.setAsstNo(asstNo++);
                            dr.setActigAmt(ese.getAmt());
                            dr.setAbst(abs);
                            insertAssts.add(dr);
                        }
                    } else {
                        ErpReimAsstDto targetDto = new ErpReimAsstDto();
                        BeanUtils.copyProperties(ysCR, targetDto);
                        targetDto.setSupType(MedConst.TYPE_1);
                        targetDto.setActigAmt(ysAmt);
                        targetDto.setReimDetailId(erpReimDetailVo.getId());
                        targetDto.setAsstNo(asstNo++);
                        targetDto.setAbst(abs);
                        insertAssts.add(targetDto);
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(ysDR, dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAsstNo(asstNo++);
                        dr.setActigAmt(ysAmt);          //预算的借贷金额为，分摊金额减去往来单位金额
                        dr.setAbst(abs);
                        insertAssts.add(dr);
                    }
                }
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_5)) {
                //工资凭证单独类型生成
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_6)) {
                //合同
                generalAsstGen(
                        erpReimDetailVos,
                        FeeNameEnum.getByType(dto.getType()).getFeeName(),
                        reimAsstDetails, insertAssts, dto.getCertificateDate(), dto);
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_8)) {
                //采购凭证
                insertAssts.addAll(generalPurcAsst(dto.getIds(),dto));
                //返回vpzh
                resMsg = dto.getVpzh();
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_10)) {
                //物资采购凭证
                insertAssts.addAll(generalPurcAsst(dto.getIds(),dto));
                //返回vpzh
                resMsg = dto.getVpzh();
            } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_11)) {
                //其他费用(无发票)
                generalAsstGen(
                        erpReimDetailVos,
                        FeeNameEnum.getByType(dto.getType()).getFeeName(),
                        reimAsstDetails, insertAssts, dto.getCertificateDate(), dto);
            } else if(StringUtils.equals(dto.getType(), MedConst.TYPE_12)){
                insertAssts.addAll(saveTransactionsVcrAssts(dto));
            }else if (StringUtils.equals(dto.getType(), MedConst.TYPE_13)){
                //借款
                generalAsstGen(
                        erpReimDetailVos,
                        FeeNameEnum.getByType(dto.getType()).getFeeName(),
                        reimAsstDetails, insertAssts, dto.getCertificateDate(), dto);
            }
        } else if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_2)) {
            //药品报销
            //查询药品报销
            List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(dto.getIds());
            //是否含有财务会计
            boolean cwActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_1, MedConst.TYPE_1);
            //是否含有预算会计
            boolean ysActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_2, MedConst.TYPE_1);
            boolean cwActigDept = false;
            boolean ysActigDept = false;
            //判断财务会计是否含有部门，有财务会计时，变量才有效
            if (cwActig) {
                cwActigDept = hasDeptAsst(dto, MedConst.TYPE_1, MedConst.TYPE_1);
            }
            //判断预算会计是否含有部门，有预算会计时，变量才有效
            if (ysActig) {
                ysActigDept = hasDeptAsst(dto, MedConst.TYPE_2, MedConst.TYPE_1);
            }

            //报销项序号
            int asstNo = 1;

            for (int i = 0; i < drugVos.size(); i++) {
                ErpDrugReimDetailVo drugVo = drugVos.get(i);
                //查询对应报销付款证明信息
                List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getPayReceiptInfos(dto.getSupType(), drugVo.getId().intValue());
                //摘要
                String abs = drugVo.getPayIstr();
                if (cwActig) {
                    ErpReimAsstDto cwCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto cwDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    //直接按报销生成，没有部门区分
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwCR, cr);
                    cr.setSupType(MedConst.TYPE_2);
                    cr.setActigAmt(drugVo.getSum());
                    cr.setReimDetailId(drugVo.getId().intValue());
                    cr.setAsstNo(asstNo);
                    cr.setAbst(abs);
                    insertAssts.add(cr);
                    asstNo++;

                    //通过付款证明文件识别，生成财务贷
                    for (int j = 0; j < erpReimPayReceiptVos.size(); j++) {
                        ErpReimPayReceiptVo payVo = erpReimPayReceiptVos.get(j);
                        //生成贷方辅助项
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwDR, dr);
                        dr.setSupType(MedConst.TYPE_2);
                        dr.setReimDetailId(drugVo.getId().intValue());
                        dr.setAsstNo(asstNo);
                        dr.setActigAmt(payVo.getPayAmt());
                        dr.setAbst(abs);
                        insertAssts.add(dr);
                        asstNo++;
                    }
                }

                if (ysActig) {
                    ErpReimAsstDto ysCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto ysDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    //直接按报销生成，没有部门区分
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysCR, cr);
                    BeanUtils.copyProperties(ysDR, dr);
                    cr.setSupType(MedConst.TYPE_2);
                    cr.setActigAmt(drugVo.getSum());
                    cr.setReimDetailId(drugVo.getId().intValue());
                    cr.setAsstNo(asstNo);
                    cr.setAbst(abs);
                    insertAssts.add(cr);
//                    asstNo++;
                    dr.setSupType(MedConst.TYPE_2);
                    dr.setReimDetailId(drugVo.getId().intValue());
                    dr.setAsstNo(asstNo);
                    dr.setActigAmt(drugVo.getSum());
                    dr.setAbst(abs);
                    insertAssts.add(dr);
                    asstNo++;
                }
            }
        } else if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_3)) {

        } else if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_4))

        /*if (true) {
            throw new AppException("测试异常");
        }*/

        //新增
        insertAssts.stream().forEach(asst -> {
            asst.setCrter(dto.getRecordPersonId());
            asst.setCreateTime(DateUtil.getCurrentTime(null));
            asst.setHospitalId("zjxrmyy");
        });
        System.out.println("需要插入的辅助项" + insertAssts.size());
        System.out.println(new Gson().toJson(insertAssts));
//        删除旧辅助项目
        if (!StringUtils.equals(dto.getType(),MedConst.TYPE_8) && !StringUtils.equals(dto.getType(),MedConst.TYPE_10)) {
            erpVcrDetailWriteMapper.deleteVcrReimAsst(dto);
        }
        BatchUtil.batch("insertReimAsst", insertAssts, ErpVcrDetailWriteMapper.class);
        log.info("------------------end生成报销assts-------------------------");
        return resMsg;
    }

    @Override
    public String saveErpAsstsWithFile(Certificate dto) {
        log.info("------------------start生成辅助项-------------------------");
        if (CollectionUtil.isEmpty(dto.getIds())) {
            throw new AppException("请选择报销");
        }

        String resMsg = "1";
        List<ErpReimAsstDto> reimAsstDetails = dto.getReimAsstDetails();

        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_1)) {
            //查询报销明细
            List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailList(dto.getIds());
            //暂时只有费用报销(无发票) 需上传借方数据
            if (StringUtils.equals(dto.getType(),MedConst.TYPE_11)) {
                if (CollectionUtil.isEmpty(dto.getAttFiles())) {
                    throw new AppException("借方凭证信息文件未上传");
                }
                List<ErpReimAsstDto> fileAssts = new ArrayList<>();
                //识别文件数据
                try {
                    EasyExcel.read(dto.getAttFiles().get(0).getInputStream(), ErpReimAsstDto.class, new AnalysisEventListener<ErpReimAsstDto>() {
                        @Override
                        public void invoke(ErpReimAsstDto asstDto, AnalysisContext analysisContext) {
                            if (StringUtils.isEmpty(asstDto.getActigSubCode())) {
                                throw new AppException("存在会计科目为空的行，请检查");
                            }
                            asstDto.setPayTypeCode(FeeNameEnum.getByType(dto.getType()).getPayTypeCode());
                            asstDto.setPayTypeName(FeeNameEnum.getByType(dto.getType()).getFeeName());
                            asstDto.setSupType(MedConst.TYPE_1);
                            asstDto.setActigAmtType(MedConst.TYPE_1);
                            asstDto.setActigSys(MedConst.TYPE_1);
                            asstDto.setReimDetailId(erpReimDetailVos.get(0).getId().intValue());
                            fileAssts.add(asstDto);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                        }
                    }).sheet().doRead();
                } catch (IOException e) {
                    log.error("文件识别失败");
                    throw new AppException("文件识别失败");
                }

                generalAsstGen(
                        erpReimDetailVos,
                        FeeNameEnum.getByType(dto.getType()).getFeeName(),
                        reimAsstDetails, insertAssts, dto.getCertificateDate(), dto);
                //处理借方数据
                insertAssts = insertAssts.stream()
                        .filter(item -> !StringUtils.equals(item.getActigSys(),MedConst.TYPE_1)
                        || !StringUtils.equals(item.getActigAmtType(),MedConst.TYPE_1)).collect(Collectors.toList());
                insertAssts.addAll(fileAssts);
                insertAssts.sort(Comparator.comparing(ErpReimAsstDto::getActigSys)
                        .thenComparing(ErpReimAsstDto::getActigAmtType));

                // 重新设置 asstNo，按排序后的顺序递增
                int asstNo = 1;
                for (ErpReimAsstDto asst : insertAssts) {
                    asst.setAsstNo(asstNo++);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(insertAssts)) {
            insertAssts.stream().forEach(asst -> {
                asst.setCrter(dto.getRecordPersonId());
                asst.setCreateTime(DateUtil.getCurrentTime(null));
                asst.setHospitalId("zjxrmyy");
            });
            System.out.println("需要插入的辅助项" + insertAssts.size());
            System.out.println(new Gson().toJson(insertAssts));
//        删除旧辅助项目
            if (!StringUtils.equals(dto.getType(),MedConst.TYPE_8) && !StringUtils.equals(dto.getType(),MedConst.TYPE_10)) {
                erpVcrDetailWriteMapper.deleteVcrReimAsst(dto);
            }
            BatchUtil.batch("insertReimAsst", insertAssts, ErpVcrDetailWriteMapper.class);
        }
        log.info("------------------end生成报销assts-------------------------");
        return resMsg;
    }

    /**
     * 生成往来支付凭证
     */
    private List<ErpReimAsstDto> saveTransactionsVcrAssts(Certificate dto) {
        log.info("------------------start生成往来支付assts-------------------------");
        List<ErpReimAsstDto> assts = new ArrayList<>();
        //查询报销明细
        List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailList(dto.getIds());
        FeeNameEnum fee = FeeNameEnum.getByType(dto.getType());
        //循环报销
        int asstNo = 1;
        for (int i = 0; i < erpReimDetailVos.size(); i++) {
            ErpReimDetailVo erpReimDetailVo = erpReimDetailVos.get(i);
            //查询对应报销付款证明信息
            List<ErpReimPayReceiptVo> payReceiptInfos = getPayReceiptInfos(dto.getSupType(), erpReimDetailVo.getId());
            //查询当前报销的报销项
            List<ErpReimItemDetail> erpReimItemDetails = erpVcrDetailReadMapper.queryItemDetail(Arrays.asList(erpReimDetailVo.getId()));
            for (int j = 0; j < erpReimItemDetails.size(); j++) {
                ErpReimItemDetail erpReimItemDetail = erpReimItemDetails.get(j);
                //借方
                ErpReimAsstDto cr = new ErpReimAsstDto();
                cr.setPayTypeCode(fee.getPayTypeCode());
                cr.setPayTypeName(fee.getFeeName());
                cr.setActigSys(MedConst.TYPE_1);
                cr.setActigAmtType(MedConst.TYPE_1);
                cr.setSupType(MedConst.TYPE_1);
                cr.setActigSubCode("121203");
                cr.setActigSubName("其他应收账款");
                cr.setRelCoCode(erpReimItemDetail.getRelCo());
                cr.setRelCoName(erpReimItemDetail.getRelCoName());
                cr.setReimDetailId(erpReimDetailVo.getId());
                cr.setAsstNo(asstNo++);
                cr.setActigAmt(erpReimItemDetail.getAmt());
                cr.setAbst(erpReimItemDetail.getReimAbst());
                assts.add(cr);
                //如果支付类型为现金支付或者复明工程，这没有付款证明文件，按照项目金额生成
                if (!StringUtils.equals(erpReimDetailVo.getPayMethod(), MedConst.TYPE_0)) {
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    dr.setPayTypeCode(fee.getPayTypeCode());
                    dr.setPayTypeName(fee.getFeeName());
                    dr.setActigSys(MedConst.TYPE_1);
                    dr.setActigAmtType(MedConst.TYPE_2);
                    dr.setSupType(MedConst.TYPE_1);
                    dr.setActigSubCode("10020101");
                    dr.setActigSubName("银行存款");
                    dr.setCashFlowCode("010204");
                    dr.setCashFlowName("支付的其他与日常活动有关的现金");
                    dr.setReimDetailId(erpReimDetailVo.getId());
                    dr.setAsstNo(asstNo++);
                    dr.setActigAmt(erpReimItemDetail.getAmt());
                    dr.setAbst(erpReimItemDetail.getReimAbst());
                    assts.add(dr);
                }
            }
            //如果支付类型为非现金，则按照付款单生成财务贷方
            if (StringUtils.equals(erpReimDetailVo.getPayMethod(), MedConst.TYPE_0)) {
                for (int j = 0; j < payReceiptInfos.size(); j++) {
                    ErpReimPayReceiptVo erpReimPayReceiptVo = payReceiptInfos.get(j);
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    dr.setPayTypeCode(fee.getPayTypeCode());
                    dr.setPayTypeName(fee.getFeeName());
                    dr.setActigSys(MedConst.TYPE_1);
                    dr.setActigAmtType(MedConst.TYPE_2);
                    dr.setSupType(MedConst.TYPE_1);
                    dr.setActigSubCode("10020101");
                    dr.setActigSubName("银行存款");
                    dr.setCashFlowCode("010204");
                    dr.setCashFlowName("支付的其他与日常活动有关的现金");
                    dr.setReimDetailId(erpReimDetailVo.getId());
                    dr.setAsstNo(asstNo++);
                    dr.setActigAmt(erpReimPayReceiptVo.getPayAmt());
                    dr.setAbst("付往来支付款");
                    assts.add(dr);
                }
            }
        }
        log.info("------------------end生成往来支付assts-------------------------");
        return assts;
    }

    /**
     * 生成采购凭证
     * @param reimIds
     * @return
     */
    private List<ErpReimAsstDto> generalPurcAsst(List<Integer> reimIds,Certificate purcDto) {
        log.info("------------------start生成采购assts-------------------------");
        List<ErpReimAsstDto> assts = new ArrayList<>();
        //查询报销明细
        List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailList(reimIds);
        //判断付款方式 0非现金 1现金
        if (purcDto.getPayMethod().equals(MedConst.TYPE_0)) {
            //所有报销的pay_rcpt_id 都不能为空
            erpReimDetailVos.stream().forEach(reim -> {
                if (Objects.isNull(reim.getPayRcptId())) {
                    throw new AppException("存在未上传付款证明文件的报销");
                }
            });
            //获取当前报销对应的所有pay_rcpt_id  去重
            Set<Integer> payRcptIds = erpReimDetailVos.stream().map(ErpReimDetailVo::getPayRcptId).collect(Collectors.toSet());
            //判断payRcptIds 包含了所有对应的报销
            List<ErpReimDetailVo> erpReimDetailVos1 = erpVcrDetailReadMapper.queryReimDetailByPayRcptId(new ArrayList<>(payRcptIds));
            //获取pay_rcpt_id 对应的所有报销
            Set<Integer> allReimIds = erpReimDetailVos1.stream().map(ErpReimDetailVo::getId).collect(Collectors.toSet());
            //判断两者报销id是否相同，以此判断是否选择了当前pay_rcpt_id对应的所有报销
            if ((reimIds.size() != allReimIds.size()) || (reimIds.size() == allReimIds.size() && !allReimIds.containsAll(reimIds))) {
                throw new AppException("请选择当前付款文件对应的所有报销");
            }
        }


        //查询报销项目明细
        List<ErpReimItemDetail> erpReimItemDetails = erpVcrDetailReadMapper.queryItemDetail(reimIds);
        //hrp-用友科室映射(用于使用用友部门前缀判断科室类型)
        CommonResult<List<HrmOrgAgencyMapVo>> orgMapresult = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());

        if (Objects.isNull(orgMapresult) || CollectionUtil.isEmpty(orgMapresult.getData())) {
            throw new AppException("当前未维护科室关系映射!");
        }
        List<HrmOrgAgencyMapVo> orgAgencyMapVos = orgMapresult.getData();
        //查询采购的固定项目
        CommonResult<List<EcsReimFixedAsstVo>> result = ecsReimFeignService.list(new EcsReimFixedAsstDto());
        if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
            throw new AppException("未查询到固定项目配置");
        }
        List<EcsReimFixedAsstVo> fixedAsstVos = result.getData();
        //零星采购固定科目
        List<EcsReimFixedAsstVo> purc1 = fixedAsstVos.stream()
                .filter(item -> StringUtils.equals(item.getFixedSubCode(), ErpConstants.PAY_TYPECODE_PURC))
                .collect(Collectors.toList());
        if (purc1.isEmpty()) {
            throw new AppException("未查询到零星采购固定科目");
        }
        //物资采购固定科目
        List<EcsReimFixedAsstVo> purc2 = fixedAsstVos.stream()
                .filter(item -> StringUtils.equals(item.getFixedSubCode(), ErpConstants.PAY_TYPECODE_PURC_WZ))
                .collect(Collectors.toList());
        if (purc2.isEmpty()) {
            throw new AppException("未查询到物资采购固定科目");
        }

        //删除旧辅助项目
        ErpVcrDetailDto purcParam = new ErpVcrDetailDto();
        purcParam.setIds(reimIds);
        purcParam.setSupType(MedConst.TYPE_1);  //零星、物资supType 为1
        purcParam.setType(purcDto.getType());     //type 零星8 物资 10
        delDrugVpzhMsg(purcParam);

        //生成报销的预览凭证记录  对应一个凭证
        List<ErpVcrPreviewDto> previews = new ArrayList<>();
        String hrppzh = ULIDUtil.generate();
        purcDto.setVpzh(hrppzh);
        for (int i = 0; i < reimIds.size(); i++) {
            ErpVcrPreviewDto previewDto = new ErpVcrPreviewDto();
            previewDto.setPayType(purcDto.getType());
            previewDto.setModuleId(reimIds.get(i).intValue());
            previewDto.setSupType(MedConst.TYPE_1);
            previewDto.setPushFlag(MedConst.TYPE_0);
            previewDto.setVpzh(hrppzh);
            previewDto.setCrter("");
            previewDto.setCreateTime(DateUtil.getCurrentTime(null));
            previews.add(previewDto);
        }
        BatchUtil.batch(previews, ErpVcrPreviewWriteMapper.class);

        if (StringUtils.equals(purcDto.getType(), MedConst.TYPE_8)) {
            int asstNo = 1;
            for (int j = 0; j < erpReimDetailVos.size(); j++) {
                ErpReimDetailVo erpReimDetailVo = erpReimDetailVos.get(j);
                //获取当前报销项目
                List<ErpReimItemDetail> items = erpReimItemDetails.stream()
                        .filter(e -> e.getReimDetailId().longValue() == erpReimDetailVo.getId().intValue()).collect(Collectors.toList());
                //借方
                for (int i = 0; i < items.size(); i++) {
                    ErpReimItemDetail item = items.get(i);
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    //判断科室类型 转换为用友dept 前缀判断科室类型
                    String deptType = getDeptType(item.getDeptCode(),orgAgencyMapVos);    // 1：临床医技医辅 2：行政
                    if (StringUtils.isEmpty(deptType)) {
                        throw new AppException(String.format("不存在当前科室类型-科室%s", item.getDeptCode()));
                    }
                    //筛选当前项目对应的借方科目
                    Optional<EcsReimFixedAsstVo> asstVo = purc1.stream().filter(e -> StringUtils.equals(e.getDeptType(), deptType)
                            && StringUtils.equals(e.getEconFunSubCode(), item.getType())).findFirst();
                    if (asstVo.isEmpty()) {
                        log.error(String.format("不存在当前科室类型对应的配置科目-科室类型%s-经济科目%s",deptType, item.getType()));
                        throw new AppException(String.format("不存在当前科室类型对应的配置科目-科室类型%s-经济科目%s",deptType, item.getType()));
                    }
                    List<EcsReimFixedAsstDetailVo> asstDetailVos = asstVo.get().getFixedAsstDetails();
                    //获取财务借方配置科目
                    EcsReimFixedAsstDetailVo cwPurc1CR = asstDetailVos.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    EcsReimFixedAsstDetailVo cwPurc1DR = asstDetailVos.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    BeanUtils.copyProperties(cwPurc1CR,cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setVpzh(hrppzh);
//                    cr.setReimDetailId(erpReimDetailVo.getId());
                    cr.setDeptCode(item.getDeptCode());
                    cr.setDeptName(item.getDeptName());
                    cr.setActigAmt(item.getAmt());
                    cr.setAsstNo(asstNo++);
                    cr.setAbst(item.getReimAbst());
                    assts.add(cr);
                    //如果是支付方式为现金支付或者复明工程，则没有付款证明文件
                    if (!StringUtils.equals(purcDto.getPayMethod(),MedConst.TYPE_0)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc1DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setVpzh(hrppzh);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(item.getAmt());
                        dr.setAsstNo(asstNo++);
//                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAbst(item.getReimAbst());
                        assts.add(dr);
                    }
                }

                //获取总务低值易耗品的零星采购配置项,筛选后会有行政和临床的，任取即可（预算会计相同）
                Optional<EcsReimFixedAsstVo> ysAsstVo = purc1.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.ZWDZYH)).findFirst();
                if (ysAsstVo.isEmpty()) {
                    log.error(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                    throw new AppException(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                }
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails = ysAsstVo.get().getFixedAsstDetails();
                //获取其他总务材料的零星采购配置项,筛选后会有行政和临床的，任取即可（预算会计相同）
                Optional<EcsReimFixedAsstVo> ysAsst2Vo = purc1.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.QTZWCL)).findFirst();
                if (ysAsst2Vo.isEmpty()) {
                    log.error(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                    throw new AppException(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                }
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails2 = ysAsst2Vo.get().getFixedAsstDetails();
                //预算会计-借，分为：总务低值易耗/其他总务材料  预算会计不区分科室，任意取采购的预算会计即可
                //总务低值易耗品-借
                List<ErpReimItemDetail> zwdzyh = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.ZWDZYH)).collect(Collectors.toList());
                //有总务低值易耗品，则生成对应预算-借
                if (CollectionUtil.isNotEmpty(zwdzyh)) {
                    EcsReimFixedAsstDetailVo ysPurc1CR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc1CR,yscr);
                    yscr.setSupType(MedConst.TYPE_1);
                    yscr.setVpzh(hrppzh);
//                    yscr.setReimDetailId(erpReimDetailVo.getId());
                    yscr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO, BigDecimal::add));
                    yscr.setAsstNo(asstNo++);
                    yscr.setAbst("零星采购-总务低值易耗品");
                    assts.add(yscr);
                }
                //其他总务材料-借
                List<ErpReimItemDetail> qtzwcl = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.QTZWCL)).collect(Collectors.toList());
                //有其他总务材料，则生成对应预算-借
                if (CollectionUtil.isNotEmpty(qtzwcl)) {
                    EcsReimFixedAsstDetailVo ysPurc2CR = fixedAsstDetails2.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr2 = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc2CR,yscr2);
                    yscr2.setSupType(MedConst.TYPE_1);
                    yscr2.setVpzh(hrppzh);
//                    yscr2.setReimDetailId(erpReimDetailVo.getId());
                    yscr2.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO, BigDecimal::add));
                    yscr2.setAsstNo(asstNo++);
                    yscr2.setAbst("零星采购-总务低值易耗品");
                    assts.add(yscr2);
                }
                //预算贷方
                EcsReimFixedAsstDetailVo ysPurc1DR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                ErpReimAsstDto ysdr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysPurc1DR,ysdr);
                ysdr.setSupType(MedConst.TYPE_1);
                ysdr.setVpzh(hrppzh);
//                ysdr.setReimDetailId(erpReimDetailVo.getId());
                ysdr.setActigAmt(items.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO, BigDecimal::add));
                ysdr.setAsstNo(asstNo++);
                ysdr.setAbst("零星采购");
                assts.add(ysdr);
            }

            //支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
            if (StringUtils.isEmpty(purcDto.getPayMethod()) || StringUtils.equals(purcDto.getPayMethod(),MedConst.TYPE_0)) {
                //查询当前报销所有的付款文件识别信息
                List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getDrugReceiptInfos(purcDto.getSupType(), reimIds);
                //贷方 (通过付款证明文件生成)  贷方没有分类取零星采购任一即可
                EcsReimFixedAsstDetailVo cwPurc1DR = purc1.get(0).getFixedAsstDetails().stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                for (int i = 0; i < erpReimPayReceiptVos.size(); i++) {
                    ErpReimPayReceiptVo vo = erpReimPayReceiptVos.get(i);
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwPurc1DR,dr);
                    dr.setSupType(MedConst.TYPE_1);
                    dr.setVpzh(hrppzh);
//                    dr.setReimDetailId(erpReimDetailVo.getId());
                    dr.setAsstNo(asstNo++);
                    dr.setActigAmt(vo.getPayAmt());
                    dr.setAbst(vo.getPayDate() + "付零星采购费用");
                    assts.add(dr);
                }
            }
        } else if (StringUtils.equals(purcDto.getType(),MedConst.TYPE_10)) {
            int asstNo = 1;
            for (int j = 0; j < erpReimDetailVos.size(); j++) {
                ErpReimDetailVo erpReimDetailVo = erpReimDetailVos.get(j);
                //物资采购  生成总金额凭证
//                List<EcsReimFixedAsstDetailVo> asstDetailVos = purc2.get(0).getFixedAsstDetails();
                //获取物资采购项目
                List<ErpReimItemDetail> items = erpReimItemDetails.stream()
                        .filter(e -> e.getReimDetailId().longValue() == erpReimDetailVo.getId().intValue()).collect(Collectors.toList());
                //其他总务材料
                List<ErpReimItemDetail> qtzwcl = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.QTZWCL)).collect(Collectors.toList());
                //总务低值易耗品
                List<ErpReimItemDetail> zwdzyh = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.ZWDZYH)).collect(Collectors.toList());

                //其他总务材料-配置科目
                Optional<EcsReimFixedAsstVo> wzQTcfg = purc2.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.QTZWCL)).findFirst();
                if (wzQTcfg.isEmpty()) {
                    log.error(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                    throw new AppException(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                }
                //其他总务材料科目信息
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails = wzQTcfg.get().getFixedAsstDetails();
                //总务低值易耗品-配置科目
                Optional<EcsReimFixedAsstVo> wzZWcfg = purc2.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.ZWDZYH)).findFirst();
                if (wzZWcfg.isEmpty()) {
                    log.error(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                    throw new AppException(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                }
                //总务低值易耗品科目信息
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails1 = wzZWcfg.get().getFixedAsstDetails();
                //有其他总务材料，生成凭证项
                if (CollectionUtil.isNotEmpty(qtzwcl)) {
                    //财务-借
                    EcsReimFixedAsstDetailVo cwPurc2CR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    EcsReimFixedAsstDetailVo cwPurc2DR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwPurc2CR,cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setVpzh(hrppzh);
//                    cr.setReimDetailId(erpReimDetailVo.getId());
                    cr.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    cr.setAsstNo(asstNo++);
                    cr.setAbst("物资采购-其他总务材料");
                    assts.add(cr);
                    //如果是支付方式为现金支付或者复明工程，则没有付款证明文件
                    if (!StringUtils.equals(purcDto.getPayMethod(),MedConst.TYPE_0)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc2DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setVpzh(hrppzh);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                        dr.setAsstNo(asstNo++);
//                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAbst("物资采购-其他总务材料");
                        assts.add(dr);
                    }
                    //预算-借
                    EcsReimFixedAsstDetailVo ysPurc2CR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc2CR,yscr);
                    yscr.setSupType(MedConst.TYPE_1);
                    yscr.setVpzh(hrppzh);
//                    yscr.setReimDetailId(erpReimDetailVo.getId());
                    yscr.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    yscr.setAsstNo(asstNo++);
                    yscr.setAbst("物资采购-其他总务材料");
                    assts.add(yscr);
                }
                //有总务低值易耗品项目，生成凭证项
                if (CollectionUtil.isNotEmpty(zwdzyh)) {
                    //财务-借
                    EcsReimFixedAsstDetailVo cwPurc2CR = fixedAsstDetails1.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    EcsReimFixedAsstDetailVo cwPurc2DR = fixedAsstDetails1.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwPurc2CR,cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setVpzh(hrppzh);
//                    cr.setReimDetailId(erpReimDetailVo.getId());
                    cr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    cr.setAsstNo(asstNo++);
                    cr.setAbst("物资采购-总务低值易耗品");
                    assts.add(cr);
                    //如果是支付方式为现金支付或者复明工程，则没有付款证明文件
                    if (!StringUtils.equals(purcDto.getPayMethod(),MedConst.TYPE_0)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc2DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setVpzh(hrppzh);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                        dr.setAsstNo(asstNo++);
//                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAbst("物资采购-总务低值易耗品");
                        assts.add(dr);
                    }
                    //预算-借
                    EcsReimFixedAsstDetailVo ysPurc2CR = fixedAsstDetails1.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc2CR,yscr);
                    yscr.setSupType(MedConst.TYPE_1);
                    yscr.setVpzh(hrppzh);
//                    yscr.setReimDetailId(erpReimDetailVo.getId());
                    yscr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    yscr.setAsstNo(asstNo++);
                    yscr.setAbst("物资采购-总务低值易耗品");
                    assts.add(yscr);
                }

                //预算-贷
                EcsReimFixedAsstDetailVo ysPurc2DR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                ErpReimAsstDto ysdr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysPurc2DR,ysdr);
                ysdr.setSupType(MedConst.TYPE_1);
                ysdr.setVpzh(hrppzh);
//                ysdr.setReimDetailId(erpReimDetailVo.getId());
                ysdr.setAsstNo(asstNo++);
                ysdr.setActigAmt(items.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                ysdr.setAbst("物资采购");
                assts.add(ysdr);
            }

            //支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
            if (StringUtils.isEmpty(purcDto.getPayMethod()) || StringUtils.equals(purcDto.getPayMethod(),MedConst.TYPE_0)) {
                //查询当前报销所有的付款文件识别信息
                List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getDrugReceiptInfos(purcDto.getSupType(), reimIds);
                //贷方 (通过付款证明文件生成)  贷方没有分类取物资采购任一即可
                EcsReimFixedAsstDetailVo cwPurc2DR = purc2.get(0).getFixedAsstDetails().stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                for (int i = 0; i < erpReimPayReceiptVos.size(); i++) {
                    ErpReimPayReceiptVo vo = erpReimPayReceiptVos.get(i);
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwPurc2DR,dr);
                    dr.setSupType(MedConst.TYPE_1);
                    dr.setVpzh(hrppzh);
//                    dr.setReimDetailId(erpReimDetailVo.getId());
                    dr.setAsstNo(asstNo++);
                    dr.setActigAmt(vo.getPayAmt());
                    dr.setAbst(vo.getPayDate() + "付物资采购费用");
                    assts.add(dr);
                }
            }
        }
        /*for (int j = 0; j < erpReimDetailVos.size();j++) {
            ErpReimDetailVo erpReimDetailVo = erpReimDetailVos.get(j);
//            List<EcsReimPurcTaskVo> ecsReimPurcTaskVos = purcTaskMap.get(key);
            //查询采购付款证明
//            List<ErpReimPayReceiptVo> payReceiptVos = getPayReceiptInfos(MedConst.TYPE_1, erpReimDetailVo.getId());
//            //采购报销类型  1：零星采购 2：物资
//            String taskType = ecsReimPurcTaskVos.get(0).getReimTaskType();
            int asstNo = 1;
            if (StringUtils.equals(erpReimDetailVo.getType(),MedConst.TYPE_8)) {        //零星采购 type 8
                //零星采购
                //获取当前报销项目
                List<ErpReimItemDetail> items = erpReimItemDetails.stream()
                        .filter(e -> e.getReimDetailId().longValue() == erpReimDetailVo.getId().intValue()).collect(Collectors.toList());
                //借方
                for (int i = 0; i < items.size(); i++) {
                    ErpReimItemDetail item = items.get(i);
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    //判断科室类型 转换为用友dept 前缀判断科室类型
                    String deptType = getDeptType(item.getDeptCode(),orgAgencyMapVos);    // 1：临床医技医辅 2：行政
                    if (StringUtils.isEmpty(deptType)) {
                        throw new AppException(String.format("不存在当前科室类型-科室%s", item.getDeptCode()));
                    }
                    //筛选当前项目对应的借方科目
                    Optional<EcsReimFixedAsstVo> asstVo = purc1.stream().filter(e -> StringUtils.equals(e.getDeptType(), deptType)
                            && StringUtils.equals(e.getEconFunSubCode(), item.getType())).findFirst();
                    if (asstVo.isEmpty()) {
                        log.error(String.format("不存在当前科室类型对应的配置科目-科室类型%s-经济科目%s",deptType, item.getType()));
                        throw new AppException(String.format("不存在当前科室类型对应的配置科目-科室类型%s-经济科目%s",deptType, item.getType()));
                    }
                    List<EcsReimFixedAsstDetailVo> asstDetailVos = asstVo.get().getFixedAsstDetails();
                    //获取财务借方配置科目
                    EcsReimFixedAsstDetailVo cwPurc1CR = asstDetailVos.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    EcsReimFixedAsstDetailVo cwPurc1DR = asstDetailVos.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    BeanUtils.copyProperties(cwPurc1CR,cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setReimDetailId(erpReimDetailVo.getId());
                    cr.setDeptCode(item.getDeptCode());
                    cr.setDeptName(item.getDeptName());
                    cr.setActigAmt(item.getAmt());
                    cr.setAsstNo(asstNo++);
                    cr.setAbst(item.getReimAbst());
                    assts.add(cr);
                    //如果是现金支付，则没有付款证明文件
                    if (StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_1)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc1DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(item.getAmt());
                        dr.setAsstNo(asstNo++);
                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAbst(item.getReimAbst());
                        assts.add(dr);
                    }
                }

                //支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
                if (StringUtils.isEmpty(erpReimDetailVo.getPayMethod()) || StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_0)) {
                    //贷方 (通过付款证明文件生成)  贷方没有分类取零星采购任一即可
                    EcsReimFixedAsstDetailVo cwPurc1DR = purc1.get(0).getFixedAsstDetails().stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    for (int i = 0; i < payReceiptVos.size(); i++) {
                        ErpReimPayReceiptVo vo = payReceiptVos.get(i);
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc1DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAsstNo(asstNo++);
                        dr.setActigAmt(vo.getPayAmt());
                        dr.setAbst(vo.getPayDate() + "付零星采购费用");
                        assts.add(dr);
                    }
                }

                //获取总务低值易耗品的零星采购配置项,筛选后会有行政和临床的，任取即可（预算会计相同）
                Optional<EcsReimFixedAsstVo> ysAsstVo = purc1.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.ZWDZYH)).findFirst();
                if (ysAsstVo.isEmpty()) {
                    log.error(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                    throw new AppException(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                }
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails = ysAsstVo.get().getFixedAsstDetails();
                //获取其他总务材料的零星采购配置项,筛选后会有行政和临床的，任取即可（预算会计相同）
                Optional<EcsReimFixedAsstVo> ysAsst2Vo = purc1.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.QTZWCL)).findFirst();
                if (ysAsst2Vo.isEmpty()) {
                    log.error(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                    throw new AppException(String.format("不存在当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                }
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails2 = ysAsst2Vo.get().getFixedAsstDetails();
                //预算会计-借，分为：总务低值易耗/其他总务材料  预算会计不区分科室，任意取采购的预算会计即可
                //总务低值易耗品-借
                List<ErpReimItemDetail> zwdzyh = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.ZWDZYH)).collect(Collectors.toList());
                //有总务低值易耗品，则生成对应预算-借
                if (CollectionUtil.isNotEmpty(zwdzyh)) {
                    EcsReimFixedAsstDetailVo ysPurc1CR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc1CR,yscr);
                    yscr.setSupType(MedConst.TYPE_1);
                    yscr.setReimDetailId(erpReimDetailVo.getId());
                    yscr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO, BigDecimal::add));
                    yscr.setAsstNo(asstNo++);
                    yscr.setAbst("零星采购-总务低值易耗品");
                    assts.add(yscr);
                }
                //其他总务材料-借
                List<ErpReimItemDetail> qtzwcl = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.QTZWCL)).collect(Collectors.toList());
                //有其他总务材料，则生成对应预算-借
                if (CollectionUtil.isNotEmpty(qtzwcl)) {
                    EcsReimFixedAsstDetailVo ysPurc2CR = fixedAsstDetails2.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr2 = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc2CR,yscr2);
                    yscr2.setSupType(MedConst.TYPE_1);
                    yscr2.setReimDetailId(erpReimDetailVo.getId());
                    yscr2.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO, BigDecimal::add));
                    yscr2.setAsstNo(asstNo++);
                    yscr2.setAbst("零星采购-总务低值易耗品");
                    assts.add(yscr2);
                }
                //预算贷方
                EcsReimFixedAsstDetailVo ysPurc1DR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                ErpReimAsstDto ysdr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysPurc1DR,ysdr);
                ysdr.setSupType(MedConst.TYPE_1);
                ysdr.setReimDetailId(erpReimDetailVo.getId());
                ysdr.setActigAmt(items.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO, BigDecimal::add));
                ysdr.setAsstNo(asstNo++);
                ysdr.setAbst("零星采购");
                assts.add(ysdr);
            } else if (StringUtils.equals(erpReimDetailVo.getType(),MedConst.TYPE_10)) {
                //物资采购  生成总金额凭证
//                List<EcsReimFixedAsstDetailVo> asstDetailVos = purc2.get(0).getFixedAsstDetails();
                //获取物资采购项目
                List<ErpReimItemDetail> items = erpReimItemDetails.stream()
                        .filter(e -> e.getReimDetailId().longValue() == erpReimDetailVo.getId().intValue()).collect(Collectors.toList());
                //其他总务材料
                List<ErpReimItemDetail> qtzwcl = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.QTZWCL)).collect(Collectors.toList());
                //总务低值易耗品
                List<ErpReimItemDetail> zwdzyh = items.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.ZWDZYH)).collect(Collectors.toList());

                //其他总务材料-配置科目
                Optional<EcsReimFixedAsstVo> wzQTcfg = purc2.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.QTZWCL)).findFirst();
                if (wzQTcfg.isEmpty()) {
                    log.error(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                    throw new AppException(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.QTZWCL));
                }
                //其他总务材料科目信息
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails = wzQTcfg.get().getFixedAsstDetails();
                //总务低值易耗品-配置科目
                Optional<EcsReimFixedAsstVo> wzZWcfg = purc2.stream().filter(e -> StringUtils.equals(e.getEconFunSubCode(), ErpConstants.ZWDZYH)).findFirst();
                if (wzZWcfg.isEmpty()) {
                    log.error(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                    throw new AppException(String.format("不存在物资采购当前类型对应的配置科目-经济科目%s",ErpConstants.ZWDZYH));
                }
                //总务低值易耗品科目信息
                List<EcsReimFixedAsstDetailVo> fixedAsstDetails1 = wzZWcfg.get().getFixedAsstDetails();
                //有其他总务材料，生成凭证项
                if (CollectionUtil.isNotEmpty(qtzwcl)) {
                    //财务-借
                    EcsReimFixedAsstDetailVo cwPurc2CR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    EcsReimFixedAsstDetailVo cwPurc2DR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwPurc2CR,cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setReimDetailId(erpReimDetailVo.getId());
                    cr.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    cr.setAsstNo(asstNo++);
                    cr.setAbst("物资采购-其他总务材料");
                    assts.add(cr);
                    //如果是现金支付，则没有付款证明文件
                    if (StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_1)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc2DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                        dr.setAsstNo(asstNo++);
                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAbst("物资采购-其他总务材料");
                        assts.add(dr);
                    }
                    //预算-借
                    EcsReimFixedAsstDetailVo ysPurc2CR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc2CR,yscr);
                    yscr.setSupType(MedConst.TYPE_1);
                    yscr.setReimDetailId(erpReimDetailVo.getId());
                    yscr.setActigAmt(qtzwcl.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    yscr.setAsstNo(asstNo++);
                    yscr.setAbst("物资采购-其他总务材料");
                    assts.add(yscr);
                }
                //有总务低值易耗品项目，生成凭证项
                if (CollectionUtil.isNotEmpty(zwdzyh)) {
                    //财务-借
                    EcsReimFixedAsstDetailVo cwPurc2CR = fixedAsstDetails1.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    EcsReimFixedAsstDetailVo cwPurc2DR = fixedAsstDetails1.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwPurc2CR,cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setReimDetailId(erpReimDetailVo.getId());
                    cr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    cr.setAsstNo(asstNo++);
                    cr.setAbst("物资采购-总务低值易耗品");
                    assts.add(cr);
                    //如果是现金支付，则没有付款证明文件
                    if (StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_1)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc2DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                        dr.setAsstNo(asstNo++);
                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAbst("物资采购-总务低值易耗品");
                        assts.add(dr);
                    }
                    //预算-借
                    EcsReimFixedAsstDetailVo ysPurc2CR = fixedAsstDetails1.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                    ErpReimAsstDto yscr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysPurc2CR,yscr);
                    yscr.setSupType(MedConst.TYPE_1);
                    yscr.setReimDetailId(erpReimDetailVo.getId());
                    yscr.setActigAmt(zwdzyh.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                    yscr.setAsstNo(asstNo++);
                    yscr.setAbst("物资采购-总务低值易耗品");
                    assts.add(yscr);
                }
                //支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
                if (StringUtils.isEmpty(erpReimDetailVo.getPayMethod()) || StringUtils.equals(erpReimDetailVo.getPayMethod(),MedConst.TYPE_0)) {
                    //财务-贷 付款证明文件生成
                    EcsReimFixedAsstDetailVo cwPurc2DR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    for (int i = 0; i < payReceiptVos.size(); i++) {
                        ErpReimPayReceiptVo vo = payReceiptVos.get(i);
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwPurc2DR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setReimDetailId(erpReimDetailVo.getId());
                        dr.setAsstNo(asstNo++);
                        dr.setActigAmt(vo.getPayAmt());
                        dr.setAbst(vo.getPayDate() + "付物资采购费用");
                        assts.add(dr);
                    }
                }

                //预算-贷
                EcsReimFixedAsstDetailVo ysPurc2DR = fixedAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                ErpReimAsstDto ysdr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysPurc2DR,ysdr);
                ysdr.setSupType(MedConst.TYPE_1);
                ysdr.setReimDetailId(erpReimDetailVo.getId());
                ysdr.setAsstNo(asstNo++);
                ysdr.setActigAmt(items.stream().map(e -> e.getAmt()).reduce(BigDecimal.ZERO,BigDecimal::add));
                ysdr.setAbst("物资采购");
                assts.add(ysdr);
            }
        }*/
        log.info("------------------end生成采购assts-------------------------");
        return assts;
    }


    @Override
    public void saveSalaryVcrAssts(ErpReimSalaryTaskDto dto) {
        //通过工资任务id查询工资任务
        LambdaQueryWrapper<ErpReimSalaryTaskDto> salaryTaskWrapper = Wrappers.lambdaQuery(ErpReimSalaryTaskDto.class);
        salaryTaskWrapper.eq(ErpReimSalaryTaskDto::getSalaryId, dto.getSalaryId());
        List<ErpReimSalaryTaskDto> salaryTaskDtos = erpReimSalaryTaskReadMapper.selectList(salaryTaskWrapper);

        List<Integer> delIds = new ArrayList<>();

        List<ErpReimAsstDto> insertAssts = new ArrayList<>();
        if (StringUtils.equals(dto.getType(), ErpConstants.SALARY) || StringUtils.equals(dto.getType(), ErpConstants.BUSINESS_PAYMENT)) {

            //工资计提，企业缴纳
            //工资计提、企业代扣凭证生成
            //查询工资计提配置表
            LambdaQueryWrapper<ErpVcrSalaryConfigDto> configWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDto.class);
            configWrapper.eq(ErpVcrSalaryConfigDto::getSalaryType, ErpConstants.SALARY);
            List<ErpVcrSalaryConfigDto> configDtos = erpVcrSalaryConfigReadMapper.selectList(configWrapper);
            //查询工资计提配置表明细
            List<Integer> salaryConfigIds = configDtos.stream().map(ErpVcrSalaryConfigDto::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ErpVcrSalaryConfigDetailDto> configDetailWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDetailDto.class);
            configDetailWrapper.in(ErpVcrSalaryConfigDetailDto::getSalaryConfigId, salaryConfigIds);
            List<ErpVcrSalaryConfigDetailDto> configDetailDtos = erpVcrSalaryConfigDetailReadMapper.selectList(configDetailWrapper);
            //查询企业配置表
            LambdaQueryWrapper<ErpVcrSalaryConfigDto> entpWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDto.class);
            entpWrapper.eq(ErpVcrSalaryConfigDto::getSalaryType, ErpConstants.BUSINESS_PAYMENT);
            List<ErpVcrSalaryConfigDto> entpDtos = erpVcrSalaryConfigReadMapper.selectList(entpWrapper);
            //查询企业配置表明细
            List<Integer> entpConfigIds = entpDtos.stream().map(ErpVcrSalaryConfigDto::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ErpVcrSalaryConfigDetailDto> entpDetailWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDetailDto.class);
            entpDetailWrapper.in(ErpVcrSalaryConfigDetailDto::getSalaryConfigId, entpConfigIds);
            List<ErpVcrSalaryConfigDetailDto> entpConfigDetailDtos = erpVcrSalaryConfigDetailReadMapper.selectList(entpDetailWrapper);
            //hrp-用友科室映射(用于使用用友部门前缀判断科室类型)
            CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());

            if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
                throw new AppException("当前未维护科室关系映射!");
            }
            List<HrmOrgAgencyMapVo> orgAgencyMapVos = result.getData();
            //科室信息
            HrmOrgDto hrmOrgDto = new HrmOrgDto();
            hrmOrgDto.setActiveFlag(MedConst.TYPE_1);
            CommonResult<List<HrmOrgVo>> hrmOrgResult = hrmOrgFeignService.queryOrgFramework(hrmOrgDto);
            List<HrmOrgVo> hrmOrgs = hrmOrgResult.getData();
            if (CollectionUtil.isEmpty(hrmOrgs)) {
                throw new AppException("科室信息不存在");
            }
            Map<String, String> orgToNameMap = hrmOrgs.stream().collect(Collectors.toMap(HrmOrgVo::getOrgId, HrmOrgVo::getOrgName));
//            Map<String, String> orgToTypeMap = hrmOrgs.stream().collect(Collectors.toMap(HrmOrgVo::getOrgId, HrmOrgVo::getOrgType));

            //工资计提任务
            List<ErpReimSalaryTaskDto> salaryTasks = salaryTaskDtos.stream()
                    .filter(e -> StringUtils.equals(e.getType(), ErpConstants.SALARY))
                    .collect(Collectors.toList());
            if (salaryTasks.size() > 1) {
                throw new AppException("存在多个相同待生成凭证工资任务");
            }
            ErpReimSalaryTaskDto salary = salaryTasks.get(0);
            delIds.add(salary.getId());
            //查询工资类型任务的明细
            LambdaQueryWrapper<ErpReimSalaryTaskDetailDto> salaryTaskDetailWrapper = Wrappers.lambdaQuery(ErpReimSalaryTaskDetailDto.class);
            salaryTaskDetailWrapper.eq(ErpReimSalaryTaskDetailDto::getTaskId, salary.getId())
                    .orderByAsc(ErpReimSalaryTaskDetailDto::getOrgId);
            List<ErpReimSalaryTaskDetailDto> salaryTaskDetails = erpReimSalaryTaskDetailReadMapper.selectList(salaryTaskDetailWrapper);
            //按照工资明细生成凭证

            int asstNo = 1;
            for (int i = 0; i < salaryTaskDetails.size(); i++) {
                ErpReimSalaryTaskDetailDto detail = salaryTaskDetails.get(i);

                //科室类型code   注： 科室不会为空，为空的数据在推送时已归为医务部
                /*String s = orgToTypeMap.get(detail.getOrgId());
                if (StringUtil.isEmpty(s)) {
                    log.error("科室类型不能为空：科室{]", detail.getOrgId());
                    continue;
                }*/
                //科室类型  转换为用友dept 前缀判断科室类型
                String deptType = getDeptType(detail.getOrgId(),orgAgencyMapVos);               // 1：临床医技医辅 2：行政
                if (StringUtils.isEmpty(deptType)) {
                    throw new AppException(String.format("不存在当前科室类型-科室%s", detail.getOrgId()));
                }

                //人员类型
                String empType = getSalaryEmpType(detail.getEmpType());     //1：在编 2：招聘 3：临聘
                if (StringUtils.isEmpty(empType)) {
                    throw new AppException(String.format("不存在当前人员类型-科室%s,人员类型%s", detail.getOrgId(), detail.getEmpType()));
                }
                //查询工资配置项
                Optional<ErpVcrSalaryConfigDto> first = configDtos.stream().filter(e -> StringUtils.equals(e.getDeptType(), deptType)
                        && StringUtils.equals(e.getEmpType(), empType)
                        && StringUtils.equals(e.getReimName(), detail.getReimName())
                ).findFirst();
                if (first.isEmpty()) {
                    throw new AppException(String.format("不存在当前科室配置项-科室类型 %s-人员类型%s-工资项目%s", deptType, empType, detail.getReimName()));
                }
                ErpVcrSalaryConfigDto config = first.get();
                //获取配置项明细
                List<ErpVcrSalaryConfigDetailDto> configDetails = configDetailDtos.stream()
                        .filter(e -> e.getSalaryConfigId().equals(config.getId())).collect(Collectors.toList());
                //按照配置明细生成对应凭证
                //财务会计科目
                ErpVcrSalaryConfigDetailDto cwCR = configDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                ErpVcrSalaryConfigDetailDto cwDR = configDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                //财务借
                ErpReimAsstDto cr = new ErpReimAsstDto();
                BeanUtils.copyProperties(cwCR, cr);
                cr.setSupType(MedConst.TYPE_3);     //1：报销 2：药品 3：工资凭证（工资计提和企业类）
                cr.setDeptCode(detail.getOrgId());
                cr.setDeptName(orgToNameMap.get(detail.getOrgId()));
                cr.setReimDetailId(salary.getId()); //任务id
                cr.setAsstNo(asstNo);
                cr.setActigAmt(detail.getReimAmt());
                String abst = detail.getReimDesc() + "-" + detail.getEmpType();
                cr.setAbst(abst);
                insertAssts.add(cr);
                // 财务贷
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(cwDR, dr);
                dr.setSupType(MedConst.TYPE_3);
                dr.setReimDetailId(salary.getId()); //任务id
                dr.setAsstNo(asstNo);
                dr.setActigAmt(detail.getReimAmt());
                dr.setAbst(abst);
                asstNo++;
                insertAssts.add(dr);
            }
            // 更新工资计提工资任务状态
            LambdaUpdateWrapper<ErpReimSalaryTaskDto> updSalaryWrapper = Wrappers.lambdaUpdate(ErpReimSalaryTaskDto.class);
            updSalaryWrapper.eq(ErpReimSalaryTaskDto::getId, salary.getId())
                    .set(ErpReimSalaryTaskDto::getReimFlag, MedConst.TYPE_1);
            erpReimSalaryTaskWriteMapper.update(null, updSalaryWrapper);

            //未生成的企业四险两金计提任务
            List<ErpReimSalaryTaskDto> businessTasks = salaryTaskDtos.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.BUSINESS_PAYMENT)
                    && StringUtils.equals(e.getReimFlag(), MedConst.TYPE_0)).collect(Collectors.toList());
            if (businessTasks.size() > 1) {
                throw new AppException("存在多个相同待生成凭证企业计提任务");
            }
            ErpReimSalaryTaskDto business = businessTasks.get(0);
            delIds.add(business.getId());
            //查询企业类型任务明细
            LambdaQueryWrapper<ErpReimSalaryTaskDetailDto> businessTaskDetailWrapper = Wrappers.lambdaQuery(ErpReimSalaryTaskDetailDto.class);
            businessTaskDetailWrapper.eq(ErpReimSalaryTaskDetailDto::getTaskId, business.getId())
                    .orderByAsc(ErpReimSalaryTaskDetailDto::getOrgId);
            List<ErpReimSalaryTaskDetailDto> businessTaskDetails = erpReimSalaryTaskDetailReadMapper.selectList(businessTaskDetailWrapper);
            //按照企业缴纳明细生成凭证
            asstNo = 1;
            for (int i = 0; i < businessTaskDetails.size(); i++) {
                ErpReimSalaryTaskDetailDto detail = businessTaskDetails.get(i);

                //科室类型  直接通过deptCode的前缀判断科室类型
                String deptType = getDeptType(detail.getOrgId(),orgAgencyMapVos);               // 1：临床医技医辅 2：行政
                if (StringUtils.isEmpty(deptType)) {
                    throw new AppException(String.format("不存在当前科室类型-科室%s", detail.getOrgId()));
                }

                //人员类型
                String empType = getSalaryEmpType(detail.getEmpType());         //1：在编 2：招聘 3：临聘
                if (StringUtils.isEmpty(empType)) {
                    log.error("不存在当前人员类型-{}", detail.getEmpType());
                    throw new AppException("不存在当前人员类型");
                }
                //查询企业缴纳配置项
                Optional<ErpVcrSalaryConfigDto> first = entpDtos.stream().filter(e -> StringUtils.equals(e.getEmpType(), empType)
                        && StringUtils.equals(e.getReimName(), detail.getReimName()) && StringUtils.equals(e.getDeptType(),deptType)
                ).findFirst();
                if (first.isEmpty()) {
                    log.error("不存在企业缴纳配置项-empType:{},reimName:{}", empType, detail.getReimName());
                    throw new AppException("不存在企业缴纳配置项");
                }
                ErpVcrSalaryConfigDto config = first.get();
                //获取配置项明细
                List<ErpVcrSalaryConfigDetailDto> configDetails = entpConfigDetailDtos.stream()
                        .filter(e -> e.getSalaryConfigId().equals(config.getId())).collect(Collectors.toList());
                //按照配置项明细生成对应凭证
                //财务会计科目
                ErpVcrSalaryConfigDetailDto cwCR = configDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                ErpVcrSalaryConfigDetailDto cwDR = configDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                //财务借
                ErpReimAsstDto cr = new ErpReimAsstDto();
                BeanUtils.copyProperties(cwCR, cr);
                cr.setSupType(MedConst.TYPE_3);     //1：报销 2：药品 3：工资凭证（工资计提和企业类） 用于关联表
                cr.setDeptCode(detail.getOrgId());
                cr.setDeptName(orgToNameMap.get(detail.getOrgId()));
                cr.setReimDetailId(business.getId());     //任务id
                cr.setAsstNo(asstNo);
                cr.setActigAmt(detail.getReimAmt());
                String abst = detail.getReimDesc() + "-" + detail.getEmpType();
                cr.setAbst(abst);
                insertAssts.add(cr);
                //财务贷
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(cwDR, dr);
                dr.setSupType(MedConst.TYPE_3);
                dr.setReimDetailId(business.getId()); //任务id
                dr.setAsstNo(asstNo);
                dr.setActigAmt(detail.getReimAmt());
                dr.setAbst(abst);
                asstNo++;
                insertAssts.add(dr);
            }
            // 更新企业扣缴任务状态
            LambdaUpdateWrapper<ErpReimSalaryTaskDto> updEntpWrapper = Wrappers.lambdaUpdate(ErpReimSalaryTaskDto.class);
            updEntpWrapper.eq(ErpReimSalaryTaskDto::getId, business.getId())
                    .set(ErpReimSalaryTaskDto::getReimFlag, MedConst.TYPE_1);
            erpReimSalaryTaskWriteMapper.update(null, updEntpWrapper);
            log.info("" + insertAssts.size());
        } else {
            //个人代扣
            //工资凭证生成
            //工资不会多个进行报销
            //通过id查询个人代扣工资任务
            /*LambdaQueryWrapper<ErpReimSalaryTaskDto> salaryTaskWrapper = Wrappers.lambdaQuery(ErpReimSalaryTaskDto.class);
            salaryTaskWrapper.eq(ErpReimSalaryTaskDto::getId,dto.getId());
            ErpReimSalaryTaskDto indiReimSalaryTaskDto = erpReimSalaryTaskReadMapper.selectOne(salaryTaskWrapper);*/
            /*List<ErpReimSalaryTaskDto> indivdualTasks = salaryTaskDtos.stream().filter(e -> StringUtils.equals(e.getType(), ErpConstants.INDIVDUAL_REDUCE)
                    && StringUtils.equals(e.getReimFlag(), MedConst.TYPE_1)).collect(Collectors.toList());
            if (indivdualTasks.size() > 1) {
                throw new AppException("存在多个相同待生成凭证个人代扣任务");
            }
            ErpReimSalaryTaskDto indiReimSalaryTaskDto = indivdualTasks.get(0);*/

            //查询salaryId的所有工资任务
            /*LambdaQueryWrapper<ErpReimSalaryTaskDto> allSalaryTaskWrapper = Wrappers.lambdaQuery(ErpReimSalaryTaskDto.class);
            allSalaryTaskWrapper.eq(ErpReimSalaryTaskDto::getSalaryId,indiReimSalaryTaskDto.getSalaryId());
            List<ErpReimSalaryTaskDto> allSalaryTasks = erpReimSalaryTaskReadMapper.selectList(allSalaryTaskWrapper);*/

            // 判断工资计提是否生成
            //查询工资计提数据
            List<ErpReimSalaryTaskDto> gzjtTasks = salaryTaskDtos.stream()
                    .filter(e -> StringUtils.equals(e.getType(), ErpConstants.SALARY))
                    .collect(Collectors.toList());

            ErpReimAsstDto jtParam = new ErpReimAsstDto();
            jtParam.setSupType(MedConst.TYPE_3);
            jtParam.setReimDetailId(gzjtTasks.get(0).getId());
            List<ErpReimAsstVo> jtAssts = erpVcrDetailReadMapper.queryReimAsstVoList(jtParam);
            if (CollectionUtil.isEmpty(jtAssts)) {
                log.error("工资计提未生成，请先生成工资计提数据");
                throw new AppException("工资计提未生成，请先生成工资计提数据");
            }
            //只有个人代扣从报销生成，其他直接从任务生成凭证
            delIds.add(dto.getId());
            //个人代扣  1:工资计提 2：个人代扣 3：企业缴纳
            //查询工资计提配置表
            LambdaQueryWrapper<ErpVcrSalaryConfigDto> configWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDto.class);
            configWrapper.eq(ErpVcrSalaryConfigDto::getSalaryType, ErpConstants.SALARY);
            List<ErpVcrSalaryConfigDto> salaryConfigDtos = erpVcrSalaryConfigReadMapper.selectList(configWrapper);
            //查询工资计提配置表明细
            List<Integer> salaryConfigIds = salaryConfigDtos.stream().map(ErpVcrSalaryConfigDto::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ErpVcrSalaryConfigDetailDto> configDetailWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDetailDto.class);
            configDetailWrapper.in(ErpVcrSalaryConfigDetailDto::getSalaryConfigId, salaryConfigIds);
            List<ErpVcrSalaryConfigDetailDto> salaryConfigDetailDtos = erpVcrSalaryConfigDetailReadMapper.selectList(configDetailWrapper);

            //查询个人扣减配置表
            LambdaQueryWrapper<ErpVcrSalaryConfigDto> individualWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDto.class);
            individualWrapper.eq(ErpVcrSalaryConfigDto::getSalaryType, ErpConstants.INDIVDUAL_REDUCE);
            List<ErpVcrSalaryConfigDto> indiConfigDtos = erpVcrSalaryConfigReadMapper.selectList(individualWrapper);
            //查询个人扣减配置表明细
            List<Integer> indiConfigIds = indiConfigDtos.stream().map(ErpVcrSalaryConfigDto::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ErpVcrSalaryConfigDetailDto> indiConfigDetailWrapper = Wrappers.lambdaQuery(ErpVcrSalaryConfigDetailDto.class);
            indiConfigDetailWrapper.in(ErpVcrSalaryConfigDetailDto::getSalaryConfigId, indiConfigIds);
            List<ErpVcrSalaryConfigDetailDto> indiConfigDetailDtos = erpVcrSalaryConfigDetailReadMapper.selectList(indiConfigDetailWrapper);
            //1. 工资总额
            BigDecimal salaryTotal = BigDecimal.ZERO;
            //获取工资基本工资任务id
            Optional<ErpReimSalaryTaskDto> bstOpt = salaryTaskDtos.stream().filter(e -> StringUtils.equals(e.getType(), MedConst.TYPE_1)).findFirst();
            if (bstOpt.isEmpty()) {
                log.error("工资计提任务不存在,salaryId:{}", dto.getSalaryId());
                throw new AppException("工资计提任务不存在");
            }
            ErpReimSalaryTaskDto baseTask = bstOpt.get();
            if (!StringUtils.equals(baseTask.getReimFlag(), MedConst.TYPE_1)) {
                log.error("工资计提任务未生成凭证，工资计提任务id:{}", baseTask.getId());
                throw new AppException("工资计提任务未生成凭证");
            }
            //查询工资计提辅助项
            ErpReimAsstDto asstParam = new ErpReimAsstDto();
            asstParam.setSupType(MedConst.TYPE_3);
            asstParam.setReimDetailId(baseTask.getId());
            List<ErpReimAsstVo> erpReimAsstVos = erpVcrDetailReadMapper.queryReimAsstVoList(asstParam);
            //汇总金额  应付职工薪酬-基本工资（含离退休费）：220101 应付职工薪酬-国家统一规定的津贴补贴：220102 应付职工薪酬-规范津贴补贴（绩效工资）：220103
            //1.工资总额-基本工资汇总 220101  财务-贷
            int asstNo = 1;
            List<ErpReimAsstVo> indiBaseSalary = erpReimAsstVos.stream().filter(e -> StringUtils.equals(e.getActigSubCode(), "220101")
                    && StringUtils.equals(e.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(e.getActigAmtType(), MedConst.TYPE_2)).collect(Collectors.toList());
            //计算基本工资总额
            BigDecimal baseTotal = indiBaseSalary.stream().map(ErpReimAsstVo::getActigAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            salaryTotal = salaryTotal.add(baseTotal);
            //获取配置项
            Optional<ErpVcrSalaryConfigDto> opt1 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.SALARY_TOTAL_BASE)).findFirst();
            if (opt1.isEmpty()) {
                log.error("工资总额-基本工资配置项不存在-reimName:{}", ErpConstants.SALARY_TOTAL_BASE);
                throw new AppException("工资总额-基本工资配置项不存在");
            }
            ErpVcrSalaryConfigDto baseConfig = opt1.get();
            //获取配置项明细
            List<ErpVcrSalaryConfigDetailDto> baseConfigDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(baseConfig.getId()))
                    .collect(Collectors.toList());
            //将贷方的科目作为基本工资的借方
            ErpVcrSalaryConfigDetailDto baseCwDR = baseConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            ErpReimAsstDto baseDr = new ErpReimAsstDto();
            BeanUtils.copyProperties(baseCwDR, baseDr);
            baseDr.setSupType(MedConst.TYPE_3);
            baseDr.setReimDetailId(dto.getId());
            baseDr.setAsstNo(asstNo++);
            baseDr.setActigAmt(baseTotal);
            baseDr.setAbst("工资总额-应付职工薪酬-基本工资（含离退休费）");
            baseDr.setActigSys(MedConst.TYPE_1);
            baseDr.setActigAmtType(MedConst.TYPE_1);        //生成的是财务借方金额
            insertAssts.add(baseDr);
            //2. 工资总额-国家统一规定的津贴补贴
            List<ErpReimAsstVo> indiAllowance = erpReimAsstVos.stream().filter(e -> StringUtils.equals(e.getActigSubCode(), "220102")
                    && StringUtils.equals(e.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(e.getActigAmtType(), MedConst.TYPE_2)).collect(Collectors.toList());
            //计算国家统一规定的津贴补贴总额
            BigDecimal allowanceTotal = indiAllowance.stream().map(ErpReimAsstVo::getActigAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            salaryTotal = salaryTotal.add(allowanceTotal);
            //获取配置项
            Optional<ErpVcrSalaryConfigDto> opt2 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.SALARY_TOTAL_ALLOWANCE))
                    .findFirst();
            if (opt2.isEmpty()) {
                log.error("工资总额-国家统一规定的津贴补贴-reimName:{}", ErpConstants.SALARY_TOTAL_ALLOWANCE);
                throw new AppException("工资总额-国家统一规定的津贴补贴");
            }
            ErpVcrSalaryConfigDto allowanceConfig = opt2.get();
            //获取配置项明细
            List<ErpVcrSalaryConfigDetailDto> allowanceConfigDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(allowanceConfig.getId()))
                    .collect(Collectors.toList());
            //获取个人扣缴国家统一规定的津贴补贴的借方
            ErpVcrSalaryConfigDetailDto allCwDR = allowanceConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            ErpReimAsstDto allowDr = new ErpReimAsstDto();
            BeanUtils.copyProperties(allCwDR, allowDr);
            allowDr.setSupType(MedConst.TYPE_3);
            allowDr.setReimDetailId(dto.getId());
            allowDr.setAsstNo(asstNo++);
            allowDr.setActigAmt(allowanceTotal);
            allowDr.setAbst("工资总额-应付职工薪酬-国家统一规定的津贴补贴");
            allowDr.setActigSys(MedConst.TYPE_1);
            allowDr.setActigAmtType(MedConst.TYPE_1);        //生成的是财务借方金额
            insertAssts.add(allowDr);
            //3. 工资总额-规范津贴补贴
            List<ErpReimAsstVo> indiPerformSal = erpReimAsstVos.stream().filter(e -> StringUtils.equals(e.getActigSubCode(), "220103")
                    && StringUtils.equals(e.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(e.getActigAmtType(), MedConst.TYPE_2)).collect(Collectors.toList());
            //计算规范津贴补贴总额
            BigDecimal performSalTotal = indiPerformSal.stream().map(ErpReimAsstVo::getActigAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            salaryTotal = salaryTotal.add(performSalTotal);
            //获取配置项
            Optional<ErpVcrSalaryConfigDto> opt3 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.SALARY_TOTAL_PERFORM_SAL))
                    .findFirst();
            if (opt3.isEmpty()) {
                log.error("工资总额-规范津贴补贴-reimName:{}", ErpConstants.SALARY_TOTAL_PERFORM_SAL);
                throw new AppException("工资总额-规范津贴补贴");
            }
            ErpVcrSalaryConfigDto performSalConfig = opt3.get();
            //获取配置项明细
            List<ErpVcrSalaryConfigDetailDto> performSalConfigDetils = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(performSalConfig.getId()))
                    .collect(Collectors.toList());
            //获取个人扣缴规范津贴补贴的借方
            ErpVcrSalaryConfigDetailDto performSalCwDR = performSalConfigDetils.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            ErpReimAsstDto performSalDr = new ErpReimAsstDto();
            BeanUtils.copyProperties(performSalCwDR, performSalDr);
            performSalDr.setSupType(MedConst.TYPE_3);
            performSalDr.setReimDetailId(dto.getId());
            performSalDr.setAsstNo(asstNo++);
            performSalDr.setActigAmt(performSalTotal);
            performSalDr.setAbst("工资总额-应付职工薪酬-规范津贴补贴(绩效工资)");
            performSalDr.setActigSys(MedConst.TYPE_1);
            performSalDr.setActigAmtType(MedConst.TYPE_1);        //生成的是财务借方金额
            insertAssts.add(performSalDr);
            //4.特殊往来账
            //查询维修班的人员的工资信息
            HrpSalaryTask salaryParam = new HrpSalaryTask();
            salaryParam.setId(dto.getSalaryId());
            CommonResult<List<EmployeeReallySalaryVo>> result = hrmEmployeeSalaryFeignService.queryReallySalaryDetail(salaryParam);
            if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
                throw new AppException("在职职工工资明细数据不存在");
            }
            List<EmployeeReallySalaryVo> data = result.getData();
            //筛选维修班人员中的11个人 工号：0175 0580 0601 0605 0633 0666 0669 1261 1262 1404 1406 维修班：528004
            List<String> wxCodes = Arrays.asList("0175", "0580", "0601", "0605", "0633", "0666", "0669", "1261", "1262", "1404", "1406");
            List<EmployeeReallySalaryVo> wxEmpInfo = data.stream().filter(e -> StringUtils.equals(e.getOrgId(), "528004") && wxCodes.contains(e.getEmpCode())).collect(Collectors.toList());
            BigDecimal wxEmpInfoTotal = wxEmpInfo.stream()
                    .map(e -> e.getShouldPayTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            salaryTotal = salaryTotal.add(wxEmpInfoTotal);
            //筛选总务科-吴思琪   工号 1242 部门code 528001
            List<EmployeeReallySalaryVo> zwEmpInfo = data.stream().filter(e -> StringUtils.equals(e.getOrgId(), "528001") && StringUtils.equals(e.getEmpCode(), "1242")).collect(Collectors.toList());
            BigDecimal zwEmpInfoTotal = zwEmpInfo.stream()
                    .map(e -> e.getShouldPayTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            salaryTotal = salaryTotal.add(zwEmpInfoTotal);
            //筛选住院收费室-罗亚婕 工号 1346  部门code 402002
            List<EmployeeReallySalaryVo> zyEmpInfo = data.stream().filter(e -> StringUtils.equals(e.getOrgId(), "402002") && StringUtils.equals(e.getEmpCode(), "1346")).collect(Collectors.toList());
            BigDecimal zyEmpInfoTotal = zyEmpInfo.stream()
                    .map(e -> e.getShouldPayTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            salaryTotal = salaryTotal.add(zyEmpInfoTotal);
            //筛选单采血浆站人员 单采血浆站：405001
            List<EmployeeReallySalaryVo> dqEmpInfo = data.stream().filter(e -> StringUtils.equals(e.getOrgId(), "405001")).collect(Collectors.toList());
            BigDecimal dqEmpInfoTotal = dqEmpInfo.stream()
                    .map(e -> e.getShouldPayTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            salaryTotal = salaryTotal.add(dqEmpInfoTotal);
            //获取特殊往来账配置项
            Optional<ErpVcrSalaryConfigDto> opt6 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.SPECIAL_CORRS_ACCOUNT))
                    .findFirst();
            //获取特殊单采血浆配置项
            Optional<ErpVcrSalaryConfigDto> opt7 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.SPECIAL_PLASMA_STATION))
                    .findFirst();
            if (opt6.isEmpty()) {
                log.error("工资总额-特殊往来账配置不存在-reimName:{}", ErpConstants.OFFSET_ACCOUNT);
                throw new AppException("工资总额-特殊往来账配置不存在");
            }
            if (opt7.isEmpty()) {
                log.error("工资总额-特殊单采血浆站配置不存在-reimName:{}", ErpConstants.OFFSET_ACCOUNT);
                throw new AppException("工资总额-特殊单采血浆站配置不存在");
            }
            ErpVcrSalaryConfigDto spCorrAccCfg = opt6.get();
            ErpVcrSalaryConfigDto spPlasmaCfg = opt7.get();
            //获取特殊往来账明细
            List<ErpVcrSalaryConfigDetailDto> spCorrAccCfgDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(spCorrAccCfg.getId()))
                    .collect(Collectors.toList());
            //获取特殊单采血浆站明细
            List<ErpVcrSalaryConfigDetailDto> spPlasmaCfgDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(spPlasmaCfg.getId()))
                    .collect(Collectors.toList());
            //获取特殊往来账-借方
            ErpVcrSalaryConfigDetailDto spCorrAccCR = spCorrAccCfgDetails.stream()
                    .filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            //获取特殊单采血浆站-借方
            ErpVcrSalaryConfigDetailDto spPlasmaCR = spPlasmaCfgDetails.stream()
                    .filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            for (int i = 0; i < wxEmpInfo.size(); i++) {
                EmployeeReallySalaryVo info = wxEmpInfo.get(i);
                ErpReimAsstDto scaCr = new ErpReimAsstDto();
                BeanUtils.copyProperties(spCorrAccCR, scaCr);
                scaCr.setSupType(MedConst.TYPE_3);
                scaCr.setReimDetailId(dto.getId());
                scaCr.setAsstNo(asstNo++);
                scaCr.setActigAmt(info.getShouldPayTotal());
                scaCr.setAbst("维修班人员-特殊往来账");
                insertAssts.add(scaCr);
            }
            for (int i = 0; i < zwEmpInfo.size(); i++) {
                EmployeeReallySalaryVo info = zwEmpInfo.get(i);
                ErpReimAsstDto scaCr = new ErpReimAsstDto();
                BeanUtils.copyProperties(spCorrAccCR, scaCr);
                scaCr.setSupType(MedConst.TYPE_3);
                scaCr.setReimDetailId(dto.getId());
                scaCr.setAsstNo(asstNo++);
                scaCr.setActigAmt(info.getShouldPayTotal());
                scaCr.setAbst("总务科人员-特殊往来账");
                insertAssts.add(scaCr);
            }
            for (int i = 0; i < zyEmpInfo.size(); i++) {
                EmployeeReallySalaryVo info = zyEmpInfo.get(i);
                ErpReimAsstDto scaCr = new ErpReimAsstDto();
                BeanUtils.copyProperties(spCorrAccCR, scaCr);
                scaCr.setSupType(MedConst.TYPE_3);
                scaCr.setReimDetailId(dto.getId());
                scaCr.setAsstNo(asstNo++);
                scaCr.setActigAmt(info.getShouldPayTotal());
                scaCr.setAbst("住院收费室人员-特殊往来账");
                insertAssts.add(scaCr);
            }
            for (int i = 0; i < dqEmpInfo.size(); i++) {
                EmployeeReallySalaryVo info = dqEmpInfo.get(i);
                ErpReimAsstDto spsCr = new ErpReimAsstDto();
                BeanUtils.copyProperties(spPlasmaCR, spsCr);
                spsCr.setSupType(MedConst.TYPE_3);
                spsCr.setReimDetailId(dto.getId());
                spsCr.setAsstNo(asstNo++);
                spsCr.setActigAmt(info.getShouldPayTotal());
                spsCr.setAbst("单采血浆站人员-特殊单采血浆站");
                insertAssts.add(spsCr);
            }

            //5.工资总额-冲账 冲账包括：岗位工资、薪级工资、护士10%、地区附件津贴、护龄补贴、基础绩效、生活补贴、临时增加
            //获取临床冲账配置项
            Optional<ErpVcrSalaryConfigDto> opt4 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.OFFSET_ACCOUNT)
                            && StringUtils.equals(e.getDeptType(), MedConst.TYPE_1)).findFirst();
            //获取职能冲账配置项
            Optional<ErpVcrSalaryConfigDto> opt5 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.OFFSET_ACCOUNT)
                            && StringUtils.equals(e.getDeptType(), MedConst.TYPE_2)).findFirst();
            if (opt4.isEmpty()) {
                log.error("工资总额-临床-冲账配置不存在-reimName:{}", ErpConstants.OFFSET_ACCOUNT);
                throw new AppException("工资总额-临床-冲账配置不存在");
            }
            if (opt5.isEmpty()) {
                log.error("工资总额-职能-冲账配置不存在-reimName:{}", ErpConstants.OFFSET_ACCOUNT);
                throw new AppException("工资总额-临床-冲账配置不存在");
            }
            ErpVcrSalaryConfigDto lcOffsetCfg = opt4.get();
            ErpVcrSalaryConfigDto znOffsetCfg = opt5.get();
            //获取临床配置项明细
            List<ErpVcrSalaryConfigDetailDto> lcOffsetConfigDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(lcOffsetCfg.getId()))
                    .collect(Collectors.toList());
            //获取职能配置项明细
            List<ErpVcrSalaryConfigDetailDto> znOffsetConfigDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(znOffsetCfg.getId()))
                    .collect(Collectors.toList());
            //查询临床岗位工资分录
            /*List<ErpReimAsstVo> lcOffsetAssts = erpReimAsstVos.stream()
                    .filter(e -> StringUtils.equals(e.getActigSubCode(), "********")
                            && StringUtils.equals(e.getEconSubCode(), "3010101")
                            && StringUtils.equals(e.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(e.getActigAmtType(), MedConst.TYPE_1))
                    .collect(Collectors.toList());*/
            //求和，取负数  临床科室：住院收费室
//            BigDecimal lcOffsetTotal = lcOffsetAssts.stream().map(ErpReimAsstVo::getActigAmt).reduce(BigDecimal.ZERO, BigDecimal::add).negate();
            BigDecimal lcOffsetTotal = zyEmpInfoTotal.negate();
            salaryTotal = salaryTotal.add(lcOffsetTotal);
            //查询职能岗位工资分录
//            List<ErpReimAsstVo> znOffsetAssts = erpReimAsstVos.stream()
//                    .filter(e -> StringUtils.equals(e.getActigSubCode(), "51010401")
//                            && StringUtils.equals(e.getEconSubCode(), "3010101")
//                            && StringUtils.equals(e.getActigSys(), MedConst.TYPE_1)
//                            && StringUtils.equals(e.getActigAmtType(), MedConst.TYPE_1))
//                    .collect(Collectors.toList());
            //求和，取负数 职能科室：维修班，总务科，单采血浆站
//            BigDecimal znOffsetTotal = znOffsetAssts.stream().map(ErpReimAsstVo::getActigAmt).reduce(BigDecimal.ZERO, BigDecimal::add).negate();
            BigDecimal znOffsetTotal = wxEmpInfoTotal.add(zwEmpInfoTotal).add(dqEmpInfoTotal).negate();
            salaryTotal = salaryTotal.add(znOffsetTotal);
            //获取临床冲账的借方
            ErpVcrSalaryConfigDetailDto lcOffsetCR = lcOffsetConfigDetails.stream()
                    .filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            //获取职能冲账借方
            ErpVcrSalaryConfigDetailDto znOffsetCR = znOffsetConfigDetails.stream()
                    .filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
            //维修班 (职能)、总务科（职能）、住院收费室（临床），单采血浆站（职能） 分别生成冲账
            //住院收费室   402002/住院收费室、城南院区收费室
            ErpReimAsstDto lcCR = new ErpReimAsstDto();
            BeanUtils.copyProperties(lcOffsetCR, lcCR);
            lcCR.setSupType(MedConst.TYPE_3);
            lcCR.setReimDetailId(dto.getId());
            lcCR.setAsstNo(asstNo++);
            lcCR.setDeptCode("402002");
            lcCR.setDeptName("住院收费室、城南院区收费室");
            lcCR.setActigAmt(zyEmpInfoTotal.negate());
            lcCR.setAbst("工资总额-冲账");
            insertAssts.add(lcCR);
            //维修班   528004/维修班
            ErpReimAsstDto znCR = new ErpReimAsstDto();
            BeanUtils.copyProperties(znOffsetCR, znCR);
            znCR.setSupType(MedConst.TYPE_3);
            znCR.setReimDetailId(dto.getId());
            znCR.setDeptCode("528004");
            znCR.setDeptName("维修班");
            znCR.setAsstNo(asstNo++);
            znCR.setActigAmt(wxEmpInfoTotal.negate());
            znCR.setAbst("工资总额-冲账");
            insertAssts.add(znCR);
            //总务科       528001/总务科办公室
            ErpReimAsstDto znCR1 = new ErpReimAsstDto();
            BeanUtils.copyProperties(znOffsetCR, znCR1);
            znCR1.setSupType(MedConst.TYPE_3);
            znCR1.setReimDetailId(dto.getId());
            znCR1.setDeptCode("528001");
            znCR1.setDeptName("总务科办公室");
            znCR1.setAsstNo(asstNo++);
            znCR1.setActigAmt(zwEmpInfoTotal.negate());
            znCR1.setAbst("工资总额-冲账");
            insertAssts.add(znCR1);
            //单采血浆站 405001/单采血浆站
            ErpReimAsstDto znCR2 = new ErpReimAsstDto();
            BeanUtils.copyProperties(znOffsetCR, znCR2);
            znCR2.setSupType(MedConst.TYPE_3);
            znCR2.setReimDetailId(dto.getId());
            znCR2.setDeptCode("405001");
            znCR2.setDeptName("单采血浆站");
            znCR2.setAsstNo(asstNo++);
            znCR2.setActigAmt(dqEmpInfoTotal.negate());
            znCR2.setAbst("工资总额-冲账");
            insertAssts.add(znCR2);

            //2累计扣款  （不区分科室，汇总）
//                //查询用户-往来单位映射
//                CommonResult<List<EcsUserCorrsInsCfgVo>> list = ecsReimFeignService.list(new EcsUserCorrsInsCfgDto());
//                if (CollectionUtils.isEmpty(list.getData())) {
//                    throw new AppException("个人临时扣款用户-往来单位映射信息不存在");
//                }
//                List<EcsUserCorrsInsCfgVo> userCorrs = list.getData();
//                userCorrs.stream().map()
//                List<ErpReimSalaryTaskDto> salaryTaskDtos = erpReimSalaryTaskReadMapper.selectList(salaryTaskWrapper);
            //查询未生成的个人扣款任务
            List<ErpReimSalaryTaskDto> salaryTasks = salaryTaskDtos.stream()
                    .filter(e -> StringUtils.equals(e.getType(), ErpConstants.INDIVDUAL_REDUCE))
                    .collect(Collectors.toList());
            if (salaryTasks.size() > 1) {
                throw new AppException("存在多个相同待生成凭证个人扣款任务");
            }
            ErpReimSalaryTaskDto salary = salaryTasks.get(0);
            //查询个人代扣类型任务明细
            LambdaQueryWrapper<ErpReimSalaryTaskDetailDto> salaryTaskDetailWrapper = Wrappers.lambdaQuery(ErpReimSalaryTaskDetailDto.class);
            salaryTaskDetailWrapper.eq(ErpReimSalaryTaskDetailDto::getTaskId, salary.getId());
            List<ErpReimSalaryTaskDetailDto> salaryTaskDetails = erpReimSalaryTaskDetailReadMapper.selectList(salaryTaskDetailWrapper);
            //累计扣款总和
            BigDecimal accDedu = salaryTaskDetails.stream()
                    .map(ErpReimSalaryTaskDetailDto::getReimAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            //通过项目类型进行分组，准备汇总
            Map<String, List<ErpReimSalaryTaskDetailDto>> groupSalaryDetails = salaryTaskDetails.stream().collect(Collectors.groupingBy(ErpReimSalaryTaskDetailDto::getReimName));
//            asstNo = 1;
            for (String key : groupSalaryDetails.keySet()) {
                List<ErpReimSalaryTaskDetailDto> salaryTaskDetailDtos = groupSalaryDetails.get(key);
                //如果项目为临时扣款则单独处理
                if (StringUtils.equals(key, ErpConstants.TEMPORARY_REDUCE_SALARY) || StringUtils.equals(key, ErpConstants.TEMPORARY_REDUCE_SALARY2)) {         //临时扣款
                    //每个用户单独生成各自的贷方
                    for (int i = 0; i < salaryTaskDetailDtos.size(); i++) {
                        ErpReimSalaryTaskDetailDto salaryTaskDetail = salaryTaskDetailDtos.get(i);
                        //查询工资配置项
                        Optional<ErpVcrSalaryConfigDto> first = indiConfigDtos.stream().filter(e -> StringUtils.equals(e.getReimName(), "temporaryReduceSalary")
                                && StringUtils.equals(e.getEmpCode(), salaryTaskDetail.getEmpCode())).findFirst();
                        if (first.isEmpty()) {
                            log.error("个人临时扣款，用户的往来单位未配置 empCode:{}", salaryTaskDetail.getEmpCode());
                            throw new AppException("个人临时扣款，用户的往来单位未配置 工号：" + salaryTaskDetail.getEmpCode());
                        }
                        ErpVcrSalaryConfigDto indiConfig = first.get();
                        //获取配置项明细
                        List<ErpVcrSalaryConfigDetailDto> indiConfigDetails = indiConfigDetailDtos.stream()
                                .filter(e -> e.getSalaryConfigId().equals(indiConfig.getId()))
                                .collect(Collectors.toList());
                        //生成财务贷方
                        ErpVcrSalaryConfigDetailDto cwDR = indiConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                                && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwDR, dr);
                        dr.setSupType(MedConst.TYPE_3);
                        dr.setReimDetailId(dto.getId());
                        dr.setAsstNo(asstNo++);
                        dr.setActigAmt(salaryTaskDetail.getReimAmt());
                        dr.setAbst(indiConfig.getAbst());
                        insertAssts.add(dr);
                    }
                } else {                    //其他项目，汇总
                    BigDecimal total = salaryTaskDetailDtos.stream()
                            .map(ErpReimSalaryTaskDetailDto::getReimAmt)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //查询工资配置项
                    Optional<ErpVcrSalaryConfigDto> first = indiConfigDtos.stream()
                            .filter(e -> StringUtils.equals(e.getReimName(), key))
                            .findFirst();
                    if (first.isEmpty()) {
                        log.error("不存在个人扣减配置项-reim_name:{}", key);
                        throw new AppException("不存在个人扣减配置项");
                    }
                    ErpVcrSalaryConfigDto indiConfig = first.get();
                    //获取配置项明细
                    List<ErpVcrSalaryConfigDetailDto> indiConfigDetails = indiConfigDetailDtos.stream()
                            .filter(e -> e.getSalaryConfigId().equals(indiConfig.getId()))
                            .collect(Collectors.toList());
                    //生成财务贷方
                    ErpVcrSalaryConfigDetailDto cwDR = indiConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                            && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwDR, dr);
                    dr.setSupType(MedConst.TYPE_3);
                    dr.setReimDetailId(dto.getId());
                    dr.setAsstNo(asstNo++);
                    dr.setActigAmt(total);
                    dr.setAbst(indiConfig.getAbst());
                    insertAssts.add(dr);
                }
            }

            //部单独处理吴军兵 实发工资   4月7日 不特殊处理吴军兵
            //获取吴军兵工资信息  工号：0718
            List<EmployeeReallySalaryVo> wbjSalaryVo = data.stream().filter(e -> StringUtils.equals(e.getEmpCode(), "0718")).collect(Collectors.toList());
            if (false  && CollectionUtil.isNotEmpty(wbjSalaryVo)) {
                EmployeeReallySalaryVo wjbSalary = wbjSalaryVo.get(0);
                accDedu = accDedu.add(wjbSalary.getRealPayTotal());
                //查询工资配置项
                Optional<ErpVcrSalaryConfigDto> wjbConfig = indiConfigDtos.stream().filter(e -> StringUtils.equals(e.getReimName(), "temporaryReduceSalary")
                        && StringUtils.equals(e.getEmpCode(), "0718")).findFirst();
                if (wjbConfig.isEmpty()) {
                    log.error("个人临时扣款，用户的往来单位未配置 empCode:0718");
                    throw new AppException(String.format("个人临时扣款，用户的往来单位未配置 员工编号:0718"));
                }
                ErpVcrSalaryConfigDto wjbIndiConfig = wjbConfig.get();
                //获取配置项明细
                List<ErpVcrSalaryConfigDetailDto> wjbIndiConfigDetails = indiConfigDetailDtos.stream()
                        .filter(e -> e.getSalaryConfigId().equals(wjbIndiConfig.getId()))
                        .collect(Collectors.toList());
                //生成财务贷方
                ErpVcrSalaryConfigDetailDto cwWJBDR = wjbIndiConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                ErpReimAsstDto wdr = new ErpReimAsstDto();
                BeanUtils.copyProperties(cwWJBDR, wdr);
                wdr.setSupType(MedConst.TYPE_3);
                wdr.setReimDetailId(dto.getId());
                wdr.setAsstNo(asstNo++);
                wdr.setActigAmt(wjbSalary.getRealPayTotal());
                wdr.setAbst("吴军兵实发");
                insertAssts.add(wdr);
            }


            //银行存款  银行存款=工资总额-累计扣款
            BigDecimal yhck = salaryTotal.subtract(accDedu);
            //查询银行存款配置项
            Optional<ErpVcrSalaryConfigDto> first = indiConfigDtos.stream().filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.BANK_DEPOSIT)).findFirst();
            if (first.isEmpty()) {
                log.error("个人扣减-银行存款贷方未配置");
                throw new AppException("个人扣减-银行存款贷方未配置");
            }
            ErpVcrSalaryConfigDto bankConfig = first.get();
            //获取配置项明细
            List<ErpVcrSalaryConfigDetailDto> bankConfigDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(bankConfig.getId()))
                    .collect(Collectors.toList());
            //生成财务贷方
            ErpVcrSalaryConfigDetailDto cwDR = bankConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                    && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
            ErpReimAsstDto dr = new ErpReimAsstDto();
            BeanUtils.copyProperties(cwDR, dr);
            dr.setSupType(MedConst.TYPE_3);
            dr.setReimDetailId(dto.getId());
            dr.setAsstNo(asstNo++);
            dr.setActigAmt(yhck);
            dr.setAbst(bankConfig.getAbst());
            insertAssts.add(dr);

            //----------预算科目-借方生成------------

            //1.薪级工资
            //获取薪级工资总额
            BigDecimal sgaTotal = jtAssts.stream()
                    .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.SAL_GRADE_SALARY_CODE))
                    .map(e -> e.getActigAmt())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //获取配置项
            Optional<ErpVcrSalaryConfigDto> ysOpt1 = indiConfigDtos.stream()
                    .filter(e -> StringUtils.equals(e.getReimName(), ErpConstants.SAL_GRADE_SALARY))
                    .findFirst();
            if (ysOpt1.isEmpty()) {
                log.error("个人代扣-预算科目-薪级工资配置项不存在-reimName:{}", ErpConstants.SAL_GRADE_SALARY);
                throw new AppException("个人代扣-预算科目-薪级工资配置项不存在");
            }
            ErpVcrSalaryConfigDto psCfg = ysOpt1.get();
            //获取配置项明细
            List<ErpVcrSalaryConfigDetailDto> ysConfigDetails = indiConfigDetailDtos.stream()
                    .filter(e -> e.getSalaryConfigId().equals(psCfg.getId()))
                    .collect(Collectors.toList());
            //获取预算科目借方
            ErpVcrSalaryConfigDetailDto ysCR = ysConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                    && StringUtils.equals(asst.getActigAmtType(),MedConst.TYPE_1)).findFirst().get();
            ErpReimAsstDto psCR = new ErpReimAsstDto();
            BeanUtils.copyProperties(ysCR, psCR);
            psCR.setSupType(MedConst.TYPE_3);
            psCR.setReimDetailId(dto.getId());
            psCR.setAsstNo(asstNo++);
            psCR.setActigAmt(sgaTotal);
            psCR.setAbst("个人代扣-薪级工资");
            insertAssts.add(psCR);
            //2.护士10%
            //获取护士10%总额
            BigDecimal nsTotal = jtAssts.stream()
                    .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.NURSE_SALARY_CODE))
                    .map(e -> e.getActigAmt())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            insertAssts.add(inDividualReduceYsAsst(indiConfigDtos,indiConfigDetailDtos,ErpConstants.NURSE_SALARY,dto.getId(),asstNo++,"个人代扣-预算科目-护士10%",nsTotal));
            //3.地区附件津贴
            //获取地区附加津贴总额
            BigDecimal asTotal = jtAssts.stream()
                    .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.AREA_SALARY_CODE))
                    .map(e -> e.getActigAmt())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            insertAssts.add(inDividualReduceYsAsst(indiConfigDtos,indiConfigDetailDtos,ErpConstants.AREA_SALARY,dto.getId(),asstNo++,"个人代扣-预算科目-地区附加津贴",asTotal));
            //4.护龄补贴
            //获取护龄补贴总额
            BigDecimal agTotal = jtAssts.stream()
                    .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.AGE_SALARY_CODE))
                    .map(e -> e.getActigAmt())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            insertAssts.add(inDividualReduceYsAsst(indiConfigDtos,indiConfigDetailDtos,ErpConstants.AGE_SALARY,dto.getId(),asstNo++,"个人代扣-预算科目-护龄补贴",agTotal));
            //5.基础绩效工资
            //获取基础绩效工资总额
            BigDecimal bpTotal = jtAssts.stream()
                    .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.BASIC_PERF_CODE))
                    .map(e -> e.getActigAmt())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            insertAssts.add(inDividualReduceYsAsst(indiConfigDtos,indiConfigDetailDtos,ErpConstants.BASIC_PERF,dto.getId(),asstNo++,"个人代扣-预算科目-基础绩效工资",bpTotal));
            //6.生活补贴
            //获取生活补贴总额
            BigDecimal lsTotal = jtAssts.stream()
                    .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.LIFE_SALARY_CODE))
                    .map(e -> e.getActigAmt())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            insertAssts.add(inDividualReduceYsAsst(indiConfigDtos,indiConfigDetailDtos,ErpConstants.LIFE_SALARY,dto.getId(),asstNo++,"个人代扣-预算科目-生活补贴",lsTotal));
            //7.岗位工资
            //获取岗位工资总额  银行存款-薪级工资-护士10%-地区附加津贴-护龄津贴-基础性绩效-生活补贴
            BigDecimal psTotal  = yhck.subtract(sgaTotal).subtract(nsTotal).subtract(asTotal).subtract(agTotal).subtract(bpTotal).subtract(lsTotal);
            insertAssts.add(inDividualReduceYsAsst(indiConfigDtos,indiConfigDetailDtos,ErpConstants.POST_SALARY,dto.getId(),asstNo++,"个人代扣-预算科目-岗位工资",psTotal));

            //---------------预算科目-贷方生成----------------
            //直接取薪级工资预算科目贷方项目即可
            ErpVcrSalaryConfigDetailDto ysDRDetail = ysConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                    && StringUtils.equals(asst.getActigAmtType(),MedConst.TYPE_2)).findFirst().get();
            ErpReimAsstDto ysDR = new ErpReimAsstDto();
            BeanUtils.copyProperties(ysDRDetail, ysDR);
            ysDR.setSupType(MedConst.TYPE_3);
            ysDR.setReimDetailId(dto.getId());
            ysDR.setAsstNo(asstNo++);
            ysDR.setActigAmt(yhck);
            ysDR.setAbst("个人代扣");
            insertAssts.add(ysDR);
        }

        //新增
        insertAssts.stream().forEach(asst -> {
            asst.setCrter(dto.getSysUser().getUsername());
            asst.setCreateTime(DateUtil.getCurrentTime(null));
            asst.setHospitalId("zjxrmyy");
        });

        log.info("--------------需要插入的辅助项{}", insertAssts.size());
//        log.info(new Gson().toJson(insertAssts));
        //删除旧辅助项目
        Certificate delParam = new Certificate();
        delParam.setSupType(MedConst.TYPE_3);
        delParam.setIds(delIds);
        erpVcrDetailWriteMapper.deleteVcrReimAsst(delParam);
        //插入
        BatchUtil.batch("insertReimAsst", insertAssts, ErpVcrDetailWriteMapper.class);
    }

    /**
     * 个人代扣-预算科目生成
     * @return
     */
    private ErpReimAsstDto inDividualReduceYsAsst(List<ErpVcrSalaryConfigDto> configDtos,
                                                  List<ErpVcrSalaryConfigDetailDto> configDetailDtos,
                                                  String reimName,
                                                  Integer reimDetailId,
                                                  int asstNo,
                                                  String abst,
                                                  BigDecimal amt) {
        Optional<ErpVcrSalaryConfigDto> ysOpt1 = configDtos.stream()
                .filter(e -> StringUtils.equals(e.getReimName(), reimName))
                .findFirst();
        if (ysOpt1.isEmpty()) {
            log.error("个人代扣-预算科目配置项不存在-reimName:{}", reimName);
            throw new AppException("个人代扣-预算科目配置项不存在");
        }
        ErpVcrSalaryConfigDto psCfg = ysOpt1.get();
        //获取配置项明细
        List<ErpVcrSalaryConfigDetailDto> ysConfigDetails = configDetailDtos.stream()
                .filter(e -> e.getSalaryConfigId().equals(psCfg.getId()))
                .collect(Collectors.toList());
        //获取预算科目借方
        ErpVcrSalaryConfigDetailDto ysCR = ysConfigDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                && StringUtils.equals(asst.getActigAmtType(),MedConst.TYPE_1)).findFirst().get();
        ErpReimAsstDto psCR = new ErpReimAsstDto();
        BeanUtils.copyProperties(ysCR, psCR);
        psCR.setSupType(MedConst.TYPE_3);
        psCR.setReimDetailId(reimDetailId);
        psCR.setAsstNo(asstNo);
        psCR.setActigAmt(amt);
        psCR.setAbst(abst);
        return psCR;
    }

    /**
     * 保存折旧凭证
     * @param dto
     */
    @Override
    public void saveDeprVcrAssts(EcsReimDeprTaskDto dto) {
        log.info("------------------------------start保存折旧凭证------------------------");
        //查询折旧数据
//        ErpPropertyDto propertyDto = new ErpPropertyDto();
////        List<ErpPropertyVo> erpPropertyVos = erpVcrDetailReadMapper.queryPropertyDeprList(propertyDto);
//        //查询某月的资产信息
        ErpPropertyDeprDto propertyDeprDto = new ErpPropertyDeprDto();
        propertyDeprDto.setYm(dto.getLaunchDate());
        propertyDeprDto.setTypen(true);
        propertyDeprDto.setBigCategoryModel(false);
        propertyDeprDto.setCalcFinaSubsidy(true);
        propertyDeprDto.setCalcBigCategoryDepr(false);

        //查询折旧分配表，生成excel
        CommonResult<List<ErpAmsPropertyDepr2Vo>> erpQueryDeprSummary2Result = amsDeprDeptReadServiceFeignApi.erpQueryDeprSummary2(propertyDeprDto);
        if (Objects.isNull(erpQueryDeprSummary2Result) || Objects.isNull(erpQueryDeprSummary2Result.getData())) {
            log.error(String.format("查询%s折旧信息失败", dto.getLaunchDate()));
            throw new AppException(String.format("查询%s折旧信息失败", dto.getLaunchDate()));
        }
        List<ErpAmsPropertyDepr2Vo> yearSummary2 = erpQueryDeprSummary2Result.getData();
        List<ErpAmsPropertyDepr2Vo> yy = yearSummary2.stream().filter(e -> !e.getIsSummary()).collect(Collectors.toList());
        //生成折旧摊销分配表
        createDeprAmorExcel(yy,dto);

        CommonResult<List<EcsReimDeprTaskVo>> taskRes = ecsReimFeignService.list(dto);
        if (Objects.isNull(taskRes) || CollectionUtil.isEmpty(taskRes.getData())) {
            log.error(String.format("查询折旧任务失败，折旧任务id%s", dto.getId()));
            throw new AppException(String.format("查询折旧任务失败，折旧任务id%s", dto.getId()));
        }
        EcsReimDeprTaskVo ecsReimDeprTaskVo = taskRes.getData().get(0);
        if (StringUtils.equals(ecsReimDeprTaskVo.getReimFlag(),MedConst.TYPE_1)) {
            log.error(String.format("任务已生成凭证，折旧任务id%s", dto.getId()));
            throw new AppException(String.format("任务已生成凭证，折旧任务id%s", dto.getId()));
        }
        //查询折旧任务明细
        EcsReimDeprTaskDetailDto taskDetailParam = new EcsReimDeprTaskDetailDto();
        taskDetailParam.setTaskId(dto.getId());
        CommonResult<List<EcsReimDeprTaskDetailVo>> taskDetailRes = ecsReimFeignService.list(taskDetailParam);
        if (Objects.isNull(taskDetailRes) || CollectionUtil.isEmpty(taskDetailRes.getData())) {
            log.error(String.format("查询折旧任务明细失败，折旧任务id%s", dto.getId()));
            throw new AppException(String.format("查询折旧任务明细失败，折旧任务id%s", dto.getId()));
        }
        List<EcsReimDeprTaskDetailVo> taskDetails = taskDetailRes.getData();
        //hrp-用友科室映射(用于使用用友部门前缀判断科室类型)
        CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());

        if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
            throw new AppException("当前未维护科室关系映射!");
        }
        List<HrmOrgAgencyMapVo> orgAgencyMapVos = result.getData();
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        //折旧固定辅助项目-借方科目
        Map<String, Map<String, Map<String, Map<String, ErpReimAsstDto>>>> defaultDeprCRVcrAssts = createDefaultDeprCRVcrAssts();

        //折旧固定辅助项目-贷方科目
        Map<String,Map<String,ErpReimAsstDto>> defaultDeprDRVcrAssts = createDefaultDeprDRVcrAssts();

        //资金来源映射
        Map<String, String> defaultSourceMapping = createDefaultSourceMapping();

        //资金编码映射
        Map<String, String> defaultSourceCodeMapping = createDefaultSourceCodeMapping();

        //部门类型
        Map<String, Map<String, String>> defaultDeptMapping = createDefaultDeptMapping();

        //资产类型
        Map<String, String> defaultAssetMapping = createDefaultAssetMapping();

        //处理折旧数据
        int asstNo = 1;

        for (int i = 0; i < taskDetails.size(); i++) {
            EcsReimDeprTaskDetailVo erpPropertyVo = taskDetails.get(i);
            //如果折旧摊销金额为0 ，则跳过
            if (erpPropertyVo.getAmt().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            //科室类型 (如果部门为空，或者不匹配hrp的部门规则，则直接跳过)
            String deptType = getDeptType(erpPropertyVo.getDeptCode(), orgAgencyMapVos);
//            String deptType = getDeptType(defaultDeptMapping, erpPropertyVo.getDeptUseCode());
            if (StringUtils.isEmpty(deptType)) {
                throw new AppException(String.format("不存在当前科室类型-科室%s", erpPropertyVo.getDeptCode()));
            }
            //资产类型
            String assetType = getAssetType(defaultAssetMapping, erpPropertyVo.getAssetTypeCode());
            //资金编码,如果是混合资金，则跳过
            String sourceCode = defaultSourceCodeMapping.get(erpPropertyVo.getSourceCode());
            if (StringUtils.isEmpty(sourceCode)) {
                continue;
            }
            //资金类型 ,如果资产类型为混合类型，则跳过
            String sourceType = getSourceType(defaultSourceMapping,erpPropertyVo.getSourceCode(), assetType,deptType);
            if (sourceType == null) {
                continue;
            }
            //根据资产类型改编资金类型
            //年度  暂时默认为以前年度
            String year = getYear(erpPropertyVo.getOpenYear(),sourceType);
//            String year = StringUtils.equals(erpPropertyVo.getOpenYear(),MedConst.TYPE_1)?"当前年度":"以前年度";
            //借方
            String abst;
            if (StringUtils.equals(assetType,"固定资产") && StringUtils.equals(sourceType,"财政补助资金")) {
                abst = year + erpPropertyVo.getDeptName() + "-" + erpPropertyVo.getAssetTypeName() + "-" + "固定资产折旧费";
            } else {
                abst = erpPropertyVo.getDeptName() + "-" + erpPropertyVo.getAssetTypeName() + "-无形资产摊销";
            }
            ErpReimAsstDto deprCRAsstDto = defaultDeprCRVcrAssts.get(deptType).get(assetType).get(sourceType).get(year);
            ErpReimAsstDto cr = new ErpReimAsstDto();
            BeanUtils.copyProperties(deprCRAsstDto,cr);
            cr.setDeptCode(erpPropertyVo.getDeptCode());
            cr.setDeptName(erpPropertyVo.getDeptName());
            cr.setReimDetailId(dto.getId());
            cr.setAsstNo(asstNo++);
            cr.setActigAmt(erpPropertyVo.getAmt());
            cr.setAbst(abst);

            insertAssts.add(cr);
        }

        //固定资产贷方  (只统计金额不为零)
        List<EcsReimDeprTaskDetailVo> notA08 = taskDetails.stream()
                .filter(e -> !e.getAssetTypeCode().startsWith("A08") && e.getAmt().compareTo(BigDecimal.ZERO)>0)
                .collect(Collectors.toList());
        //通过资产类型类型分类
        Map<String, List<EcsReimDeprTaskDetailVo>> capAssetMap = notA08.stream().collect(Collectors.groupingBy(item -> {
            String aValue = item.getAssetTypeCode();
            return aValue.substring(0, 3);
        }));

        for (String s : capAssetMap.keySet()) {
            List<EcsReimDeprTaskDetailVo> vos = capAssetMap.get(s);
            //资产性质
            String assetName = getAssetName(defaultAssetMapping, s);
            //通过资金类型分类
            Map<String, List<EcsReimDeprTaskDetailVo>> sourceCodeMap = vos.stream().collect(Collectors.groupingBy(EcsReimDeprTaskDetailVo::getSourceCode));
            for (String s1 : sourceCodeMap.keySet()) {
                List<EcsReimDeprTaskDetailVo> vos1 = sourceCodeMap.get(s1);

                String abst = "固定资产累计折旧-" + assetName;
                //计算当前资产类型的金额总和
                BigDecimal total = vos1.stream()
                        .map(item -> item.getAmt())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                ErpReimAsstDto deprDRAsstDto = defaultDeprDRVcrAssts.get("固定资产").get(assetName);
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(deprDRAsstDto,dr);
                dr.setReimDetailId(dto.getId());
                dr.setAsstNo(asstNo);
                dr.setActigAmt(total);
                dr.setAbst(abst);
                dr.setFundType(defaultSourceCodeMapping.get(s1));
                dr.setFundTypeName(defaultSourceMapping.get(s1));
                insertAssts.add(dr);
                asstNo++;
            }
        }

        //无形资产贷方 只统计金额不为零
        List<EcsReimDeprTaskDetailVo> a08 = taskDetails.stream()
                .filter(e -> e.getAssetTypeCode().startsWith("A08") && e.getAmt().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        //通过资产类型分类
        Map<String, List<EcsReimDeprTaskDetailVo>> immaAssetMap = a08.stream().collect(Collectors.groupingBy(item -> {
            //土地使用权归为一类，其他类型归为一类
            if (StringUtils.equals(item.getAssetTypeCode(),"A08040101")) {
                return "A08040101";
            } else {
                return "other";
            }
//            String aValue = item.getAssetTypeCode();
//            return aValue.substring(0, 3);
//            return aValue;
        }));

        for (String s : immaAssetMap.keySet()) {
            List<EcsReimDeprTaskDetailVo> vos = immaAssetMap.get(s);
            //资产性质  无形资产分为土地使用权和其他无形资产，直接进行判断  土地使用权:A08040101   其他无形资产(可能包含多种：应用软件：A08060303)
            String assetName ;
            if (StringUtils.equals(s,"A08040101")) {
                assetName = "土地使用权";
            } else {
                assetName = "其他无形资产";
            }
//            String assetName = getAssetName(defaultAssetMapping, s);
            //通过资金类型分类
            Map<String, List<EcsReimDeprTaskDetailVo>> sourceCodeMap = vos.stream().collect(Collectors.groupingBy(EcsReimDeprTaskDetailVo::getSourceCode));
            for (String s1 : sourceCodeMap.keySet()) {
                List<EcsReimDeprTaskDetailVo> vos1 = sourceCodeMap.get(s1);
                String abst = "无形资产累计摊销-" + assetName;
                //计算当前资产类型的金额总和
                BigDecimal total = vos1.stream()
                        .map(item -> item.getAmt())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                ErpReimAsstDto deprDRAsstDto = defaultDeprDRVcrAssts.get("无形资产").get(assetName);
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(deprDRAsstDto,dr);
                dr.setReimDetailId(dto.getId());
                dr.setAsstNo(asstNo);
                dr.setActigAmt(total);
                dr.setAbst(abst);
                dr.setFundType(defaultSourceCodeMapping.get(s1));
                dr.setFundTypeName(defaultSourceMapping.get(s1));
                insertAssts.add(dr);
                asstNo++;
            }
        }


        //插入辅助项目
        insertAssts.stream().forEach(asst -> {
            asst.setCrter(dto.getSysUser().getUsername());
            asst.setCreateTime(DateUtil.getCurrentTime(null));
            asst.setHospitalId("zjxrmyy");
        });

//        log.info("--------------需要插入的辅助项{}", JSONObject.toJSON(insertAssts));
        log.info("--------------需要插入的辅助项{}", insertAssts.size());

//        if (true) {
//            throw new AppException("测试");
//        }

        //更新折旧任务状态
        erpVcrDetailWriteMapper.updateDeprTask(dto);

        //更新资产扎帐状态
        EcsReimDeprTaskDto monthlyDepr = new EcsReimDeprTaskDto();
        monthlyDepr.setLaunchDate(ecsReimDeprTaskVo.getLaunchDate());
        erpVcrDetailWriteMapper.updateMonthlyDepr(monthlyDepr);

        //删除折旧旧辅助项目 ErpConstants.VCR_SOURCE_DEPR
        Certificate delParam = new Certificate();
        delParam.setSupType(ErpConstants.VCR_SOURCE_DEPR);
        delParam.setIds(Arrays.asList(dto.getId()));
        erpVcrDetailWriteMapper.deleteVcrReimAsst(delParam);
        //插入
        BatchUtil.batch("insertReimAsst", insertAssts, ErpVcrDetailWriteMapper.class);
        log.info("------------------------------end保存折旧凭证--------------------------");
    }

    private void createDeprAmorExcel(List<ErpAmsPropertyDepr2Vo> depr2Vos,EcsReimDeprTaskDto dto) {
        //写入数据到excel
        try(InputStream tempIS = OSSUtil.getObject(OSSConst.BUCKET_ECS, "template/折旧分配表.xlsx");
            ByteArrayOutputStream os = new ByteArrayOutputStream();) {


            ExcelWriter excelWriter = EasyExcel.write(os).inMemory(true).withTemplate(tempIS).build();
//            EasyExcel.write(os, ErpAmsPropertyDepr2Vo.class).withTemplate(tempIS).sheet(0).doWrite(depr2Vos);
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();

            excelWriter.fill(new FillWrapper("deprAmor",depr2Vos),writeSheet);

            //强制公式计算
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.getCreationHelper().createFormulaEvaluator().evaluateAll();

            excelWriter.finish();

            //上传并记录
            try(InputStream is = new ByteArrayInputStream(os.toByteArray());) {
                String path = "reim/item/" + ULIDUtil.generate() + ".xlsx";
                log.error("生成文件路径");
                log.error(path);
                OSSUtil.uploadFile(OSSConst.BUCKET_ECS, path, is);
                is.close();

                EcsReimFileRecordDto recordDto = new EcsReimFileRecordDto();
                recordDto.setAtt(path);
                recordDto.setAttName("折旧分配表.xlsx");
                recordDto.setType(ErpConstants.FILE_TYPE_GENERAL);
                recordDto.setAttCode(dto.getAttCode());
                erpVcrDetailWriteMapper.insertFileRecord(recordDto);
            }
        } catch (Exception e) {
            log.error("写入折旧分配表失败",e);
            throw new AppException("写入折旧分配表失败");
        }
    }

    private String getYear(String openYear,String sourceType) {
        if (StringUtils.equals(sourceType,"自有资金/捐赠资金/其他资金") || StringUtils.equals(sourceType,"资金")) {
            return "年度";
        }
//        return year;
        return StringUtils.equals(openYear,MedConst.TYPE_1)?"当前年度":"以前年度";     //
    }
    private String getSourceType(Map<String, String> sourceMapping,String sourceType,String assetType,String deptType) {
        String s = sourceMapping.get(sourceType);
        //如果是混合类型，返回null
        if (s == null) {
            return null;
        }
        //根据科室类型、资产类型确定资金来源，方便后续查询借贷方科目
        if (StringUtils.equals(deptType,MedConst.TYPE_1)) {      //临床科室
            if (StringUtils.equals(assetType,"固定资产")) {
                if (StringUtils.equals(s,"财政补助资金")) {
                    return "财政补助资金";
                } else {
                    return "自有资金/捐赠资金/其他资金";
                }
            } else {                                        //无形资产
                return "资金";
            }
        } else {                                            //职能科室
            if (StringUtils.equals(s,"财政补助资金")) {
                return "财政补助资金";
            } else {
                return "自有资金/捐赠资金/其他资金";
            }
        }
    }

    private String getAssetName(Map<String, String> assetMapping, String prefix) {
        //参数prefix 已经截取过
//        String prefix = assetType.substring(0, 3);
        return assetMapping.get(prefix);
    }

    private String getAssetType(Map<String, String> assetMapping, String assetType) {
        if (assetType != null && assetType.startsWith("A08")) {
            return "无形资产";
        }
        return "固定资产";
    }

    private String getDeptType(Map<String, Map<String, String>> deptMapping, String deptCode) {
        if (deptCode != null && !deptCode.isEmpty()) {
            Map<String, String> deptInfo = deptMapping.get(deptCode.substring(0, 1));
            return deptInfo != null ? deptInfo.get("mergeType") : "其他";
        }
        return "其他";
    }

    private Map<String,Map<String,ErpReimAsstDto>> createDefaultDeprDRVcrAssts() {
        Map<String,Map<String,ErpReimAsstDto>> asstsMap = new HashMap<>();
        //固定资产-房屋和构筑物
        doCreateDefaultDeprDRVcrAssts(asstsMap,"固定资产","房屋和构筑物",createDefaultDeprDRVcrAsst("160201","房屋及建筑物","1","2","",""));
        //固定资产-设备
        doCreateDefaultDeprDRVcrAssts(asstsMap,"固定资产","设备",createDefaultDeprDRVcrAsst("160202","设备","1","2","",""));
        //固定资产-文物和陈列品
        doCreateDefaultDeprDRVcrAssts(asstsMap,"固定资产","文物和陈列品",createDefaultDeprDRVcrAsst("160203","文物和陈列品","1","2","",""));
        //固定资产-图书和档案
        doCreateDefaultDeprDRVcrAssts(asstsMap,"固定资产","图书和档案",createDefaultDeprDRVcrAsst("160204","图书和档案","1","2","",""));
        //固定资产-家具和用具
        doCreateDefaultDeprDRVcrAssts(asstsMap,"固定资产","家具和用具",createDefaultDeprDRVcrAsst("160206","家具和用具","1","2","",""));

        //特种动植物
        doCreateDefaultDeprDRVcrAssts(asstsMap,"固定资产","特种动植物",createDefaultDeprDRVcrAsst("160205","特种动植物","1","2","",""));

        //物资
        doCreateDefaultDeprDRVcrAssts(asstsMap,"固定资产","物资",createDefaultDeprDRVcrAsst("160206","物资","1","2","",""));
        //无形资产
        doCreateDefaultDeprDRVcrAssts(asstsMap,"无形资产","土地使用权",createDefaultDeprDRVcrAsst("170201","土地使用权","1","2","",""));
        //无形资产-其他无形资产
        doCreateDefaultDeprDRVcrAssts(asstsMap,"无形资产","其他无形资产",createDefaultDeprDRVcrAsst("170202","其他无形资产","1","2","",""));
        return asstsMap;
    }

    private void doCreateDefaultDeprDRVcrAssts(Map<String,Map<String,ErpReimAsstDto>> asstsMap,String assetType,String asstName,ErpReimAsstDto dto) {
        asstsMap.computeIfAbsent(assetType, k -> new HashMap<>()).put(asstName, dto);
    }

    /**
     * 折旧借方科目生成
     * @return map<科室类型,map<资产类型,map<资金来源,map<使用年度,List<ErpReimAsstDto>>>
     */
    private Map<String,Map<String,Map<String,Map<String,ErpReimAsstDto>>>> createDefaultDeprCRVcrAssts(){
        Map<String,Map<String,Map<String,Map<String,ErpReimAsstDto>>>> asstsMap = new HashMap<>();
        //临床科室
        //临床-固定资产-财政补助资金-以前年度
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_1,"固定资产","财政补助资金","以前年度",createDefaultDeprDRVcrAsst("5001020302","以前年度固定资产折旧","1","1","04","医疗收支项目"));
        //临床-固定资产-财政补助资金-当前年度
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_1,"固定资产","财政补助资金","当前年度",createDefaultDeprDRVcrAsst("5001020301","固定资产折旧费","1","1","04","医疗收支项目"));
        //临床-固定资产-自有资金/捐赠资金/其他资金-(不区分年度)
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_1,"固定资产","自有资金/捐赠资金/其他资金","年度",createDefaultDeprDRVcrAsst("50010403","固定资产折旧费","1","1","04","医疗收支项目"));
        //临床-无形资产-（不区分资金）-（不区分年度）
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_1,"无形资产","资金","年度",createDefaultDeprDRVcrAsst("50010404","无形资产摊销费","1","1","04","医疗收支项目"));

        //管理科室
        //管理科室-固定资产-财政补助资金-以前年度
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_2,"固定资产","财政补助资金","以前年度",createDefaultDeprDRVcrAsst("5101020302","以前年度固定资产折旧费","1","1","04","医疗收支项目"));

        //管理科室-固定资产-财政补助资金-当前年度
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_2,"固定资产","财政补助资金","当前年度",createDefaultDeprDRVcrAsst("5101020301","固定资产折旧费","1","1","04","医疗收支项目"));

        //管理科室-固定资产-自有资金/捐赠资金/其他资金-(不区分年度)
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_2,"固定资产","自有资金/捐赠资金/其他资金","年度",createDefaultDeprDRVcrAsst("51010403","固定资产折旧费","1","1","04","医疗收支项目"));

        //管理科室-无形资产-财政补助资金-以前年度
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_2,"无形资产","财政补助资金","以前年度",createDefaultDeprDRVcrAsst("5101020402","以前年度无形资产摊销","1","1","04","医疗收支项目"));

        //管理科室-无形资产-财政补助资金-当前年度
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_2,"无形资产","财政补助资金","当前年度",createDefaultDeprDRVcrAsst("5101020401","无形资产摊销","1","1","04","医疗收支项目"));

        //管理科室-无形资产-自有资金/捐赠资金/其他资金-(不区分年度)
        doCreateDefaultDeprCRVcrAssts(asstsMap,MedConst.TYPE_2,"无形资产","自有资金/捐赠资金/其他资金","年度",createDefaultDeprDRVcrAsst("51010404","无形资产摊销","1","1","04","医疗收支项目"));

        return asstsMap;
    }

    private void doCreateDefaultDeprCRVcrAssts(Map<String,Map<String,Map<String,Map<String,ErpReimAsstDto>>>> asstsMap,
                                               String deptType,String asstType,String sourceType,String yearType,ErpReimAsstDto dto){
        asstsMap
                .computeIfAbsent(deptType,k -> new HashMap<>())
                .computeIfAbsent(asstType,k -> new HashMap<>())
                .computeIfAbsent(sourceType,k -> new HashMap<>())
                .put(yearType,dto);
    }

    private ErpReimAsstDto createDefaultDeprDRVcrAsst(String actigSubCode,String actigSubName,String actigSys,String actigAmtType,String projCode,String projName){
        ErpReimAsstDto deprAsst0 = new ErpReimAsstDto();
        deprAsst0.setPayTypeCode(ErpConstants.PAY_TYPECODE_DEPR);
        deprAsst0.setPayTypeName("折旧");
        deprAsst0.setActigSubCode(actigSubCode);
        deprAsst0.setActigSubName(actigSubName);
        deprAsst0.setActigSys(actigSys);
        deprAsst0.setActigAmtType(actigAmtType);
        deprAsst0.setProjCode(projCode);
        deprAsst0.setProjName(projName);
        deprAsst0.setHospitalId("zjxrmyy");
        deprAsst0.setSupType(ErpConstants.VCR_SOURCE_DEPR);
        return deprAsst0;
    }

    private Map<String,String> createDefaultSourceMapping(){
        Map<String,String> sourceMapping = new HashMap<>();
        sourceMapping.put("1", "自有资金");
        sourceMapping.put("2", "财政补助资金");
        sourceMapping.put("3", "自有资金");             //todo 暂时归到自有资金
//        sourceMapping.put("3", "财政补助资金/自有资金");  暂时不统计混合
        sourceMapping.put("4", "捐赠资金");
        sourceMapping.put("5", "其他资金");
        sourceMapping.put("/", "其他资金");
        return sourceMapping;
    }

    private Map<String,String> createDefaultSourceCodeMapping(){
        Map<String,String> sourceMapping = new HashMap<>();
        sourceMapping.put("1", "003");//自有资金
        sourceMapping.put("2", "001");//财政补助资金
        sourceMapping.put("3", "001");//自有资金        //todo 暂时混合资金归到自有资金
        sourceMapping.put("4", "004");//捐赠资金
        sourceMapping.put("5", "005");//其他资金
        sourceMapping.put("/", "005");//其他资金        // 归到其他资金
        return sourceMapping;
    }

    private Map<String,String> createDefaultAssetMapping(){
        Map<String,String> assetTypeMapping = new HashMap<>();
        assetTypeMapping.put("A01", "房屋和构筑物");
        assetTypeMapping.put("A02", "设备");
        assetTypeMapping.put("A03", "文物和陈列品");
        assetTypeMapping.put("A04", "图书和档案");
        assetTypeMapping.put("A05", "家具和用具");
        assetTypeMapping.put("A06", "特种动植物");
        assetTypeMapping.put("A07", "物资");
        assetTypeMapping.put("A08", "无形资产");
        return assetTypeMapping;
    }

    private Map<String,Map<String,String>> createDefaultDeptMapping(){
        Map<String,Map<String,String>> depts = new HashMap<>();
        depts.put("1", createDeptInfo("门、急诊临床科室", "临床科室", "临床科室", "0100101"));
        depts.put("2", createDeptInfo("住院临床科室", "临床科室", "临床科室", "0100102"));
        depts.put("3", createDeptInfo("医技科室", "医技科室", "临床科室", "0100103"));
        depts.put("4", createDeptInfo("医辅科室", "职能科室", "管理科室", "04001"));
        depts.put("5", createDeptInfo("行政后勤", "职能科室", "管理科室", "04010"));
        return depts;
    }

    private Map<String, String> createDeptInfo(String name, String type, String mergeType, String code) {
        Map<String, String> info = new HashMap<>();
        info.put("name", name);
        info.put("type", type);
        info.put("mergeType", mergeType);
        info.put("code", code);
        return info;
    }

    /**
     * 判断由字符串逗号拼接的A、B字符串是否有交集元素
     *
     * @param left
     * @param right
     * @return
     */
    private boolean checkIntersection(String left, String right) {
        String[] leftArr = left.split(",");
        String[] rightArr = right.split(",");

        Set<String> setA = new HashSet<>();
        for (String s : leftArr) {
            setA.add(s.trim());
        }
        for (String s : rightArr) {
            if (setA.contains(s.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 工资凭证生成，获取科室类型
     *
     * @return
     */
    private String getDeptType(String deptType,List<HrmOrgAgencyMapVo> orgAgencyMapVos) {
        if (StringUtils.isEmpty(deptType)) {
            return null;
        }

        Optional<HrmOrgAgencyMapVo> first = orgAgencyMapVos.stream().filter(e -> StringUtils.equals(e.getHrpOrgCode(), deptType)).findFirst();

        if (first.isEmpty()) {
            log.error("当前科室映射不存在{}", deptType);
            throw new AppException("当前科室映射不存在");
        }

        String yyOrgCode = first.get().getYyOrgCode();

        String prefix = yyOrgCode.substring(0,2);

        if (Arrays.asList("01","02","03").contains(prefix)) {   //临床
            return MedConst.TYPE_1;
        } else {                        //职能
            return MedConst.TYPE_2;
        }

        /*String prefix = deptType.substring(0, 1);

        if (StringUtils.equals(prefix,MedConst.TYPE_5)) {       //行政
            return MedConst.TYPE_2;
        } else {                                                // 临床、医技、医辅
            return MedConst.TYPE_1;
        }*/
        
        /*if (checkIntersection(ErpConstants.CLINIC_STR, deptType)) {  //临床医技
            return "1";
        }

        if (checkIntersection(ErpConstants.FUN_STR, deptType)) { //医辅、行政
            return "2";
        }
        return null;*/
    }

    /**
     * 工资凭证生成，获取人员类型
     *
     * @param empType
     * @return
     */
    private String getSalaryEmpType(String empType) {
        //在编
        if (Arrays.stream(ErpConstants.ESTAB_STR_ARR).anyMatch(e -> StringUtils.equals(e, empType))) {
            return MedConst.TYPE_1;
        }
        //招聘
        if (Arrays.stream(ErpConstants.HIRE_STR_ARR).anyMatch(e -> StringUtils.equals(e, empType))) {
            return MedConst.TYPE_2;
        }
        //临聘
        if (Arrays.stream(ErpConstants.TEMP_HIRE_STR_ARR).anyMatch(e -> StringUtils.equals(e, empType))) {
            return MedConst.TYPE_3;
        }
        //借调
        if (Arrays.stream(ErpConstants.SECONDMENT_STR_ARR).anyMatch(e -> StringUtils.equals(e,empType))) {
            return MedConst.TYPE_4;
        }
        //返聘
        if (Arrays.stream(ErpConstants.REHIRE_STR_ARR).anyMatch(e -> StringUtils.equals(e, empType))) {
            return MedConst.TYPE_5;
        }
        return null;
    }

    @Override
    public void updVcrAssts(Certificate dto) {
        //获取辅助项
        ErpReimAsstDto erpReimAsstDto = dto.getReimAsstDetails().get(0);
        if (!Objects.isNull(erpReimAsstDto.getId())) {
            //删除
            erpVcrDetailWriteMapper.deleteVcrReimAsstById(erpReimAsstDto.getId());
        }
        erpReimAsstDto.setCrter(dto.getRecordPersonId());
        erpReimAsstDto.setCreateTime(DateUtil.getCurrentTime(null));
        erpReimAsstDto.setHospitalId("zjxrmyy");
        erpVcrDetailWriteMapper.insertReimAsst(erpReimAsstDto);
    }


    @Override
    public void delVcrAssts(Certificate dto) {
        //删除
        erpVcrDetailWriteMapper.deleteVcrReimAsstById(dto.getId());
    }

    /**
     * @param erpReimDetailVos 报销
     * @param feeName          报销费用名称
     * @param reimAsstDetails  辅助项
     * @param insertAssts      需插入的辅助项
     * @param certDate         凭证日期
     */
    private void generalAsstGen(
            List<ErpReimDetailVo> erpReimDetailVos,
            String feeName,
            List<ErpReimAsstDto> reimAsstDetails,
            List<ErpReimAsstDto> insertAssts,
            String certDate,
            Certificate dto) {

        //是否含有财务会计
        boolean cwActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_1, MedConst.TYPE_1);
        //是否含有预算会计
        boolean ysActig = checkAsstActig(reimAsstDetails, MedConst.TYPE_2, MedConst.TYPE_1);
        boolean cwActigDept = false;
        boolean ysActigDept = false;
        //判断财务会计是否含有部门，有财务会计时，变量才有效
        if (cwActig) {
            cwActigDept = hasDeptAsst(dto, MedConst.TYPE_1, MedConst.TYPE_1);
        }
        //判断预算会计是否含有部门，有预算会计时，变量才有效
        if (ysActig) {
            ysActigDept = hasDeptAsst(dto, MedConst.TYPE_2, MedConst.TYPE_1);
        }

        //报销项序号
        int asstNo = 1;

        for (int j = 0; j < erpReimDetailVos.size(); j++) {
            ErpReimDetailVo item = erpReimDetailVos.get(j);
            //调用阿里接口，识别信息
            //List<Map<String, String>> maps = handleAlyPayInfoMaps(item.getId());
            //查询对应报销付款证明信息
            List<ErpReimPayReceiptVo> erpReimPayReceiptVos = getPayReceiptInfos(dto.getSupType(), item.getId());
            //查询当前报销的报销项
            List<ErpReimItemDetail> erpReimItemDetails = erpVcrDetailReadMapper.queryItemDetail(Arrays.asList(item.getId()));
            //摘要
            String abs = getAbs(erpReimPayReceiptVos, StringUtils.substring(item.getAppyerTime(), 0, 10), item.getAppyerName(), FeeNameEnum.getByType(dto.getType()).getFeeName());
            if (cwActig) {
                ErpReimAsstDto cwCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                ErpReimAsstDto cwDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                if (cwActigDept) {
                    for (int i = 0; i < erpReimItemDetails.size(); i++) {
                        ErpReimItemDetail reimItem = erpReimItemDetails.get(i);
                        //财务-借
                        ErpReimAsstDto cr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwCR, cr);
                        cr.setSupType(MedConst.TYPE_1);
                        cr.setDeptCode(reimItem.getDeptCode());
                        cr.setDeptName(reimItem.getDeptName());
                        cr.setActigSubCode(changeCWActigCode(reimItem.getDeptCode(), cr.getActigSubCode(), dto.getType()));
                        cr.setReimDetailId(item.getId());
                        cr.setAsstNo(asstNo++);
                        cr.setActigAmt(reimItem.getAmt());
                        cr.setAbst(abs);
                        insertAssts.add(cr);
                        asstNo++;
                        //如果非冲抵借款且支付方式为现金支付或者复明工程，则没有付款证明文件。
                        if (StringUtils.equals(item.getIsLoan(),MedConst.TYPE_0) && !StringUtils.equals(item.getPayMethod(),MedConst.TYPE_0)) {
                            ErpReimAsstDto dr = new ErpReimAsstDto();
                            BeanUtils.copyProperties(cwDR,dr);
                            dr.setSupType(MedConst.TYPE_1);
                            dr.setActigSubCode("10010101");
                            dr.setActigSubName("日常公用开支");
                            dr.setCashFlowCode("010201");
                            dr.setCashFlowName("购买商品、接受劳务支付的现金");
                            dr.setActigAmt(reimItem.getAmt());
                            dr.setAsstNo(asstNo++);
                            dr.setReimDetailId(item.getId());
                            dr.setAbst(abs);
                            insertAssts.add(dr);
                        }
                    }
                } else {
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(cwCR, cr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setActigAmt(item.getSum());
                    cr.setReimDetailId(item.getId());
                    cr.setAsstNo(asstNo++);
                    cr.setAbst(abs);
                    insertAssts.add(cr);
                    //如果是支付方式为现金支付或者复明工程，则没有付款证明文件
                    if (StringUtils.equals(item.getIsLoan(),MedConst.TYPE_0) && !StringUtils.equals(item.getPayMethod(),MedConst.TYPE_0)) {
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(cwDR,dr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setActigSubCode("10010101");
                        dr.setActigSubName("日常公用开支");
                        dr.setCashFlowCode("010201");
                        dr.setCashFlowName("购买商品、接受劳务支付的现金");
                        dr.setActigAmt(item.getSum());
                        dr.setAsstNo(asstNo++);
                        dr.setReimDetailId(item.getId());
                        dr.setAbst(abs);
                        insertAssts.add(dr);
                    }
                }

                if (StringUtils.equals(item.getIsLoan(),MedConst.TYPE_0)) {
                    //支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
                    if (StringUtils.isEmpty(item.getPayMethod()) || StringUtils.equals(item.getPayMethod(),MedConst.TYPE_0)) {
                        //生成财务贷方
                        genCWDRAssts(insertAssts, item, cwDR, asstNo, item.getAppyerName(), feeName, erpReimPayReceiptVos);
                        asstNo+=erpReimPayReceiptVos.size();
                    }
                } else {
                    //查询冲抵借款报销凭证
                    ErpReimAsstDto param = new ErpReimAsstDto();
                    param.setReimDetailId(item.getLoanReimId());
                    param.setSupType(MedConst.TYPE_1);
                    List<ErpReimAsstVo> erpReimAsstVos = erpVcrDetailReadMapper.queryReimAsstVoList(param);
                    if (CollectionUtil.isEmpty(erpReimAsstVos)) {
                        log.error("冲抵借款报销未生成凭证,借款报销id：{}",item.getLoanReimId());
                        throw new AppException(String.format("冲抵借款报销未生成凭证,借款报销id：%s",item.getLoanReimId()));
                    }
                    //获取借款报销财务借方数据，作为当前凭证的财务贷方数据
                    List<ErpReimAsstVo> loanAsstCrs = erpReimAsstVos.stream().filter(loan -> StringUtils.equals(loan.getActigSys(), MedConst.TYPE_1) && StringUtils.equals(loan.getActigAmtType(), MedConst.TYPE_1)).collect(Collectors.toList());
                    FeeNameEnum byType = FeeNameEnum.getByType(dto.getType());
                    //修改对应借贷方和报销id
                    for (int k = 0; k < loanAsstCrs.size(); k++) {
                        ErpReimAsstVo loanReimVo = loanAsstCrs.get(k);
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(loanReimVo, dr);
//                        dr.setPayTypeCode(byType.getPayTypeCode());
//                        dr.setPayTypeName(byType.getFeeName());
                        dr.setActigSys(MedConst.TYPE_1);
                        dr.setActigAmtType(MedConst.TYPE_2);
                        dr.setActigAmt(item.getLoanAmt());
                        dr.setReimDetailId(item.getId());
                        dr.setAsstNo(asstNo++);
                        insertAssts.add(dr);
                    }
                }
            }

            if (ysActig) {
                ErpReimAsstDto ysCR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_1)).findFirst().get();
                ErpReimAsstDto ysDR = reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), MedConst.TYPE_2)
                        && StringUtils.equals(asst.getActigAmtType(), MedConst.TYPE_2)).findFirst().get();
                if (ysActigDept) {
                    for (int i = 0; i < erpReimItemDetails.size(); i++) {
                        ErpReimItemDetail reimItem = erpReimItemDetails.get(i);
                        //财务-借
                        ErpReimAsstDto cr = new ErpReimAsstDto();
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(ysCR, cr);
                        BeanUtils.copyProperties(ysDR, dr);
                        cr.setSupType(MedConst.TYPE_1);
                        cr.setDeptCode(reimItem.getDeptCode());
                        cr.setDeptName(reimItem.getDeptName());
                        cr.setActigSubCode(changeCWActigCode(reimItem.getDeptCode(), cr.getActigSubCode(), dto.getType()));
                        cr.setReimDetailId(item.getId());
                        cr.setAsstNo(asstNo++);
                        cr.setActigAmt(reimItem.getAmt());
                        cr.setAbst(abs);
                        insertAssts.add(cr);
                        dr.setSupType(MedConst.TYPE_1);
                        dr.setReimDetailId(item.getId());
                        dr.setAsstNo(asstNo++);
                        dr.setActigAmt(reimItem.getAmt());
                        dr.setAbst(abs);
                        insertAssts.add(dr);
                    }
                } else {
                    ErpReimAsstDto cr = new ErpReimAsstDto();
                    ErpReimAsstDto dr = new ErpReimAsstDto();
                    BeanUtils.copyProperties(ysCR, cr);
                    BeanUtils.copyProperties(ysDR, dr);
                    cr.setSupType(MedConst.TYPE_1);
                    cr.setActigAmt(item.getSum());
                    cr.setReimDetailId(item.getId());
                    cr.setAsstNo(asstNo++);
                    cr.setAbst(abs);
                    insertAssts.add(cr);
                    dr.setSupType(MedConst.TYPE_1);
                    dr.setReimDetailId(item.getId());
                    dr.setAsstNo(asstNo++);
                    dr.setActigAmt(item.getSum());
                    dr.setAbst(abs);
                    insertAssts.add(dr);
                }
            }
        }
    }

    /**
     * 通过部门编号改变财务科目
     * 01-03
     * 04-05
     *
     * @param
     * @return
     */
    private String changeCWActigCode(String deptCode, String actigSubCode, String type) {
        String prefixStr = "";
        if (StringUtils.equals(type, MedConst.TYPE_4)) {
            //如果是分摊类型，则直接获取部门前缀，不需进行映射
            prefixStr = deptCode.substring(0, 2);
        } else {
            //非分摊类型，需要将部门映射为用友部门，再获取前缀
            //辅助项目信息中的部门需对应为用友中的部门
            CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());

            if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
                throw new AppException("当前未维护科室关系映射!");
            }
            List<HrmOrgAgencyMapVo> orgAgencyMapVos = result.getData();

            Optional<HrmOrgAgencyMapVo> first = orgAgencyMapVos.stream().filter(orgMap -> StringUtils.equals(orgMap.getHrpOrgCode(), deptCode)).findFirst();
            if (first.isEmpty()) {
                throw new AppException("存在未维护科室关系的部门!");
            }
            //获取用友部门前缀
            prefixStr = first.get().getYyOrgCode().substring(0, 2);
        }

        //前缀为01-03为职能部门科目，04-05为行政部门科目
        boolean isFuncDept = Arrays.asList("01", "02", "03").contains(prefixStr);

        if (StringUtils.equals(type, MedConst.TYPE_1) || StringUtils.equals(type, MedConst.TYPE_2)) {
            StringBuilder s = new StringBuilder(actigSubCode);
            if (isFuncDept) {
                s.setCharAt(1, '0');
            } else {
                s.setCharAt(1, '1');
            }
            return s.toString();
//            return isFuncDept? "50010402": "51010402";
        } else if (StringUtils.equals(type, MedConst.TYPE_4)) {
            StringBuilder s = new StringBuilder(actigSubCode);
            if (isFuncDept) {
                s.setCharAt(1, '0');
            } else {
                s.setCharAt(1, '1');
            }
            return s.toString();
//            return isFuncDept? "50010402": "51010402";
        }
        return actigSubCode;
    }

    @Override
    public void updateVcrAsstAmt(Certificate dto) {
        erpVcrDetailWriteMapper.updateVcrAsstAmt(dto);
    }

    private ErpVcrDetailDto yyVcr(Certificate source) {
        try {
            URL url = new URL(vcrGenUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            //转为json
            String requestBody = new Gson().toJson(source);
            System.out.println(requestBody);


            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = reader.readLine()) != null) {
                response.append(inputLine);
            }

            reader.close();

            connection.disconnect();

            log.info("--------Response Body-start-----");
            log.info(response.toString());
            log.info("--------Response Body-end-----");
            String postRes = response.toString();
            //处理JSON相应
            if (StringUtils.isEmpty(postRes)) {
                throw new AppException("请求凭证生成失败");
            }
            JSONObject jsonObject = JSON.parseObject(postRes);
            Integer code = jsonObject.getInteger("code");
            String msg = jsonObject.getString("message");
            if (code.equals(200)) {
                return JSON.parseObject(jsonObject.getString("data"), ErpVcrDetailDto.class);
            } else {
                throw new AppException(msg);
            }
        } catch (IOException e) {
            log.error("调用用友生成凭证失败", e);
            throw new AppException("调用用友生成凭证失败");
        } catch (AppException e1) {
            throw e1;
        }
    }

    private void yyVcrDel(List<String> ids) {
        try {
            URL url = new URL(vcrDelUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            //转为json
            String requestBody = new Gson().toJson(ids);
            System.out.println(requestBody);

            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            System.out.println("Response Code" + responseCode);

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = reader.readLine()) != null) {
                response.append(inputLine);
            }

            reader.close();
            connection.disconnect();

            String postRes = response.toString();
            //处理JSON
            if (StringUtils.isNotEmpty(postRes)) {
                JSONObject jsonObject = JSON.parseObject(postRes);
                Integer code = jsonObject.getInteger("code");
                String msg = jsonObject.getString("message");
                if (!code.equals(200)) {
                    throw new AppException(msg);
                }
            }
        } catch (IOException e) {
            throw new AppException("服务器异常");
        }
    }

    private void checkValid(List<CertificateDetail> details) {
        //现有的规则 借贷平衡  明细数量大于等于两条
        if (details.size() < 2) {
            throw new AppException("请确认明细数量大于两条");
        }
        System.out.println("当前辅助项目信息");
        System.out.println(new Gson().toJson(details));
        //借贷平衡
        Map<String, List<CertificateDetail>> byType = details.stream().collect(Collectors.groupingBy(CertificateDetail::getType));
        for (Map.Entry<String, List<CertificateDetail>> entry : byType.entrySet()) {
            List<CertificateDetail> typeCertiDetails = entry.getValue();
            Map<String, List<CertificateDetail>> byJD = typeCertiDetails.stream().collect(Collectors.groupingBy(CertificateDetail::getJdbz));
            BigDecimal CRNum = BigDecimal.ZERO; //借金额
            BigDecimal DRNum = BigDecimal.ZERO; //贷金额
            for (Map.Entry<String, List<CertificateDetail>> entry1 : byJD.entrySet()) {
                List<CertificateDetail> jdCertiDetails = entry1.getValue();
                BigDecimal reduce = jdCertiDetails.stream().map(obj -> new BigDecimal(obj.getJe())).reduce(BigDecimal.ZERO, BigDecimal::add);
                if ("借".equals(entry1.getKey())) {
                    CRNum = reduce;
                }
                if ("贷".equals(entry1.getKey())) {
                    DRNum = reduce;
                }
            }
            if (!(CRNum.compareTo(DRNum) == 0)) {
                throw new AppException((StringUtils.equals(entry.getKey(), "01") ? "财务科目" : "预算科目") + "借贷不平衡，请重新确认");
            }
        }
    }

    /**
     * 判断某类辅助项是否存在
     *
     * @param dto          dto.getReimAsstDetails()  原始辅助项
     * @param actigSys     科目类型
     * @param actigAmtType 金额类型
     * @return
     */
    private boolean hasDeptAsst(Certificate dto, String actigSys, String actigAmtType) {
        List<ErpReimAsstDto> reimAsstDtos = dto.getReimAsstDetails();
        if (CollectionUtils.isEmpty(reimAsstDtos)) {
            log.error("--------------辅助项不能为空--------------");
            throw new AppException("辅助项不能为空");
        }
        //获取对应科目辅助项
        Optional<ErpReimAsstDto> first = reimAsstDtos.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), actigSys)
                && StringUtils.equals(asst.getActigAmtType(), actigAmtType)).findFirst();
        if (first.isEmpty()) {
            log.error("会计科目缺失");
            throw new AppException("会计科目缺失");
        }

        String actigSubCode = first.get().getActigSubCode();
        //会计科目是否包含部门辅助项
        ErpActigCfg actigCfg = new ErpActigCfg();
        actigCfg.setSub(actigSubCode);
        actigCfg.setActigSys(actigSys);
        ErpActigCfg actigCfgInfo = erpVcrDetailReadMapper.queryActigCfg(actigCfg);
        if (StringUtils.isNotEmpty(actigCfgInfo.getAsstInfo()) && actigCfgInfo.getAsstInfo().contains(ErpConstants.ACTIG_ASST_CFG_DEPT)) {
            return true;
        }
        return false;
    }

    /**
     * 读取报销明细对应付款证明文件，生成贷方辅助项目
     */
    private void genCWDRAssts(List<ErpReimAsstDto> insertAssts,
                              ErpReimDetailVo erpReimVo,
                              ErpReimAsstDto drDto,
                              int asstNo,
                              String applyer,
                              String feeName,
                              List<ErpReimPayReceiptVo> receiptVos) {
        for (ErpReimPayReceiptVo vo : receiptVos) {
            String reimAbs = vo.getPayDate() + "付" + applyer + feeName;
            //生成贷方辅助项
            ErpReimAsstDto dr = new ErpReimAsstDto();
            BeanUtils.copyProperties(drDto, dr);
            dr.setSupType(MedConst.TYPE_1);
            dr.setReimDetailId(erpReimVo.getId());
            dr.setAsstNo(asstNo++);
            dr.setActigAmt(vo.getPayAmt());
            dr.setAbst(reimAbs);
            insertAssts.add(dr);

        }
    }

    private List<Map<String, String>> handleAlyPayInfoMaps(Integer id) {
        //查询报销明细对应付款文件
        List<FileRecordEntity> fileRecordEntities = erpVcrDetailReadMapper.queryFileRecord(Arrays.asList(id));
        //筛选类型为2 的 (1：普通文件 2：付款证明文件)
        List<FileRecordEntity> collect = fileRecordEntities
                .stream()
                .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2))
                .collect(Collectors.toList());
        return alyPayInfoMaps(collect);
    }

    private List<Map<String, String>> alyPayInfoMaps(List<FileRecordEntity> fileRecords) {
        List<Map<String, String>> maps = new ArrayList<>();
        for (FileRecordEntity fileRecord : fileRecords) {
            Map<String, String> stringStringMap = alyRecognGnr(fileRecord.getAtt(), fileRecord.getAttName());
            if (!stringStringMap.isEmpty()) {
                maps.add(stringStringMap);
            }
        }
        return maps;
    }

    /**
     * 获取辅助项摘要信息
     *
     * @param feeName 报销类型名称
     * @return
     */
    private String getAbs(List<ErpReimPayReceiptVo> receiptVos, String date, String applyer, String feeName) {
        //如果是差旅、培训报销，摘要为 日期+随行人员+费用名称，否则为 日期+申请人+费用名称
        StringBuilder sb = new StringBuilder();
        if (receiptVos.isEmpty()) {
            return sb.append(date).append("付").append(applyer).append(feeName).toString();
        }
        ErpReimPayReceiptVo vo = receiptVos.get(0);
        return sb.append(vo.getPayDate()).append("付").append(applyer).append(feeName).toString();
//        //如果是差旅、培训报销，摘要为 日期+随行人员+费用名称
//        //否则为 日期+申请人+费用名称
//        if (maps.isEmpty()) {
//            return date + "付" + applyer + feeName;
//        }
//        Map<String, String> map = maps.get(0);
//        if (map.isEmpty()) {
//            return date + "付" + applyer + feeName;
//        }
//        String rcpDate = map.get("日期");
//        return rcpDate.replace("年","-")
//                .replace("月","-").replace("日","") + "付" +applyer + feeName;
    }

    private Map<String, String> alyRecognGnr(String attPath, String attName) {
        Map<String, String> result = new HashMap<>();
        try {
            String boundary = "---------------------------1234567890";   //定义boundary
            URL url = new URL(recognUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            OutputStream outputStream = connection.getOutputStream();
            PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true);

            // 添加文件数据
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + attName + "\"").append("\r\n");
            writer.append("Content-Type: " + HttpURLConnection.guessContentTypeFromName(attName)).append("\r\n");
            writer.append("\r\n");
            writer.flush();

            InputStream fileInputStream = OSSUtil.getObject(OSSConst.BUCKET_ECS, attPath);
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();
            fileInputStream.close();

            writer.append("\r\n");
            writer.append("--" + boundary + "--").append("\r\n");
            writer.close();

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                response.append(line);
            }

            reader.close();
            outputStream.close();
            connection.disconnect();

            //处理JSON响应
            String post = response.toString();
            log.error("当前通用文字识别返回结果string-------------" + post);
            if (StringUtils.isNotEmpty(post)) {
                JSONObject jsonObject = JSON.parseObject(post);
                Integer code = jsonObject.getInteger("code");
                String msg = jsonObject.getString("message");
                if (code.equals(200)) {
                    JSONArray data = JSON.parseArray(jsonObject.getString("data"));
                    List<String> stringList = new ArrayList<>();
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject jsonObject1 = data.getJSONObject(i);
                        stringList.add(jsonObject1.getString("word"));
                    }
                    stringList.stream()
                            .filter(item -> StringUtils.isNotEmpty(item) && item.contains("："))
                            .forEach(kav -> {
                                String[] split = kav.split("：");
                                if (split.length == 2) {
                                    result.put(split[0], split[1]);
                                }
                            });
                    log.debug("--------------JSONArray----------" + data);
                }
            }
        } catch (Exception e) {
            //识别失败不抛出异常
            log.error("阿里云通用文字识别失败", e);
        }
        return result;
    }

    /**
     * 检查辅助项中是否有某类辅助项
     *
     * @param reimAsstDetails
     * @param actigSys
     * @param actigAmtType
     * @return
     */
    private boolean checkAsstActig(List<ErpReimAsstDto> reimAsstDetails, String actigSys, String actigAmtType) {
        return StringUtils.isNotEmpty(reimAsstDetails.stream().filter(asst -> StringUtils.equals(asst.getActigSys(), actigSys)
                && StringUtils.equals(asst.getActigAmtType(), actigAmtType)).findFirst().get().getActigSubCode());
    }


    private List<ErpReimPayReceiptVo> getPayReceiptInfos(String supType, Integer reimId) {
        //查询对应报销付款证明信息
        List<ErpReimPayReceiptVo> erpReimPayReceiptVos = erpReimPayReceiptReadMapper.queryListById(supType, reimId);
        //查询报销付款证明文件
        List<FileRecordEntity> fileRecordEntities = new ArrayList<>();
        if (StringUtils.equals(supType, MedConst.TYPE_1)) {
            //费用报销
            fileRecordEntities = erpVcrDetailReadMapper.queryFileRecord(Arrays.asList(reimId));
        } else {
            //药品报销
            List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(Arrays.asList(reimId));
            List<String> attCodes = drugVos.stream().map(ErpDrugReimDetailVo::getAttCode).collect(Collectors.toList());
            fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
        }

        List<FileRecordEntity> collect = fileRecordEntities
                .stream()
                .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2))
                .collect(Collectors.toList());
        if (erpReimPayReceiptVos.size() != collect.size()) {
            //数量不一致
            throw new AppException("存在未识别付款证明文件的报销");
        }
        erpReimPayReceiptVos.stream().forEach(item -> {
            if (Objects.isNull(item.getPayAmt()) || StringUtils.isEmpty(item.getPayDate())) {
                throw new AppException("存在金额或者日期为空的付款文件识别信息，请修正");
            }
        });
        return erpReimPayReceiptVos;
    }

    @Override
    public void updErpVcrDetailPzh(ErpVcrDetailDto dto) {
        //更新vcrDetail
        LambdaUpdateWrapper<ErpVcrDetailDto> vcrDetailWrapper = Wrappers.lambdaUpdate(ErpVcrDetailDto.class);
        vcrDetailWrapper.set(ErpVcrDetailDto::getPzh,dto.getPzh())
                    .eq(ErpVcrDetailDto::getIdpzh,dto.getIdpzh());
        erpVcrDetailWriteMapper.update(null,vcrDetailWrapper);
        //更新vcrItemDetail
        LambdaUpdateWrapper<ErpVcrItemDetailDto> vcrItemDetailWrapper = Wrappers.lambdaUpdate(ErpVcrItemDetailDto.class);
        vcrItemDetailWrapper.set(ErpVcrItemDetailDto::getPzh,dto.getPzh())
                .eq(ErpVcrItemDetailDto::getIdpzh,dto.getIdpzh());
        erpVcrItemDetailWriteMapper.update(null,vcrItemDetailWrapper);
    }
}
