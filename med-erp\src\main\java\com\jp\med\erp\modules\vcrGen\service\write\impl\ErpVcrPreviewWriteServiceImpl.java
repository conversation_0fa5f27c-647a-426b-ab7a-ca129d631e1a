package com.jp.med.erp.modules.vcrGen.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrPreviewDto;
import com.jp.med.erp.modules.vcrGen.mapper.write.ErpVcrPreviewWriteMapper;
import com.jp.med.erp.modules.vcrGen.service.write.ErpVcrPreviewWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 凭证预览映射表
 * <AUTHOR>
 * @email -
 * @date 2025-03-11 17:00:33
 */
@Service
@Transactional(readOnly = false)
public class ErpVcrPreviewWriteServiceImpl extends ServiceImpl<ErpVcrPreviewWriteMapper, ErpVcrPreviewDto> implements ErpVcrPreviewWriteService {
}
