package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import cn.hutool.core.util.StrUtil;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 最终汇总处理器
 * 负责对折旧数据进行最终的汇总、排序和格式化
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyFinalSummaryProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始进行最终汇总处理");

        List<AmsPropertyDepr2Vo> result = finalSummary(
                context.getResult(),
                context.getSourceCodeMap(),
                context.getQueryDto());

        context.setResult(result);

        log.debug("最终汇总处理完成，最终结果 {} 条记录", result.size());
    }

    /**
     * 最终汇总处理
     */
    private List<AmsPropertyDepr2Vo> finalSummary(List<AmsPropertyDepr2Vo> result, Map<String, String> sourceCodeMap,
            com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto queryDto) {

        String ym = queryDto.getYm();
        boolean useNewType = queryDto.getTypen();

        // 1. 最终排序
        result.sort(Comparator.comparing(AmsPropertyDepr2Vo::getDeptUseCode));

        // 2. 先复制一份原始数据为后续计算差额做准备
        List<AmsPropertyDepr2Vo> originalData = new ArrayList<>();
        for (AmsPropertyDepr2Vo vo : result) {
            AmsPropertyDepr2Vo copy = new AmsPropertyDepr2Vo();
            BeanUtils.copyProperties(vo, copy);
            originalData.add(copy);
        }

        // 3. 设置资金来源名称并格式化金额
        for (AmsPropertyDepr2Vo vo : result) {
            vo.setSourceName(sourceCodeMap.get(vo.getSourceCode()));

            if (vo.getSourceCode().startsWith("wl")) {
                vo.setSourceCode(vo.getSourceCode().substring(2));
            }

            // 确保所有金额字段保留2位小数 - 此处是最终展示前的格式化，可以保留
            formatAmountFields(vo);
        }

        // 4. 按科室、资产类型、资金来源分组合并
        Map<String, AmsPropertyDepr2Vo> mergedMap = result.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getDeptUseCode() + "-"
                                + (useNewType ? item.getAssetTypeCode() : item.getAssetTypeName()) + "-"
                                + item.getSourceCode(),
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                this::mergeDeprVoList)));

        result = new ArrayList<>(mergedMap.values());

        // 5. 根据科室代码过滤数据
        List<AmsPropertyDepr2Vo> filteredList = result.stream()
                .filter(item -> {
                    if (StrUtil.isNotEmpty(queryDto.getDeptCode())
                            && !item.getDeptUseCode().equals(queryDto.getDeptCode())) {
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        // 6. 构建科室代码和名称的映射关系
        filteredList.stream()
                .filter(Objects::nonNull)
                .peek(vo -> {
                    if (vo.getDeptUseCode() == null)
                        System.err.println("发现 null 键的元素: " + vo);
                })
                .filter(vo -> vo.getDeptUseCode() != null)
                .collect(Collectors.toMap(AmsPropertyDepr2Vo::getDeptUseCode, AmsPropertyDepr2Vo::getDeptUseName,
                        (e, r) -> e));

        Map<String, String> deptCodeNameMap = filteredList
                .stream()
                .collect(Collectors.toMap(AmsPropertyDepr2Vo::getDeptUseCode, AmsPropertyDepr2Vo::getDeptUseName,
                        (e, r) -> e));

        // 7. 处理原始数据后按照相同的条件进行过滤
        List<AmsPropertyDepr2Vo> originalFilteredData = originalData.stream()
                .filter(item -> {
                    if (StrUtil.isNotBlank(queryDto.getDeptCode())
                            && !item.getDeptUseCode().equals(queryDto.getDeptCode())) {
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        // 8. 计算差额并修正
        adjustAmountDifferences(filteredList, originalFilteredData);

        // 9. 按科室分组并计算小计
        addDepartmentSummaries(filteredList, deptCodeNameMap);

        // 10. 最终排序
        List<AmsPropertyDepr2Vo> sortedList = filteredList.stream()
                .sorted(Comparator.comparing(AmsPropertyDepr2Vo::getDeptUseCode)
                        .thenComparing(vo -> vo.getAssetTypeCode() == null ? "" : vo.getAssetTypeCode(),
                                Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        return sortedList;
    }

    /**
     * 格式化金额字段
     */
    private void formatAmountFields(AmsPropertyDepr2Vo vo) {
        if (vo.getMonthDeprAmt() != null) {
            vo.setMonthDeprAmt(vo.getMonthDeprAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getLastMonthDeprAmt() != null) {
            vo.setLastMonthDeprAmt(vo.getLastMonthDeprAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getLastMonthDeprAmtChange() != null) {
            vo.setLastMonthDeprAmtChange(vo.getLastMonthDeprAmtChange().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getDeprAmt() != null) {
            vo.setDeprAmt(vo.getDeprAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getDeprAmtSum() != null) {
            vo.setDeprAmtSum(vo.getDeprAmtSum().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getDeprRate() != null) {
            vo.setDeprRate(vo.getDeprRate().setScale(2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 合并折旧VO列表
     */
    private AmsPropertyDepr2Vo mergeDeprVoList(List<AmsPropertyDepr2Vo> list) {
        AmsPropertyDepr2Vo merged = new AmsPropertyDepr2Vo();
        AmsPropertyDepr2Vo first = list.get(0);
        BeanUtils.copyProperties(first, merged);
        merged.setDeptUseCode(first.getDeptUseCode());
        merged.setDeptUseName(first.getDeptUseName());
        merged.setAssetTypeCode(first.getAssetTypeCode());
        merged.setSourceCode(first.getSourceCode());

        // 计算汇总值并保留2位小数
        merged.setMonthDeprAmt(list.stream()
                .map(AmsPropertyDepr2Vo::getMonthDeprAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP));

        merged.setLastMonthDeprAmt(list.stream()
                .map(AmsPropertyDepr2Vo::getLastMonthDeprAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP));

        merged.setLastMonthDeprAmtChange(list.stream()
                .map(AmsPropertyDepr2Vo::getLastMonthDeprAmtChange)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP));

        merged.setDeprAmt(list.stream()
                .map(AmsPropertyDepr2Vo::getDeprAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP));

        merged.setDeprAmtSum(list.stream()
                .map(AmsPropertyDepr2Vo::getDeprAmtSum)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP));

        return merged;
    }

    /**
     * 调整金额差异
     */
    private void adjustAmountDifferences(List<AmsPropertyDepr2Vo> filteredList,
            List<AmsPropertyDepr2Vo> originalFilteredData) {
        // 计算最终汇总的数字 - 原始方式
        BigDecimal originalSumMonthDeprAmt = BigDecimal.ZERO;
        BigDecimal originalSumLastMonthDeprAmt = BigDecimal.ZERO;
        BigDecimal originalSumLastMonthDeprAmtChange = BigDecimal.ZERO;
        BigDecimal originalSumDeprAmt = BigDecimal.ZERO;
        BigDecimal originalSumDeprAmtSum = BigDecimal.ZERO;

        for (AmsPropertyDepr2Vo vo : originalFilteredData) {
            if (vo.getMonthDeprAmt() != null) {
                originalSumMonthDeprAmt = originalSumMonthDeprAmt.add(vo.getMonthDeprAmt());
            }
            if (vo.getLastMonthDeprAmt() != null) {
                originalSumLastMonthDeprAmt = originalSumLastMonthDeprAmt.add(vo.getLastMonthDeprAmt());
            }
            if (vo.getLastMonthDeprAmtChange() != null) {
                originalSumLastMonthDeprAmtChange = originalSumLastMonthDeprAmtChange
                        .add(vo.getLastMonthDeprAmtChange());
            }
            if (vo.getDeprAmt() != null) {
                originalSumDeprAmt = originalSumDeprAmt.add(vo.getDeprAmt());
            }
            if (vo.getDeprAmtSum() != null) {
                originalSumDeprAmtSum = originalSumDeprAmtSum.add(vo.getDeprAmtSum());
            }
        }

        // 计算最终汇总的数字 - 分组汇总方式
        BigDecimal mergedSumMonthDeprAmt = BigDecimal.ZERO;
        BigDecimal mergedSumLastMonthDeprAmt = BigDecimal.ZERO;
        BigDecimal mergedSumLastMonthDeprAmtChange = BigDecimal.ZERO;
        BigDecimal mergedSumDeprAmt = BigDecimal.ZERO;
        BigDecimal mergedSumDeprAmtSum = BigDecimal.ZERO;

        // 计算分组汇总方式的总和
        for (AmsPropertyDepr2Vo vo : filteredList) {
            if (vo.getMonthDeprAmt() != null) {
                mergedSumMonthDeprAmt = mergedSumMonthDeprAmt.add(vo.getMonthDeprAmt());
            }
            if (vo.getLastMonthDeprAmt() != null) {
                mergedSumLastMonthDeprAmt = mergedSumLastMonthDeprAmt.add(vo.getLastMonthDeprAmt());
            }
            if (vo.getLastMonthDeprAmtChange() != null) {
                mergedSumLastMonthDeprAmtChange = mergedSumLastMonthDeprAmtChange.add(vo.getLastMonthDeprAmtChange());
            }
            if (vo.getDeprAmt() != null) {
                mergedSumDeprAmt = mergedSumDeprAmt.add(vo.getDeprAmt());
            }
            if (vo.getDeprAmtSum() != null) {
                mergedSumDeprAmtSum = mergedSumDeprAmtSum.add(vo.getDeprAmtSum());
            }
        }

        // 先保持原始精度计算差值，然后四舍五入
        BigDecimal diffMonthDeprAmt = originalSumMonthDeprAmt.subtract(mergedSumMonthDeprAmt).setScale(2,
                RoundingMode.HALF_UP);
        BigDecimal diffLastMonthDeprAmt = originalSumLastMonthDeprAmt.subtract(mergedSumLastMonthDeprAmt).setScale(2,
                RoundingMode.HALF_UP);
        BigDecimal diffLastMonthDeprAmtChange = originalSumLastMonthDeprAmtChange
                .subtract(mergedSumLastMonthDeprAmtChange).setScale(2, RoundingMode.HALF_UP);
        BigDecimal diffDeprAmt = originalSumDeprAmt.subtract(mergedSumDeprAmt).setScale(2, RoundingMode.HALF_UP);
        BigDecimal diffDeprAmtSum = originalSumDeprAmtSum.subtract(mergedSumDeprAmtSum).setScale(2,
                RoundingMode.HALF_UP);

        // 打印调试信息
        log.info("原始累计合计: 本月原值总额={}, 上月原值总额={}, 变化总额={}, 月折旧总额={}, 累计折旧总额={}",
                originalSumMonthDeprAmt, originalSumLastMonthDeprAmt, originalSumLastMonthDeprAmtChange,
                originalSumDeprAmt, originalSumDeprAmtSum);
        log.info("处理后合计: 本月原值总额={}, 上月原值总额={}, 变化总额={}, 月折旧总额={}, 累计折旧总额={}",
                mergedSumMonthDeprAmt, mergedSumLastMonthDeprAmt, mergedSumLastMonthDeprAmtChange,
                mergedSumDeprAmt, mergedSumDeprAmtSum);

        // 对最后一条记录进行修正，补齐差值
        if (!filteredList.isEmpty() && (diffMonthDeprAmt.compareTo(BigDecimal.ZERO) != 0 ||
                diffLastMonthDeprAmt.compareTo(BigDecimal.ZERO) != 0 ||
                diffLastMonthDeprAmtChange.compareTo(BigDecimal.ZERO) != 0 ||
                diffDeprAmt.compareTo(BigDecimal.ZERO) != 0 ||
                diffDeprAmtSum.compareTo(BigDecimal.ZERO) != 0)) {

            // 找最后一条记录进行修正
            AmsPropertyDepr2Vo lastRecord = filteredList.get(filteredList.size() - 1);

            if (lastRecord != null) {
                // 记录日志
                log.info("发现汇总计算差额，进行修正：");
                log.info("本月原值差额：{}", diffMonthDeprAmt);
                log.info("上月原值差额：{}", diffLastMonthDeprAmt);
                log.info("原值变化差额：{}", diffLastMonthDeprAmtChange);
                log.info("月折旧额差额：{}", diffDeprAmt);
                log.info("累计折旧额差额：{}", diffDeprAmtSum);

                // 修正最后一条记录的金额
                adjustLastRecord(lastRecord, diffMonthDeprAmt, diffLastMonthDeprAmt,
                        diffLastMonthDeprAmtChange, diffDeprAmt, diffDeprAmtSum);
            }
        }
    }

    /**
     * 修正最后一条记录的金额
     */
    private void adjustLastRecord(AmsPropertyDepr2Vo lastRecord,
            BigDecimal diffMonthDeprAmt,
            BigDecimal diffLastMonthDeprAmt,
            BigDecimal diffLastMonthDeprAmtChange,
            BigDecimal diffDeprAmt,
            BigDecimal diffDeprAmtSum) {

        // 修正最后一条记录的金额
        if (lastRecord.getMonthDeprAmt() != null) {
            lastRecord.setMonthDeprAmt(lastRecord.getMonthDeprAmt().add(diffMonthDeprAmt));
        } else if (diffMonthDeprAmt.compareTo(BigDecimal.ZERO) != 0) {
            lastRecord.setMonthDeprAmt(diffMonthDeprAmt);
        }

        if (lastRecord.getLastMonthDeprAmt() != null) {
            lastRecord.setLastMonthDeprAmt(lastRecord.getLastMonthDeprAmt().add(diffLastMonthDeprAmt));
        } else if (diffLastMonthDeprAmt.compareTo(BigDecimal.ZERO) != 0) {
            lastRecord.setLastMonthDeprAmt(diffLastMonthDeprAmt);
        }

        if (lastRecord.getLastMonthDeprAmtChange() != null) {
            lastRecord.setLastMonthDeprAmtChange(
                    lastRecord.getLastMonthDeprAmtChange().add(diffLastMonthDeprAmtChange));
        } else if (diffLastMonthDeprAmtChange.compareTo(BigDecimal.ZERO) != 0) {
            lastRecord.setLastMonthDeprAmtChange(diffLastMonthDeprAmtChange);
        }

        if (lastRecord.getDeprAmt() != null) {
            lastRecord.setDeprAmt(lastRecord.getDeprAmt().add(diffDeprAmt));
        } else if (diffDeprAmt.compareTo(BigDecimal.ZERO) != 0) {
            lastRecord.setDeprAmt(diffDeprAmt);
        }

        if (lastRecord.getDeprAmtSum() != null) {
            lastRecord.setDeprAmtSum(lastRecord.getDeprAmtSum().add(diffDeprAmtSum));
        } else if (diffDeprAmtSum.compareTo(BigDecimal.ZERO) != 0) {
            lastRecord.setDeprAmtSum(diffDeprAmtSum);
        }
    }

    /**
     * 添加科室汇总数据
     */
    private void addDepartmentSummaries(List<AmsPropertyDepr2Vo> filteredList,
            Map<String, String> deptCodeNameMap) {

        // 完成补差后，再按科室代码分组并计算小计
        Map<String, AmsPropertyDepr2Vo> summaryByDeptUseCode = filteredList.stream()
                .collect(Collectors.groupingBy(
                        AmsPropertyDepr2Vo::getDeptUseCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                AmsPropertyDepr2Vo::summarize)));

        // 处理汇总数据并添加到过滤后的列表中
        summaryByDeptUseCode.forEach((key, value) -> {
            value.setDeptUseName(deptCodeNameMap.get(key) + "_小 计");
            value.setDeptUseCode(key);
            // 标记为小计记录
            value.setIsSummary(true);

            if (value.getLastMonthDeprAmtChange() == null) {
                value.setLastMonthDeprAmtChange(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            } else {
                value.setLastMonthDeprAmtChange(value.getLastMonthDeprAmtChange().setScale(2, RoundingMode.HALF_UP));
            }

            // 确保所有金额字段保留2位小数
            formatAmountFields(value);

            filteredList.add(value);
        });
    }
}
