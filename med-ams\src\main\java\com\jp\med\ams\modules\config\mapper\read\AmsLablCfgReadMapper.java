package com.jp.med.ams.modules.config.mapper.read;

import com.jp.med.ams.modules.config.dto.AmsLablCfgDto;
import com.jp.med.ams.modules.config.vo.AmsLablCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 资产标签配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 17:36:48
 */
@Mapper
public interface AmsLablCfgReadMapper extends BaseMapper<AmsLablCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsLablCfgVo> queryList(AmsLablCfgDto dto);
}
