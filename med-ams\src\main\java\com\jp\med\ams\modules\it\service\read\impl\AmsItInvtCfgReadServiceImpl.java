package com.jp.med.ams.modules.it.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.it.mapper.read.AmsItInvtCfgReadMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtCfgVo;
import com.jp.med.ams.modules.it.service.read.AmsItInvtCfgReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsItInvtCfgReadServiceImpl extends ServiceImpl<AmsItInvtCfgReadMapper, AmsItInvtCfgDto> implements AmsItInvtCfgReadService {

    @Autowired
    private AmsItInvtCfgReadMapper amsItInvtCfgReadMapper;

    @Override
    public List<AmsItInvtCfgVo> queryList(AmsItInvtCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtCfgReadMapper.queryList(dto);
    }
    @Override
    public List<AmsItInvtCfgVo> searchAlarmList(AmsItInvtCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtCfgReadMapper.queryAlarmList(dto);
    }


    @Override
    public List<AmsItInvtCfgVo> cfgList(AmsItInvtCfgDto dto) {
        return amsItInvtCfgReadMapper.queryList(dto);
    }

    /**
     * 查询耗材集合
     * */
    @Override
    public List<AmsItInvtCfgVo> queryConsumableList(AmsItInvtCfgDto dto) {
        return amsItInvtCfgReadMapper.queryConsumableList(dto);
    }

    @Override
    public List<String> searchCodeList(AmsItInvtCfgDto dto) {
        List<String> codeList = new ArrayList<>();
        if (dto.getChoice()!= null){
            for (String id: dto.getChoice()) {
                dto.setId(Integer.parseInt(id));
                List<AmsItInvtCfgVo> amsItInvtCfgVos = amsItInvtCfgReadMapper.queryConsumableList(dto);
                for (AmsItInvtCfgVo cfgVo:amsItInvtCfgVos) {
                    codeList.add(cfgVo.getCode());
                }
            }
        }
        return codeList;
    }
}
