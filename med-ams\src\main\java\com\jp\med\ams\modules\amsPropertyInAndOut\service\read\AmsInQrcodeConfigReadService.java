package com.jp.med.ams.modules.amsPropertyInAndOut.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInQrcodeConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInQrcodeConfigVo;

import java.util.List;

/**
 * 资产入库二维码配置
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
public interface AmsInQrcodeConfigReadService extends IService<AmsInQrcodeConfigDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsInQrcodeConfigVo> queryList(AmsInQrcodeConfigDto dto);
}

