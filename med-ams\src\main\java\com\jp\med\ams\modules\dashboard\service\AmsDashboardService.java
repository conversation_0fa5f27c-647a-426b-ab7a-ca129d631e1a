package com.jp.med.ams.modules.dashboard.service;

import com.jp.med.ams.modules.dashboard.dto.AmsDashboardFilterDto;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardChartVo;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardMetricsVo;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardTableVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 资产盘点仪表盘服务接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface AmsDashboardService {

    /**
     * 获取仪表盘指标数据
     *
     * @param filterDto 筛选条件
     * @return 指标数据
     */
    AmsDashboardMetricsVo getMetrics(AmsDashboardFilterDto filterDto);

    /**
     * 获取仪表盘图表数据
     *
     * @param filterDto 筛选条件
     * @return 图表数据
     */
    AmsDashboardChartVo getCharts(AmsDashboardFilterDto filterDto);

    /**
     * 获取仪表盘表格数据
     *
     * @param filterDto 筛选条件
     * @return 表格数据列表
     */
    List<AmsDashboardTableVo> getTableData(AmsDashboardFilterDto filterDto);

    /**
     * 获取完整的仪表盘数据
     *
     * @param filterDto 筛选条件
     * @return 包含指标、图表、表格的完整数据
     */
    Map<String, Object> getAllDashboardData(AmsDashboardFilterDto filterDto);

    /**
     * 导出仪表盘数据
     *
     * @param filterDto 筛选条件
     * @param response  HTTP响应对象
     */
    void exportDashboardData(AmsDashboardFilterDto filterDto, HttpServletResponse response);

    /**
     * 获取部门选项列表
     *
     * @return 部门选项
     */
    List<Map<String, Object>> getDepartmentOptions();

    /**
     * 获取资产类型选项列表
     *
     * @return 资产类型选项
     */
    List<Map<String, Object>> getAssetTypeOptions();
} 