package com.jp.med.ams.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.config.mapper.read.AmsTypeCfgReadMapper;
import com.jp.med.ams.modules.config.dto.AmsTypeCfgDto;
import com.jp.med.ams.modules.config.vo.AmsTypeCfgVo;
import com.jp.med.ams.modules.config.service.read.AmsTypeCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsTypeCfgReadServiceImpl extends ServiceImpl<AmsTypeCfgReadMapper, AmsTypeCfgDto> implements AmsTypeCfgReadService {

    @Autowired
    private AmsTypeCfgReadMapper amsTypeCfgReadMapper;

    @Override
    public List<AmsTypeCfgVo> queryList(AmsTypeCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return amsTypeCfgReadMapper.queryList(dto);
    }

}
