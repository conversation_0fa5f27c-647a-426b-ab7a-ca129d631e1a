<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.config.mapper.read.ErpReimSalaryTaskReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskVo" id="reimSalaryTaskMap">
        <result property="id" column="id"/>
        <result property="ffMth" column="ff_mth"/>
        <result property="num" column="num"/>
        <result property="shouldPay" column="should_pay"/>
        <result property="reducePay" column="reduce_pay"/>
        <result property="realPay" column="real_pay"/>
        <result property="remark" column="remark"/>
        <result property="crter" column="crter"/>
        <result property="crteTime" column="crte_time"/>
        <result property="reimFlag" column="reim_flag"/>
        <result property="salaryId" column="salary_id"/>
        <result property="reimId" column="reim_id"/>
        <result property="type" column="type"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskVo">
        select
            id as id,
            ff_mth as ffMth,
            num as num,
            should_pay as shouldPay,
            reduce_pay as reducePay,
            real_pay as realPay,
            remark as remark,
            crter as crter,
            crte_time as crteTime,
            reim_flag as reimFlag,
            salary_id as salaryId,
            reim_id as reimId,
            type as type
        from ecs_reim_salary_task
    </select>

</mapper>
