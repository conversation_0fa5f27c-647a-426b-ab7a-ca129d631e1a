package com.jp.med.ams.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产折旧配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:09:03
 */
@Data
public class AmsDeprCfgVo {

	/** ID */
	private Integer id;

	/** 折旧方法编码 */
	private String deprCode;

	/** 折旧方法名称 */
	private String deprName;

	/** 有效标志 */
	private String flag;

}
