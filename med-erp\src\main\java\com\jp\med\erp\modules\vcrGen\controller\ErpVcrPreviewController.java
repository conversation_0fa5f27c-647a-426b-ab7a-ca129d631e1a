package com.jp.med.erp.modules.vcrGen.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.erp.modules.vcrGen.dto.ErpVcrPreviewDto;
import com.jp.med.erp.modules.vcrGen.service.read.ErpVcrPreviewReadService;
import com.jp.med.erp.modules.vcrGen.service.write.ErpVcrPreviewWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 凭证预览映射表
 * <AUTHOR>
 * @email -
 * @date 2025-03-11 17:00:33
 */
@Api(value = "凭证预览映射表", tags = "凭证预览映射表")
@RestController
@RequestMapping("erpVcrPreview")
public class ErpVcrPreviewController {

    @Autowired
    private ErpVcrPreviewReadService erpVcrPreviewReadService;

    @Autowired
    private ErpVcrPreviewWriteService erpVcrPreviewWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询凭证预览映射表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody ErpVcrPreviewDto dto){
        return CommonResult.paging(erpVcrPreviewReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询凭证预览映射表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody ErpVcrPreviewDto dto){
        return CommonResult.success(erpVcrPreviewReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增凭证预览映射表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody ErpVcrPreviewDto dto){
        erpVcrPreviewWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改凭证预览映射表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody ErpVcrPreviewDto dto){
        erpVcrPreviewWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除凭证预览映射表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody ErpVcrPreviewDto dto){
        erpVcrPreviewWriteService.removeById(dto);
        return CommonResult.success();
    }

}
