package com.jp.med.erp.modules.config.mapper.read;

import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 应发工资报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Mapper
public interface ErpReimSalaryTaskDetailReadMapper extends BaseMapper<ErpReimSalaryTaskDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpReimSalaryTaskDetailVo> queryList(ErpReimSalaryTaskDetailDto dto);
}
