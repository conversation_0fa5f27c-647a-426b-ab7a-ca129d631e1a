package com.jp.med.ams.modules.it.mapper.write;

import com.jp.med.ams.modules.it.dto.AmsItInvtSupplementlistDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.it.dto.ModifyFileDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @email -
 * @date 2023-12-14 17:42:02
 */
@Mapper
public interface AmsItInvtSupplementlistWriteMapper extends BaseMapper<AmsItInvtSupplementlistDto> {

    void batchUpdateFile(List<ModifyFileDto> modifyFileDetails);
}
