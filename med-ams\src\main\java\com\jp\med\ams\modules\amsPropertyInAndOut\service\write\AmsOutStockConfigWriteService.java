package com.jp.med.ams.modules.amsPropertyInAndOut.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInStockConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
public interface AmsOutStockConfigWriteService extends IService<AmsInStockConfigDto> {

    void saveConfig(AmsInStockConfigDto dto);

    void removeConfig(AmsInStockConfigDto dto);

    void startOutProcess(AmsOutStockAuditDto dto);
}

