-- =================================================================================================
-- 出库申请表组织字段更新脚本
-- 功能：将修复后的组织信息同步到mmis_outbound_apply表
-- 简化版：直接更新目标表的组织字段
-- =================================================================================================

BEGIN;

-- 检查当前目标表状态
SELECT 
    '更新前状态检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_taget_org IS NOT NULL THEN 1 END) as has_target_org,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) as has_target_org_id
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16';

-- 检查源表修复状态
SELECT 
    '源表修复状态' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_org IS NOT NULL THEN 1 END) as has_org_name,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) as has_org_id
FROM mmis_temp_xinxike_outbound_six;

-- 显示源表中的组织数据分布
SELECT 
    '源表组织数据分布' as data_type,
    out_org,
    out_target_org_id,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL
GROUP BY out_org, out_target_org_id
ORDER BY record_count DESC;

-- 方案1：如果目标表中所有记录都应该使用相同的组织信息
-- 使用源表中最常见的组织信息
WITH most_common_org AS (
    SELECT 
        out_org,
        out_target_org_id,
        COUNT(*) as cnt
    FROM mmis_temp_xinxike_outbound_six
    WHERE out_org IS NOT NULL AND out_target_org_id IS NOT NULL
    GROUP BY out_org, out_target_org_id
    ORDER BY COUNT(*) DESC
    LIMIT 1
)
UPDATE mmis_outbound_apply 
SET 
    out_taget_org = (SELECT out_org FROM most_common_org),
    out_target_org_id = (SELECT out_target_org_id FROM most_common_org)
WHERE hospital_id = 'zjxrmyy' 
  AND bill_date = '2025-07-16'
  AND (out_taget_org IS NULL OR out_target_org_id IS NULL);

-- 验证更新结果
SELECT 
    '更新后状态检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_taget_org IS NOT NULL THEN 1 END) as has_target_org,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) as has_target_org_id,
    ROUND(
        COUNT(CASE WHEN out_taget_org IS NOT NULL AND out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
    ) as completion_rate
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16';

-- 显示更新后的数据样例
SELECT 
    '更新后数据样例' as sample_type,
    docment_num,
    out_taget_org,
    out_target_org_id,
    appy_org_id,
    appyer
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16'
ORDER BY docment_num
LIMIT 5;

-- 验证组织ID的有效性
SELECT 
    '组织ID有效性验证' as validation_type,
    oa.out_taget_org,
    oa.out_target_org_id,
    ho.org_name as verified_org_name,
    COUNT(*) as record_count,
    CASE 
        WHEN ho.org_id IS NOT NULL THEN '✅ 有效'
        ELSE '❌ 无效'
    END as validity_status
FROM mmis_outbound_apply oa
LEFT JOIN hrm_org ho ON oa.out_target_org_id = ho.org_id 
    AND ho.hospital_id = 'zjxrmyy' 
    AND ho.active_flag = '1'
WHERE oa.hospital_id = 'zjxrmyy' 
  AND oa.bill_date = '2025-07-16'
  AND oa.out_target_org_id IS NOT NULL
GROUP BY oa.out_taget_org, oa.out_target_org_id, ho.org_name;

-- 最终总结
SELECT 
    '🎯 更新总结' as summary_type,
    COUNT(*) as total_updated_records,
    COUNT(CASE WHEN out_taget_org IS NOT NULL AND out_target_org_id IS NOT NULL THEN 1 END) as successfully_updated,
    CASE 
        WHEN COUNT(CASE WHEN out_taget_org IS NOT NULL AND out_target_org_id IS NOT NULL THEN 1 END) = COUNT(*)
        THEN '✅ 所有记录更新成功'
        ELSE '⚠️ 部分记录未更新'
    END as update_status
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16';

COMMIT;
