package com.jp.med.ams.modules.changes.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产借用信息表
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 16:01:14
 */
@Data
public class AmsChngBrwgVo {

	/** id */
	private Integer id;

	/** 单据号 */
	private String docNum;

	/** 办理状态 */
	private String prosstas;

	/** 业务状态 */
	private String busstas;

	/** 借用科室 */
	private String loaneeDept;

	/** 借用科室名称 */
	private String loaneeDeptName;

	/** 借用人 */
	private String loanee;

	/** 借用时间 */
	private String loaneeTime;

	/** 预计归还时间 */
	private String expRtnTime;

	/** 实际归还时间 */
	private String actRtnTime;

	/** 备注 */
	private String remarks;

	/** 归还备注 */
	private String backRemarks;

	/** 归还时间 */
	private String backTime;

	/** 创建人 */
	private String crter;

	/** 创建科室 */
	private String crterDept;

	/** 创建科室名称 */
	private String crterDeptName;

	/** 创建时间 */
	private String createTime;

	/** 医疗机构ID */
	private String hospitalId;

	/** 审核备注 */
	private String chkRemarks;

	/** 审核时间 */
	private String chkTime;

	/** 创建人名称 */
	private String crterName;

	/** 借用人名称 */
	private String loaneeName;

	/** 资产类型名称 */
	private String assetTypeName;

	/** 资产名称 */
	private String assetName;

	/** 资产类型编码 */
	private String assetTypeCode;

	/** 固定资产编码 */
	private String faCode;

	/** 申请数量 */
	private Integer applyCount;

	/** 待归还数量 */
	private Integer repayCount;

	/** 总数 */
	private Integer allCount;

}
