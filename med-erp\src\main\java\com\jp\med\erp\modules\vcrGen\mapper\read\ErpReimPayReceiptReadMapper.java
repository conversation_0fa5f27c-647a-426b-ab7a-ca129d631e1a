package com.jp.med.erp.modules.vcrGen.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimPayReceiptDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用报销付款回单信息
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 16:02:12
 */
@Mapper
public interface ErpReimPayReceiptReadMapper extends BaseMapper<ErpReimPayReceiptDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpReimPayReceiptVo> queryList(ErpReimPayReceiptDto dto);

    /**
     * 查询列表by reimId
     * @param supType
     * @return
     */
    List<ErpReimPayReceiptVo> queryListById(@Param("supType")String supType,@Param("id") Integer id);
}
