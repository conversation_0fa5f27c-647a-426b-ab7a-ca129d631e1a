package com.jp.med.ams.modules.it.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 耗材申请详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
@TableName("ams_it_apply_detail" )
public class AmsItApplyDetailDto extends CommonQueryDto {


    @TableId("id")
    private Integer id;

    /** 申请id */
    @TableField("apply_id")
    private Integer applyId;

    /** 名称 */
    @TableField("name")
    private String name;

    /** 耗材名称 */
    @TableField("name2")
    private String name2;

    /** 耗材型号 */
    @TableField("model2")
    private String model2;

    /** 申请物品类型(0:设备,1:耗材) */
    @TableField("type")
    private String type;

    /** 型号 */
    @TableField("model")
    private String model;

    /** 申请数量 */
    @TableField("num")
    private Integer num;

    /** 历史申请数量 */
    @TableField("his_num")
    private Integer hisNum;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 更新人 */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

    /** 删除人 */
    @TableField("delter")
    private String delter;

    /** 删除时间 */
    @TableField("delete_time")
    private String deleteTime;

    /** 有效标志(1:删除) */
    @TableField("active_flag")
    private String activeFlag;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

}
