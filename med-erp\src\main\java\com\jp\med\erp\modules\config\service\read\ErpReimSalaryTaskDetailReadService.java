package com.jp.med.erp.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskDetailVo;

import java.util.List;

/**
 * 应发工资报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
public interface ErpReimSalaryTaskDetailReadService extends IService<ErpReimSalaryTaskDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpReimSalaryTaskDetailVo> queryList(ErpReimSalaryTaskDetailDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<ErpReimSalaryTaskDetailVo> queryPageList(ErpReimSalaryTaskDetailDto dto);
}

