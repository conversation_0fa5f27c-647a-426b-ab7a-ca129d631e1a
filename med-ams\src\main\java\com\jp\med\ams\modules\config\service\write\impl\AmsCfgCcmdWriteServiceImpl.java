package com.jp.med.ams.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.config.mapper.write.AmsCfgCcmdWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsCfgCcmdDto;
import com.jp.med.ams.modules.config.service.write.AmsCfgCcmdWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 09:35:21
 */
@Service
@Transactional(readOnly = false)
public class AmsCfgCcmdWriteServiceImpl extends ServiceImpl<AmsCfgCcmdWriteMapper, AmsCfgCcmdDto> implements AmsCfgCcmdWriteService {
}
