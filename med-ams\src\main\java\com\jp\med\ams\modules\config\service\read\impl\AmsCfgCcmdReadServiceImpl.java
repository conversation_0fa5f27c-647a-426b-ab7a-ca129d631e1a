package com.jp.med.ams.modules.config.service.read.impl;

import com.jp.med.common.util.TreeNewUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.config.mapper.read.AmsCfgCcmdReadMapper;
import com.jp.med.ams.modules.config.dto.AmsCfgCcmdDto;
import com.jp.med.ams.modules.config.vo.AmsCfgCcmdVo;
import com.jp.med.ams.modules.config.service.read.AmsCfgCcmdReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsCfgCcmdReadServiceImpl extends ServiceImpl<AmsCfgCcmdReadMapper, AmsCfgCcmdDto> implements AmsCfgCcmdReadService {

    @Autowired
    private AmsCfgCcmdReadMapper amsCfgCcmdReadMapper;

    @Override
    public List<AmsCfgCcmdVo> queryList(AmsCfgCcmdDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<AmsCfgCcmdVo> cfgCcmdVos = amsCfgCcmdReadMapper.queryList(dto);
        if (cfgCcmdVos.isEmpty()){
            return cfgCcmdVos;
        }
        TreeNewUtil<String, AmsCfgCcmdVo> treeNewUtil = new TreeNewUtil<>();
        return treeNewUtil.buildTree(cfgCcmdVos);
    }

}
