package com.jp.med.ams.modules.changes.strategy.approval.impl;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.strategy.approval.AbstractApprovalProcessor;
import com.jp.med.ams.modules.changes.strategy.approval.ApprovalContext;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.common.exception.AppException;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 资产拆分审批处理器
 * 处理资产拆分类型的审批业务逻辑
 * 优先级：2
 */
@Component
@Order(2)
public class AssetSplitApprovalProcessor extends AbstractApprovalProcessor {
    
    @Override
    public boolean supports(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto) {
        return "8".equals(changeRecord.getRedcWay());
    }
    
    @Override
    protected void validateApproval(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto) {
        super.validateApproval(changeRecord, approvalDto);
        // 拆分操作不允许在此处审批
        throw new AppException("拆分操作请在页面的拆分菜单进行");
    }
    
    @Override
    protected AmsPropertyDto createPropertyUpdate(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto, ApprovalContext context) {
        // 拆分操作不在此处处理资产更新
        return null;
    }
}
