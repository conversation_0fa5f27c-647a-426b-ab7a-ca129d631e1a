package com.jp.med.ams.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 资产标签配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 17:36:48
 */
@Data
@TableName("ams_labl_cfg" )
public class AmsLablCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 资产卡片名称 */
    @TableField("asset_card_name")
    private String assetCardName;

    /** 图片*/
    @TableField("img")
    private String img;

    /** 附件 */
    @TableField("attachment")
    private String attachment;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 有效标志 */
    @TableField("flag")
    private String flag;

    /** 医疗机构编号 */
    @TableField("hospital_id")
    private String hospitalId;

    @TableField(exist = false)
    private MultipartFile imgFile;

    @TableField(exist = false)
    private MultipartFile cardFile;

}
