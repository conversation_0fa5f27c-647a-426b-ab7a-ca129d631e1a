package com.jp.med.ams.modules.depr.controller;

import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.service.read.AmsPropertyDeprReadService;
import com.jp.med.ams.modules.depr.service.write.impl.AmsPropertyDeprWriteServiceImpl;
import com.jp.med.common.dto.erp.ErpPropertyDeprDto;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 资产折旧表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 20:21:29
 */
@Api(value = "资产折旧表", tags = "资产折旧表")
@RestController
@RequestMapping("amsPropertyDepr")
public class AmsPropertyDeprController {

    @Autowired
    private AmsPropertyDeprReadService amsPropertyDeprReadService;

    @Autowired
    private AmsPropertyDeprWriteServiceImpl amsPropertyDeprWriteService;


    /**
     * 列表
     */
    @ApiOperation("查询资产折旧表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsPropertyDeprDto dto) {
        return CommonResult.paging(amsPropertyDeprReadService.queryList(dto));
    }

    // @ApiOperation("查询资产折汇总")
    // @PostMapping("/queryDeprSummary")
    // public CommonResult<?> queryDeprSummary(@RequestBody AmsPropertyDeprDto dto){
    // return
    // CommonResult.success(amsPropertyDeprReadService.queryDeprSummary(dto));
    // }

    @ApiOperation("查询资产折汇总2")
    @PostMapping("/queryDeprSummary2")
    public CommonResult<?> queryDeprSummary2(@RequestBody AmsPropertyDeprDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsPropertyDeprReadService.queryDeprSummary2(dto));
    }

    @ApiOperation("查询资产折汇总2")
    @PostMapping("/erpQueryDeprSummary2")
    public CommonResult<?> erpQueryDeprSummary2(@RequestBody ErpPropertyDeprDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsPropertyDeprReadService.erpQueryDeprSummary2(dto));
    }

    @ApiOperation("查询资产折旧年度月折旧")
    @PostMapping("/queryDeprMonth")
    public CommonResult<?> queryDeprMonth(@RequestBody AmsPropertyDeprDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsPropertyDeprReadService.queryDeprMonth(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产折旧表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsPropertyDeprDto dto) {
        amsPropertyDeprWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产折旧表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsPropertyDeprDto dto) {
        amsPropertyDeprWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产折旧表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsPropertyDeprDto dto) {
        amsPropertyDeprWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询资产价值构成分析表")
    @PostMapping("/queryValueAnalysis")
    public CommonResult<?> queryValueAnalysis(@RequestBody AmsPropertyDeprDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsPropertyDeprReadService.queryValueAnalysis(dto));
    }

}
