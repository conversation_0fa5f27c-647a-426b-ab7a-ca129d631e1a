package com.jp.med.ams.modules.config.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.List;

/**
 * 资产配置表
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:47:25
 */
@Mapper
public interface AmsBasicCfgReadMapper extends BaseMapper<AmsBasicCfgDto> {

    /**
     * 查询列表
     * 
     * @param dto
     * @return
     */
    List<AmsBasicCfgVo> queryList(AmsBasicCfgDto dto);

    /**
     * 查询资金来源 by code
     *
     * @param code
     * @return
     */
    @Select("SELECT * FROM ams_basic_cfg WHERE code = #{code} and type = '资金来源'")
    AmsBasicCfgDto querySourceByCode(String code);

    /**
     * 查询资金来源 by name
     *
     * @param name
     * @return
     */
    @Select("SELECT * FROM ams_basic_cfg WHERE name = #{name} and type = '资金来源'")
    AmsBasicCfgDto querySourceByName(String name);

    /**
     * 查询混合资金来源多对一关系
     */
    List<AmsBasicCfgDto> queryMixSourceRef();


    List<HashMap<String, Object>> queryEcsItemCfgList(AmsBasicCfgDto dto);
}
