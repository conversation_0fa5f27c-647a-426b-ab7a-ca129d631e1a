package com.jp.med.ams.modules.changes.service.read.impl;

import com.jp.med.ams.modules.changes.dto.AmsAssetBorrowingStatsDto;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.service.read.AmsAssetBorrowingDashboardService;
import com.jp.med.ams.modules.changes.service.read.AmsChngBrwgReadService;
import com.jp.med.ams.modules.changes.vo.AmsAssetBorrowingDashboardVo;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.common.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资产借用仪表盘服务实现类
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class AmsAssetBorrowingDashboardServiceImpl implements AmsAssetBorrowingDashboardService {

    @Autowired
    private AmsChngBrwgReadService amsChngBrwgReadService;

    @Override
    public AmsAssetBorrowingDashboardVo getDashboardData(AmsAssetBorrowingStatsDto dto) {
        log.info("🎯 获取仪表盘完整数据: {}", dto);

        try {
            return AmsAssetBorrowingDashboardVo.builder()
                    .statsOverview(getStatsOverview(dto))
                    .myApplicationStats(getMyApplicationStats(dto))
                    .pendingAuditStats(getPendingAuditStats(dto))
                    .myBorrowingStats(getMyBorrowingStats(dto))
                    .quickActions(getQuickActions(dto))
                    .recentActivities(getRecentActivities(dto))
                    .expiryReminders(getExpiryReminders(dto))
                    .popularAssets(getPopularAssets(dto))
                    .deptBorrowingRanks(getDeptBorrowingRanks(dto))
                    .trendAnalysis(getTrendAnalysis(dto))
                    .build();
        } catch (Exception e) {
            log.error("❌ 获取仪表盘数据失败", e);
            return new AmsAssetBorrowingDashboardVo();
        }
    }

    @Override
    public AmsAssetBorrowingDashboardVo.StatsOverview getStatsOverview(AmsAssetBorrowingStatsDto dto) {
        log.info("📊 获取统计概览: {}", dto);

        try {
            // 查询所有借用记录
            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            List<AmsChngBrwgVo> allRecords = amsChngBrwgReadService.queryList(queryDto);

            // 统计各种状态的数量
            int totalApplications = allRecords.size();
            int pendingAudit = (int) allRecords.stream().filter(r -> "1".equals(r.getProsstas())).count();
            int activeBorrowings = (int) allRecords.stream().filter(r -> "4".equals(r.getProsstas()))
                    .count();

            // 今日归还数
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            int todayReturns = (int) allRecords.stream()
                    .filter(r -> "5".equals(r.getProsstas()) &&
                            r.getActRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime actTime = parseDateTime(r.getActRtnTime());
                        return actTime != null && actTime.isAfter(todayStart);
                    })
                    .count();

            // 逾期数量
            LocalDateTime now = LocalDateTime.now();
            int overdueCount = (int) allRecords.stream()
                    .filter(r -> "4".equals(r.getProsstas()) &&
                            r.getExpRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime expTime = parseDateTime(r.getExpRtnTime());
                        return expTime != null && expTime.isBefore(now);
                    })
                    .count();

            // 即将到期数量（3天内）
            LocalDateTime threeDaysLater = now.plusDays(dto.getExpiringDays());
            int expiringCount = (int) allRecords.stream()
                    .filter(r -> "4".equals(r.getProsstas()) &&
                            r.getExpRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime expTime = parseDateTime(r.getExpRtnTime());
                        return expTime != null && expTime.isAfter(now)
                                && expTime.isBefore(threeDaysLater);
                    })
                    .count();

            // 本月新增申请
            LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0)
                    .withSecond(0);
            int monthlyNewApplications = (int) allRecords.stream()
                    .filter(r -> r.getCreateTime() != null)
                    .filter(r -> {
                        LocalDateTime createTime = parseDateTime(r.getCreateTime());
                        return createTime != null && createTime.isAfter(monthStart);
                    })
                    .count();

            // 按时归还率
            List<AmsChngBrwgVo> returnedRecords = allRecords.stream()
                    .filter(r -> "5".equals(r.getProsstas()) && r.getActRtnTime() != null)
                    .collect(Collectors.toList());

            long onTimeReturns = returnedRecords.stream()
                    .filter(r -> r.getExpRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime actTime = parseDateTime(r.getActRtnTime());
                        LocalDateTime expTime = parseDateTime(r.getExpRtnTime());
                        return actTime != null && expTime != null && !actTime.isAfter(expTime);
                    })
                    .count();

            BigDecimal onTimeReturnRate = returnedRecords.isEmpty() ? BigDecimal.ZERO
                    : BigDecimal.valueOf(onTimeReturns)
                    .divide(BigDecimal.valueOf(returnedRecords.size()), 4,
                            RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));

            return AmsAssetBorrowingDashboardVo.StatsOverview.builder()
                    .totalApplications(totalApplications)
                    .pendingAudit(pendingAudit)
                    .activeBorrowings(activeBorrowings)
                    .todayReturns(todayReturns)
                    .overdueCount(overdueCount)
                    .expiringCount(expiringCount)
                    .monthlyNewApplications(monthlyNewApplications)
                    .onTimeReturnRate(onTimeReturnRate)
                    .build();

        } catch (Exception e) {
            log.error("❌ 获取统计概览失败", e);
            return AmsAssetBorrowingDashboardVo.StatsOverview.builder().build();
        }
    }

    @Override
    public AmsAssetBorrowingDashboardVo.MyApplicationStats getMyApplicationStats(AmsAssetBorrowingStatsDto dto) {
        log.info("👤 获取我的申请统计: {}", dto);

        try {
            // 查询当前用户的申请记录
            String currentUserId = UserContext.getEmpCode();
            if (currentUserId == null) {
                log.warn("⚠️ 无法获取当前用户信息");
                return AmsAssetBorrowingDashboardVo.MyApplicationStats.builder().build();
            }

            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setCrter(currentUserId);
            // 设置分页参数，避免查询过多数据
            queryDto.setPageNum(1);
            queryDto.setPageSize(1000); // 设置较大的页面大小以获取用户的所有记录
            List<AmsChngBrwgVo> userRecords = amsChngBrwgReadService.queryList(queryDto);

            int pending = (int) userRecords.stream().filter(r -> "1".equals(r.getProsstas())).count();
            int approved = (int) userRecords.stream().filter(r -> "2".equals(r.getProsstas())).count();
            int rejected = (int) userRecords.stream().filter(r -> "3".equals(r.getProsstas())).count();
            int borrowing = (int) userRecords.stream().filter(r -> "4".equals(r.getProsstas())).count();

            // 本月申请数
            LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0)
                    .withSecond(0);
            int monthlyApplications = (int) userRecords.stream()
                    .filter(r -> r.getCreateTime() != null)
                    .filter(r -> {
                        LocalDateTime createTime = parseDateTime(r.getCreateTime());
                        return createTime != null && createTime.isAfter(monthStart);
                    })
                    .count();

            // 申请通过率
            int totalProcessed = approved + rejected;
            BigDecimal approvalRate = totalProcessed == 0 ? BigDecimal.ZERO
                    : BigDecimal.valueOf(approved)
                    .divide(BigDecimal.valueOf(totalProcessed), 4,
                            RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));

            return AmsAssetBorrowingDashboardVo.MyApplicationStats.builder()
                    .pending(pending)
                    .approved(approved)
                    .rejected(rejected)
                    .borrowing(borrowing)
                    .monthlyApplications(monthlyApplications)
                    .approvalRate(approvalRate)
                    .build();

        } catch (Exception e) {
            log.error("❌ 获取我的申请统计失败", e);
            return AmsAssetBorrowingDashboardVo.MyApplicationStats.builder().build();
        }
    }

    @Override
    public AmsAssetBorrowingDashboardVo.PendingAuditStats getPendingAuditStats(AmsAssetBorrowingStatsDto dto) {
        log.info("⏳ 获取待审核统计: {}", dto);

        try {
            // 查询待审核记录
            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setProsstas("1"); // 申请中状态
            // 设置分页参数
            queryDto.setPageNum(1);
            queryDto.setPageSize(1000);
            List<AmsChngBrwgVo> pendingRecords = amsChngBrwgReadService.queryList(queryDto);

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime todayStart = now.withHour(0).withMinute(0).withSecond(0);
            LocalDateTime tomorrowStart = now.plusDays(1).withHour(0).withMinute(0).withSecond(0);

            // 紧急申请（明天或今天就要借用）
            int urgent = (int) pendingRecords.stream()
                    .filter(r -> r.getLoaneeTime() != null)
                    .filter(r -> {
                        LocalDateTime loaneeTime = parseDateTime(r.getLoaneeTime());
                        return loaneeTime != null && loaneeTime.isBefore(tomorrowStart);
                    })
                    .count();

            // 超时申请（申请超过3天未处理）
            LocalDateTime threeDaysAgo = now.minusDays(3);
            int overdue = (int) pendingRecords.stream()
                    .filter(r -> r.getCreateTime() != null)
                    .filter(r -> {
                        LocalDateTime createTime = parseDateTime(r.getCreateTime());
                        return createTime != null && createTime.isBefore(threeDaysAgo);
                    })
                    .count();

            // 今日新增
            int today = (int) pendingRecords.stream()
                    .filter(r -> r.getCreateTime() != null)
                    .filter(r -> {
                        LocalDateTime createTime = parseDateTime(r.getCreateTime());
                        return createTime != null && createTime.isAfter(todayStart);
                    })
                    .count();

            int total = pendingRecords.size();

            // 平均处理时间（基于已处理的记录计算）
            AmsChngBrwgDto processedQueryDto = new AmsChngBrwgDto();
            List<AmsChngBrwgVo> processedRecords = amsChngBrwgReadService.queryList(processedQueryDto)
                    .stream()
                    .filter(r -> ("2".equals(r.getProsstas()) || "3".equals(r.getProsstas())) &&
                            r.getCreateTime() != null && r.getChkTime() != null)
                    .collect(Collectors.toList());

            BigDecimal avgProcessingHours = BigDecimal.ZERO;
            if (!processedRecords.isEmpty()) {
                double totalHours = processedRecords.stream()
                        .mapToDouble(r -> {
                            LocalDateTime createTime = parseDateTime(r.getCreateTime());
                            LocalDateTime chkTime = parseDateTime(r.getChkTime());
                            if (createTime != null && chkTime != null) {
                                long minutes = java.time.Duration
                                        .between(createTime, chkTime)
                                        .toMinutes();
                                return minutes / 60.0;
                            }
                            return 0.0;
                        })
                        .sum();
                avgProcessingHours = BigDecimal.valueOf(totalHours / processedRecords.size())
                        .setScale(2, RoundingMode.HALF_UP);
            }

            return AmsAssetBorrowingDashboardVo.PendingAuditStats.builder()
                    .urgent(urgent)
                    .overdue(overdue)
                    .today(today)
                    .total(total)
                    .avgProcessingHours(avgProcessingHours)
                    .build();

        } catch (Exception e) {
            log.error("❌ 获取待审核统计失败", e);
            return AmsAssetBorrowingDashboardVo.PendingAuditStats.builder().build();
        }
    }

    @Override
    public AmsAssetBorrowingDashboardVo.MyBorrowingStats getMyBorrowingStats(AmsAssetBorrowingStatsDto dto) {
        log.info("📋 获取我的借用统计: {}", dto);

        try {
            // 查询当前用户的借用记录
            String currentUserId = UserContext.getEmpCode();
            if (currentUserId == null) {
                log.warn("⚠️ 无法获取当前用户信息");
                return AmsAssetBorrowingDashboardVo.MyBorrowingStats.builder().build();
            }

            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setLoanee(currentUserId);
            // 设置分页参数
            queryDto.setPageNum(1);
            queryDto.setPageSize(1000);
            List<AmsChngBrwgVo> userBorrowings = amsChngBrwgReadService.queryList(queryDto);

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime threeDaysLater = now.plusDays(dto.getExpiringDays());

            int active = (int) userBorrowings.stream().filter(r -> "4".equals(r.getProsstas())).count();

            int expiring = (int) userBorrowings.stream()
                    .filter(r -> "4".equals(r.getProsstas()) &&
                            r.getExpRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime expTime = parseDateTime(r.getExpRtnTime());
                        return expTime != null && expTime.isAfter(now)
                                && expTime.isBefore(threeDaysLater);
                    })
                    .count();

            int overdue = (int) userBorrowings.stream()
                    .filter(r -> "4".equals(r.getProsstas()) &&
                            r.getExpRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime expTime = parseDateTime(r.getExpRtnTime());
                        return expTime != null && expTime.isBefore(now);
                    })
                    .count();

            int returned = (int) userBorrowings.stream().filter(r -> "5".equals(r.getProsstas())).count();

            // 平均借用天数
            List<AmsChngBrwgVo> completedBorrowings = userBorrowings.stream()
                    .filter(r -> "5".equals(r.getProsstas()) &&
                            r.getLoaneeTime() != null && r.getActRtnTime() != null)
                    .collect(Collectors.toList());

            BigDecimal avgBorrowingDays = BigDecimal.ZERO;
            if (!completedBorrowings.isEmpty()) {
                double totalDays = completedBorrowings.stream()
                        .mapToDouble(r -> {
                            LocalDateTime loaneeTime = parseDateTime(r.getLoaneeTime());
                            LocalDateTime actRtnTime = parseDateTime(r.getActRtnTime());
                            if (loaneeTime != null && actRtnTime != null) {
                                long days = java.time.Duration
                                        .between(loaneeTime, actRtnTime)
                                        .toDays();
                                return Math.max(1, days); // 至少1天
                            }
                            return 1.0; // 默认1天
                        })
                        .sum();
                avgBorrowingDays = BigDecimal.valueOf(totalDays / completedBorrowings.size())
                        .setScale(1, RoundingMode.HALF_UP);
            }

            return AmsAssetBorrowingDashboardVo.MyBorrowingStats.builder()
                    .active(active)
                    .expiring(expiring)
                    .overdue(overdue)
                    .returned(returned)
                    .avgBorrowingDays(avgBorrowingDays)
                    .build();

        } catch (Exception e) {
            log.error("❌ 获取我的借用统计失败", e);
            return AmsAssetBorrowingDashboardVo.MyBorrowingStats.builder().build();
        }
    }

    @Override
    public AmsAssetBorrowingDashboardVo.QuickActions getQuickActions(AmsAssetBorrowingStatsDto dto) {
        log.info("⚡ 获取快捷操作数据: {}", dto);

        try {
            // 这里需要根据实际的资产表来查询可申请资产数量
            // 暂时返回模拟数据
            int availableAssets = 150; // 可申请资产数量

            // 查询待归还数量（当前用户借用中的资产）
            String currentUserId = UserContext.getEmpCode();
            if (currentUserId == null) {
                log.warn("⚠️ 无法获取当前用户信息");
                return AmsAssetBorrowingDashboardVo.QuickActions.builder().build();
            }

            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setLoanee(currentUserId);
            // 设置分页参数
            queryDto.setPageNum(1);
            queryDto.setPageSize(1000);
            queryDto.setProsstas("4"); // 借用中状态
            List<AmsChngBrwgVo> activeBorrowings = amsChngBrwgReadService.queryList(queryDto);
            int pendingReturns = activeBorrowings.size();

            // 可续期数量（即将到期但还未逾期的）
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime threeDaysLater = now.plusDays(dto.getExpiringDays());
            int renewableCount = (int) activeBorrowings.stream()
                    .filter(r -> r.getExpRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime expTime = parseDateTime(r.getExpRtnTime());
                        return expTime != null && expTime.isAfter(now)
                                && expTime.isBefore(threeDaysLater);
                    })
                    .count();

            // 待审核数量（如果用户有审核权限）
            AmsChngBrwgDto auditQueryDto = new AmsChngBrwgDto();
            auditQueryDto.setProsstas("1"); // 申请中状态
            List<AmsChngBrwgVo> pendingAuditList = amsChngBrwgReadService.queryList(auditQueryDto);
            int pendingAudits = pendingAuditList.size();

            return AmsAssetBorrowingDashboardVo.QuickActions.builder()
                    .availableAssets(availableAssets)
                    .pendingReturns(pendingReturns)
                    .renewableCount(renewableCount)
                    .pendingAudits(pendingAudits)
                    .build();

        } catch (Exception e) {
            log.error("❌ 获取快捷操作数据失败", e);
            return AmsAssetBorrowingDashboardVo.QuickActions.builder().build();
        }
    }

    // 其他方法的实现将在下一个文件中继续...

    @Override
    public List<AmsAssetBorrowingDashboardVo.RecentActivity> getRecentActivities(AmsAssetBorrowingStatsDto dto) {
        log.info("📝 获取最近活动列表: {}", dto);

        try {
            // 查询最近的借用记录
            String currentUserId = UserContext.getEmpCode();
            if (currentUserId == null) {
                log.warn("⚠️ 无法获取当前用户信息");
                return new ArrayList<>();
            }

            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setCrter(currentUserId);
            // 设置分页参数，限制查询数量
            queryDto.setPageNum(1);
            queryDto.setPageSize(50); // 最近活动只需要较少的记录
            List<AmsChngBrwgVo> recentRecords = amsChngBrwgReadService.queryList(queryDto);

            // 按创建时间排序，取最近的记录
            int limitCount = dto.getLimitCount() != null ? dto.getLimitCount() : 10;
            return recentRecords.stream()
                    .filter(r -> r.getCreateTime() != null)
                    .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                    .limit(limitCount)
                    .map(this::convertToRecentActivity)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("❌ 获取最近活动列表失败", e);
            return new ArrayList<>();
        }
    }

    private AmsAssetBorrowingDashboardVo.RecentActivity convertToRecentActivity(AmsChngBrwgVo record) {
        String type = getActivityType(record.getProsstas());
        String description = getActivityDescription(record.getProsstas(), record.getAssetName());

        return AmsAssetBorrowingDashboardVo.RecentActivity.builder()
                .id(record.getId())
                .type(type)
                .description(description)
                .assetName(record.getAssetName())
                .assetCode(record.getFaCode())
                .operator(record.getCrterName())
                .operateTime(parseDateTime(record.getCreateTime()))
                .status(record.getProsstas())
                .build();
    }

    private String getActivityType(String status) {
        switch (status) {
            case "1":
                return "apply";
            case "2":
            case "3":
                return "audit";
            case "4":
                return "borrow";
            case "5":
                return "return";
            default:
                return "unknown";
        }
    }

    private String getActivityDescription(String status, String assetName) {
        switch (status) {
            case "1":
                return "提交了资产借用申请";
            case "2":
                return "借用申请已通过审核";
            case "3":
                return "借用申请被拒绝";
            case "4":
                return "开始借用资产";
            case "5":
                return "完成资产归还";
            default:
                return "未知操作";
        }
    }

    @Override
    public List<AmsAssetBorrowingDashboardVo.ExpiryReminder> getExpiryReminders(AmsAssetBorrowingStatsDto dto) {
        log.info("⏰ 获取过期提醒列表: {}", dto);

        try {
            // 查询借用中的记录
            String currentUserId = UserContext.getEmpCode();
            if (currentUserId == null) {
                log.warn("⚠️ 无法获取当前用户信息");
                return new ArrayList<>();
            }

            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setProsstas("4"); // 借用中状态
            queryDto.setLoanee(currentUserId);
            // 设置分页参数
            queryDto.setPageNum(1);
            queryDto.setPageSize(100); // 过期提醒需要查询更多记录
            List<AmsChngBrwgVo> activeBorrowings = amsChngBrwgReadService.queryList(queryDto);

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime threeDaysLater = now.plusDays(dto.getExpiringDays());

            int limitCount = dto.getLimitCount() != null ? dto.getLimitCount() : 10;
            return activeBorrowings.stream()
                    .filter(r -> r.getExpRtnTime() != null)
                    .filter(r -> {
                        LocalDateTime expTime = parseDateTime(r.getExpRtnTime());
                        return expTime != null && expTime.isBefore(threeDaysLater); // 包括已逾期和即将到期
                    })
                    .map(r -> convertToExpiryReminder(r, now))
                    .sorted((a, b) -> a.getRemainingDays().compareTo(b.getRemainingDays())) // 按剩余天数排序
                    .limit(limitCount)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("❌ 获取过期提醒列表失败", e);
            return new ArrayList<>();
        }
    }

    private AmsAssetBorrowingDashboardVo.ExpiryReminder convertToExpiryReminder(AmsChngBrwgVo record,
                                                                                LocalDateTime now) {
        LocalDateTime expTime = parseDateTime(record.getExpRtnTime());
        LocalDateTime loaneeTime = parseDateTime(record.getLoaneeTime());
        int remainingDays = 0;
        if (expTime != null) {
            long daysDiff = java.time.Duration.between(now, expTime).toDays();
            remainingDays = (int) daysDiff;
        }

        String reminderType = remainingDays < 0 ? "overdue" : "expiring";
        String urgency = getUrgency(remainingDays);

        return AmsAssetBorrowingDashboardVo.ExpiryReminder.builder()
                .borrowingId(record.getId())
                .assetName(record.getAssetName())
                .assetCode(record.getFaCode())
                .borrower(record.getLoanee())
                .borrowDept(record.getLoaneeDept())
                .expectedReturnTime(expTime)
                .loaneeTime(loaneeTime)

                .remainingDays(remainingDays)
                .reminderType(reminderType)
                .urgency(urgency)
                .build();
    }

    private String getUrgency(int remainingDays) {
        if (remainingDays < 0) {
            return "high"; // 已逾期
        } else if (remainingDays <= 1) {
            return "medium"; // 1天内到期
        } else {
            return "low"; // 其他
        }
    }

    /**
     * 解析时间字符串为LocalDateTime
     * 支持多种时间格式
     */
    private LocalDateTime parseDateTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析常见的时间格式
            if (timeStr.length() == 19) {
                // yyyy-MM-dd HH:mm:ss
                return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else if (timeStr.length() == 10) {
                // yyyy-MM-dd
                return LocalDateTime.parse(timeStr + " 00:00:00",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 其他格式，尝试ISO格式
                return LocalDateTime.parse(timeStr);
            }
        } catch (Exception e) {
            log.warn("⚠️ 时间解析失败: {}", timeStr, e);
            return null;
        }
    }

    @Override
    public List<AmsAssetBorrowingDashboardVo.PopularAsset> getPopularAssets(AmsAssetBorrowingStatsDto dto) {
        // 实现将在下一部分添加
        return new ArrayList<>();
    }

    @Override
    public List<AmsAssetBorrowingDashboardVo.DeptBorrowingRank> getDeptBorrowingRanks(
            AmsAssetBorrowingStatsDto dto) {
        // 实现将在下一部分添加
        return new ArrayList<>();
    }

    @Override
    public AmsAssetBorrowingDashboardVo.TrendAnalysis getTrendAnalysis(AmsAssetBorrowingStatsDto dto) {
        // 实现将在下一部分添加
        return AmsAssetBorrowingDashboardVo.TrendAnalysis.builder().build();
    }

    @Override
    public Integer getAvailableAssetsCount(AmsAssetBorrowingStatsDto dto) {
        return 150; // 模拟数据
    }

    @Override
    public AmsAssetBorrowingDashboardVo.MyApplicationStats getUserBorrowingHistory(AmsAssetBorrowingStatsDto dto) {
        return getMyApplicationStats(dto);
    }

    @Override
    public List<AmsAssetBorrowingDashboardVo.ExpiryReminder> getRealTimeNotifications(
            AmsAssetBorrowingStatsDto dto) {
        return getExpiryReminders(dto);
    }

    @Override
    public List<AmsAssetBorrowingDashboardVo.PopularAsset> getAssetUtilizationStats(AmsAssetBorrowingStatsDto dto) {
        return getPopularAssets(dto);
    }

    @Override
    public AmsAssetBorrowingDashboardVo.PendingAuditStats getAuditEfficiencyStats(AmsAssetBorrowingStatsDto dto) {
        return getPendingAuditStats(dto);
    }
}
