<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrItemDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.erp.modules.vcrGen.vo.ErpVcrItemDetailVo" id="vcrItemDetailMap">
        <result property="id" column="id"/>
        <result property="flh" column="flh"/>
        <result property="idpzh" column="idpzh"/>
        <result property="idpznr" column="idpznr"/>
        <result property="jdbz" column="jdbz"/>
        <result property="je" column="je"/>
        <result property="pzh" column="pzh"/>
        <result property="pzid" column="pzid"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpVcrItemDetailVo">
        select
            id as id,
            flh as flh,
            idpzh as idpzh,
            idpznr as idpznr,
            jdbz as jdbz,
            je as je,
            pzh as pzh,
            pzid as pzid
        from erp_vcr_item_detail
    </select>
</mapper>
