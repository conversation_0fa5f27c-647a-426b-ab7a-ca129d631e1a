package com.jp.med.ams.modules.changes.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.changes.dto.AmsTransferDto;
import com.jp.med.ams.modules.changes.vo.AmsTransferVo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;

import java.util.List;

/**
 * 资产转移
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
public interface AmsTransferReadService extends IService<AmsTransferDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsTransferVo> queryList(AmsTransferDto dto);

    List<AmsPropertyVo> queryPropertyDetail(AmsTransferDto dto);


    Integer queryAuditCount(AmsTransferDto dto);
}

