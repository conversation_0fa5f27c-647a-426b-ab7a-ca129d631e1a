package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.property.dto.AmsSourceAmountSplitDto;
import com.jp.med.ams.modules.property.mapper.read.AmsSourceAmountSplitReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.ams.modules.property.vo.AmsSourceAmountSplitVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 混合资金来源处理器
 * 负责处理混合资金来源项目的拆分
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyMixSourceProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Autowired
    private AmsSourceAmountSplitReadMapper amsSourceAmountSplitReadMapper;

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理混合资金来源拆分");

        // 查询混合资金来源拆分数据
        AmsSourceAmountSplitDto amsSourceAmountSplitQueryDto = new AmsSourceAmountSplitDto();
        List<AmsSourceAmountSplitVo> amsSourceAmountSplitVos = amsSourceAmountSplitReadMapper
                .queryList(amsSourceAmountSplitQueryDto);
        context.setAmsSourceAmountSplitVos(amsSourceAmountSplitVos);

        // 处理普通资产的混合资金来源拆分
        processMixSource(amsSourceAmountSplitVos, context.getNormalAssets());

        // 处理房屋维修资产的混合资金来源拆分
        processMixSource(amsSourceAmountSplitVos, context.getHouseRepairAssets());

        log.debug("混合资金来源拆分处理完成，共处理 {} 条拆分记录", amsSourceAmountSplitVos.size());
    }

    /**
     * 处理混合资金来源拆分
     * 
     * @param amsSourceAmountSplitVos 拆分配置列表
     * @param assets                  资产列表
     */
    private void processMixSource(List<AmsSourceAmountSplitVo> amsSourceAmountSplitVos,
            List<AmsPropertyVo> assets) {
        amsSourceAmountSplitVos.forEach(splitRecord -> {
            assets.stream()
                    .filter(asset -> asset.getFaCode().equals(splitRecord.getFaCode()))
                    .findFirst()
                    .ifPresent(asset -> {
                        // 移除原始资产
                        assets.remove(asset);

                        BigDecimal totalAmount = BigDecimal.ZERO;
                        if (splitRecord.getSplitAmount1() != null) {
                            totalAmount = totalAmount.add(splitRecord.getSplitAmount1());
                        }
                        if (splitRecord.getSplitAmount2() != null) {
                            totalAmount = totalAmount.add(splitRecord.getSplitAmount2());
                        }
                        if (splitRecord.getSplitAmount3() != null) {
                            totalAmount = totalAmount.add(splitRecord.getSplitAmount3());
                        }

                        // 检查并添加拆分1
                        if (splitRecord.getSplitAmount1() != null && splitRecord.getSplitType1() != null) {
                            AmsPropertyVo newAsset1 = createSplitAsset(asset, splitRecord.getSplitType1(),
                                    splitRecord.getSplitAmount1(), totalAmount);
                            assets.add(newAsset1);
                        }

                        // 检查并添加拆分2
                        if (splitRecord.getSplitAmount2() != null && splitRecord.getSplitType2() != null) {
                            AmsPropertyVo newAsset2 = createSplitAsset(asset, splitRecord.getSplitType2(),
                                    splitRecord.getSplitAmount2(), totalAmount);
                            assets.add(newAsset2);
                        }

                        // 检查并添加拆分3
                        if (splitRecord.getSplitAmount3() != null && splitRecord.getSplitType3() != null) {
                            AmsPropertyVo newAsset3 = createSplitAsset(asset, splitRecord.getSplitType3(),
                                    splitRecord.getSplitAmount3(), totalAmount);
                            assets.add(newAsset3);
                        }
                    });
        });
    }

    /**
     * 创建拆分后的资产
     * 
     * @param originalAsset 原始资产
     * @param splitType     拆分类型
     * @param splitAmount   拆分金额
     * @param totalAmount   总金额
     * @return 拆分后的资产
     */
    private AmsPropertyVo createSplitAsset(AmsPropertyVo originalAsset, String splitType,
            BigDecimal splitAmount, BigDecimal totalAmount) {
        AmsPropertyVo newAsset = new AmsPropertyVo();
        BeanUtils.copyProperties(originalAsset, newAsset);
        newAsset.setSource(splitType);
        newAsset.setAssetNav(splitAmount);

        // 按比例设置折旧
        BigDecimal ratio = splitAmount.divide(totalAmount, 6, RoundingMode.HALF_UP);
        if (originalAsset.getDeprMon() != null) {
            newAsset.setDeprMon(originalAsset.getDeprMon().multiply(ratio));
        }
        if (originalAsset.getDep() != null) {
            newAsset.setDep(originalAsset.getDep().multiply(ratio));
        }

        return newAsset;
    }
}
