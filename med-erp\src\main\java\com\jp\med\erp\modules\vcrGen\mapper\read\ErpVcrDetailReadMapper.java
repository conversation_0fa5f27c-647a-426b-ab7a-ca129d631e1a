package com.jp.med.erp.modules.vcrGen.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.dto.erp.ErpPropertyDto;
import com.jp.med.common.vo.EcsReimDeprTaskVo;
import com.jp.med.common.vo.ErpPropertyVo;
import com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrDetailDto;
import com.jp.med.erp.modules.vcrGen.entity.*;
import com.jp.med.erp.modules.vcrGen.vo.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 凭证信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Mapper
public interface ErpVcrDetailReadMapper extends BaseMapper<ErpVcrDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrDetailVo> queryList(ErpVcrDetailDto dto);

    /**
     * 查询凭证生成列表
     * @param dto
     * @return
     */
    List<ErpReimDetailVo>  queryVcrGenList(ErpVcrDetailDto dto);

    /**
     * 查询凭证生成列表
     * @param dto
     * @return
     */
    List<ErpReimDetailVo> queryPurcVcrGenList(ErpVcrDetailDto dto);

    /**
     * 查询报销明细信息
     * @param ids
     * @return
     */
    List<ErpReimDetailVo> queryReimDetailList(List<Integer> ids);

    /**
     * 查询会计科目
     * @param dto
     * @return
     */
    ErpActigCfg queryActigCfg(ErpActigCfg dto);

    /**
     * 查询报销项目辅助项信息
     * @param dto
     * @return
     */
    List<CertificateDetail> queryReimAsstList(Certificate dto);

    /**
     * 查询药品报销项目辅助项信息
     * @param dto
     * @return
     */
    List<CertificateDetail> queryDrugAsstList(Certificate dto);

    /**
     * 查询报销项目辅助项信息
     * @param dto
     * @return
     */
    List<ErpReimAsstVo> queryReimAsstVoList(ErpReimAsstDto dto);

    /**
     * 查询项目信息
     * @param ids
     * @return
     */
    List<ErpReimItemDetail> queryItemDetail(List<Integer> ids);

    /**
     * 查询补助项目信息
     * @param ids
     * @return
     */
    List<ErpReimSubsItemDetail> querySubItemDetail(List<Integer> ids);

    /**
     * 查询报销人员明细
     * @param ids
     * @return
     */
    List<ErpReimPsnDetail> queryPsnDetail(List<Integer> ids);

    /**
     * 查询凭证申请信息
     * @param dto
     * @return
     */
    List<Certificate> queryVcrApplyMsg(ErpVcrDetailDto dto);

    /**
     * 查询凭证对应辅助项目
     * @param dto
     * @return
     */
    List<ErpReimAsstVo> queryVcrAsst(ErpVcrDetailDto dto);

    /**
     * 通过vpzh查询辅助项目
     * @param dto
     * @return
     */
    List<ErpReimAsstVo> queryVcrAsstByvpzh(ErpVcrDetailDto dto);

    /**
     * 查询待生成凭证对应辅助项目
     * @param dto
     * @return
     */
    List<ErpReimAsstVo> queryAsst(Certificate dto);

    /**
     * 查询预览凭证辅助项目
     * @param dto
     * @return
     */
    List<ErpReimAsstVo> queryAsstByPreviewVcr(Certificate dto);

    /**
     * 查询科室分摊费用信息
     * @param dto
     * @return
     */
    List<ErpReimShareEntity> queryShareDetails(Certificate dto);

    /**
     * 通过附件编码查询
     * @param ids
     * @return
     */
    List<FileRecordEntity> queryFileRecord(List<Integer> ids);

    /**
     * 通过附件编码查询
     * @param attCodes
     * @return
     */
    List<FileRecordEntity> queryFileRecordByCode(List<String> attCodes);

    /**
     * 查询申请信息 by Ids
     * @param dto
     * @return
     */
    List<ErpReimTravelApprVo> queryApprInfo(ErpVcrDetailDto dto);

    /**
     * 查询报销信息 by Ids
     * @param dto
     * @return
     */
    List<ErpReimDetailVo> queryReimInfo(ErpVcrDetailDto dto);

    /**
     * 查询分摊类型-往来单位金额信息
     * @param dto
     * @return
     */
    List<ErpReimRelCoDetail> queryReimRelCoDetails(Certificate dto);

    /**
     * 查询药品待生成凭证
     * @param dto
     * @return
     */
    List<ErpReimDetailVo> drugToVcrList(ErpVcrDetailDto dto);

    /**
     * 查询药品报销
     * @param ids
     * @return
     */
    List<ErpDrugReimDetailVo> queryDrugReimDetailVo(List<Integer> ids);

    /**
     * 查询采购报销
     * @param ids
     * @return
     */
    List<ErpReimDetailVo> queryPurcReimDetailVo(List<Integer> ids);

    /**
     * 查询药品报销
     * @param idpzh
     * @return
     */
    List<ErpDrugReimDetailVo> queryDrugReimDetailVoByIdPzh(String idpzh);

    /**
     * 查询报销 by idpzh
     * @param idpzh
     * @return
     */
    List<ErpReimDetailVo> queryReimDetailVoByIdPzh(String idpzh);

    /**
     * 通过药品付款批次id查询付款文件
     * @param ids
     * @return
     */
    List<ErpReimPayReceiptVo> queryPayReceiptVoByDrugPayDetailIds(List<Integer> ids);

    /**
     * 查询药品入库单信息
     * @param ids
     * @return
     */
    List<ErpStoinVo> queryDrugStoinVoByReimIds(List<Integer> ids);

    List<ErpReimSalaryTaskVo> salaryToVcrList(ErpVcrDetailDto dto);

    List<EcsReimDeprTaskVo> deprToVcrList(ErpVcrDetailDto dto);

    /**
     * 查询折旧数据
     * @param dto
     */
    List<ErpPropertyVo> queryPropertyDeprList(ErpPropertyDto dto);

    /**
     * 通过salaryId 任务id查询费用报销信息
     * @param dto
     * @return
     */
    List<ErpReimDetailVo> queryReimDetailWithSalaryId(Certificate dto);

    List<ErpVcrSalaryConfigVo> queryToConfigSalary(ErpVcrDetailDto dto);

    /**
     * 查询药品付款单中间表
     * @param dto
     * @return
     */
    List<EcsDrugPayDetailVo> queryDrugPayDetail(EcsDrugPayDetailDto dto);

    /**
     * 查询payRcptIds所对应的所有报销
     * @param payRcptIds
     * @return
     */
    List<ErpReimDetailVo> queryReimDetailByPayRcptId(List<Integer> payRcptIds);

    /**
     * 通过idpzh查找凭证预览信息
     * @param dto
     * @return
     */
    List<ErpVcrPreviewVo> queryVcrPreview(ErpVcrDetailDto dto);
}
