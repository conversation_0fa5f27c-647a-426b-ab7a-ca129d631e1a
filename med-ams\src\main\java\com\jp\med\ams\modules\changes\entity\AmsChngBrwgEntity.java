package com.jp.med.ams.modules.changes.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产借用信息表
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 16:01:14
 */
@Data
@TableName("ams_chng_brwg")
public class AmsChngBrwgEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 医疗器械分类目录ID */
	@TableField("ccm_id")
	private Integer ccmId;

	/** 单据号 */
	@TableField("doc_num")
	private String docNum;

	/** 办理状态 */
	@TableField("prosstas")
	private String prosstas;

	/** 业务状态 */
	@TableField("busstas")
	private String busstas;

	/** 借用人/科室 */
	@TableField("loanee")
	private String loanee;

	/** 借用时间 */
	@TableField("loanee_time")
	private String loaneeTime;

	/** 预计归还时间 */
	@TableField("exp_rtn_time")
	private String expRtnTime;

	/** 实际归还时间 */
	@TableField("act_rtn_time")
	private String actRtnTime;

	/** 备注 */
	@TableField("remarks")
	private String remarks;

	/** 归还备注 */
	@TableField("back_remarks")
	private String backRemarks;

	/** 创建人 */
	@TableField("crter")
	private Integer crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 医疗机构ID */
	@TableField("hospital_id")
	private String hospitalId;

}
