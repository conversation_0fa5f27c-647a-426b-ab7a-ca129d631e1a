package com.jp.med.ams.modules.config.service.write.impl;
import com.jp.med.ams.modules.config.mapper.read.AmsBasicCfgReadMapper;
import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.config.mapper.write.AmsBasicCfgWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.service.write.AmsBasicCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资产配置表
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:47:25
 */
@Service
@Transactional(readOnly = false)
public class AmsBasicCfgWriteServiceImpl extends ServiceImpl<AmsBasicCfgWriteMapper, AmsBasicCfgDto> implements AmsBasicCfgWriteService {

}
