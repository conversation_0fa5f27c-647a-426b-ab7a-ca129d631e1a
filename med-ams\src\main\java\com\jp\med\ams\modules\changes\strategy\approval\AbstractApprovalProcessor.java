package com.jp.med.ams.modules.changes.strategy.approval;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 抽象审批处理器
 * 使用模板方法模式定义审批处理流程
 */
public abstract class AbstractApprovalProcessor implements ApprovalProcessor {
    
    @Override
    public final ApprovalResult process(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto, ApprovalContext context) {
        try {
            // 模板方法：定义审批处理流程
            
            // 1. 验证审批条件
            validateApproval(changeRecord, approvalDto);
            
            // 2. 创建资产更新信息
            List<AmsPropertyDto> propertyUpdates = new ArrayList<>();
            AmsPropertyDto propertyUpdate = createPropertyUpdate(changeRecord, approvalDto, context);
            if (propertyUpdate != null) {
                propertyUpdates.add(propertyUpdate);
            }
            
            return new ApprovalResult(propertyUpdates);
            
        } catch (Exception e) {
            return new ApprovalResult(false, "审批处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证审批条件（子类可重写）
     */
    protected void validateApproval(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto) {
        // 默认验证逻辑
        if (changeRecord == null) {
            throw new IllegalArgumentException("变更记录不能为空");
        }
        if (approvalDto == null) {
            throw new IllegalArgumentException("审批信息不能为空");
        }
    }
    
    /**
     * 创建资产更新信息（子类必须实现）
     */
    protected abstract AmsPropertyDto createPropertyUpdate(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto, ApprovalContext context);
    
    /**
     * 判断是否为通过审批
     */
    protected boolean isApprovalPassed(AmsChgRcdDto approvalDto) {
        return approvalDto.getPass() != null && approvalDto.getPass();
    }
    
    /**
     * 判断是否为取消审批
     */
    protected boolean isApprovalCancelled(AmsChgRcdDto approvalDto) {
        return approvalDto.getPass() != null && !approvalDto.getPass();
    }
}
