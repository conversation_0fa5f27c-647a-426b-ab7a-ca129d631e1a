package com.jp.med.ams.modules.changes.strategy.approval.impl;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.strategy.approval.AbstractApprovalProcessor;
import com.jp.med.ams.modules.changes.strategy.approval.ApprovalContext;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 报废审批处理器
 * 处理报废类型的审批业务逻辑
 * 优先级：1（最高）
 */
@Component
@Order(1)
public class ScrapApprovalProcessor extends AbstractApprovalProcessor {
    
    @Override
    public boolean supports(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto) {
        return "5".equals(changeRecord.getRedcWay());
    }
    
    @Override
    protected AmsPropertyDto createPropertyUpdate(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto, ApprovalContext context) {
        AmsPropertyDto propertyUpdate = new AmsPropertyDto();
        propertyUpdate.setFaCode(changeRecord.getFaCode());
        
        if (isApprovalPassed(approvalDto)) {
            // 审批通过：设置为已注销，减少方式为报废
            propertyUpdate.setIsCanc("1");
            propertyUpdate.setRedcWay("5");
        } else if (isApprovalCancelled(approvalDto)) {
            // 取消审批：恢复为未注销状态，清除减少方式
            propertyUpdate.setIsCanc("0");
            propertyUpdate.setRedcWay(null);
        }
        
        return propertyUpdate;
    }
}
