package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 资产分组处理器
 * 负责将资产按科室、资产类型、资金来源进行分组
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyGroupingProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始进行资产分组");

        // 初始化分组容器
        Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedNormalAssets = new HashMap<>();
        Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedHouseRepairAssets = new HashMap<>();

        // 分组普通资产
        for (AmsPropertyVo asset : context.getNormalAssets()) {
            groupAsset(asset, groupedNormalAssets, context.isUseNewType());
        }

        // 分组房屋维修资产
        for (AmsPropertyVo asset : context.getHouseRepairAssets()) {
            groupAsset(asset, groupedHouseRepairAssets, context.isUseNewType());
        }

        // 设置分组结果到上下文
        context.setGroupedNormalAssets(groupedNormalAssets);
        context.setGroupedHouseRepairAssets(groupedHouseRepairAssets);

        // 初始化结果列表和已使用资产代码集合
        context.setResult(new ArrayList<AmsPropertyDepr2Vo>());
        context.setUsedFacodeSet(new HashSet<String>());

        log.debug("资产分组完成，普通资产分组数: {}, 房屋维修资产分组数: {}",
                groupedNormalAssets.size(), groupedHouseRepairAssets.size());
    }

    /**
     * 资产分组辅助方法
     * 
     * @param asset         资产对象
     * @param groupedAssets 分组容器
     * @param useNewType    是否使用新资产类型
     */
    private void groupAsset(AmsPropertyVo asset,
            Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedAssets,
            Boolean useNewType) {
        if (asset.getDeptUse() == null && asset.getSource() == null) {
            log.info("资产信息不完整，无法分组：{}", asset);
            return;
        }
        if (useNewType && asset.getAssetTypeN() == null) {
            log.info("资产信息不完整，无法分组：{}", asset);
            return;
        } else if (!useNewType && asset.getAssetType() == null) {
            log.info("资产信息不完整，无法分组：{}", asset);
            return;
        }

        groupedAssets
                // 按科室分组
                .computeIfAbsent(asset.getDeptUse(), k -> new HashMap<>())
                // 按资产类型分组
                .computeIfAbsent(useNewType ? asset.getAssetTypeN() : asset.getAssetType(), k -> new HashMap<>())
                // 按资金来源分组
                .computeIfAbsent(asset.getSource(), k -> new ArrayList<>())
                .add(asset);
    }
}
