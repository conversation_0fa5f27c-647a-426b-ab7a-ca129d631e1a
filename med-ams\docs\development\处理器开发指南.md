# 处理器开发指南

## 📋 概述

本指南详细说明如何在 AmsPropertyDeprReadServiceImpl 责任链架构中开发新的处理器，包括设计原则、开发步骤、测试方法和最佳实践。

## 🎯 设计原则

### 1. 单一职责原则（SRP）
每个处理器只负责一个特定的业务功能，避免功能耦合。

```java
// ✅ 好的设计 - 只负责资金来源配置
public class AmsPropertySourceConfigProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        // 只处理资金来源配置相关逻辑
        loadSourceConfiguration(context);
        buildSourceCodeMapping(context);
        calculateLastDayOfMonth(context);
    }
}

// ❌ 不好的设计 - 职责过多
public class AmsPropertyMixedProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        // 处理资金来源配置
        loadSourceConfiguration(context);
        // 处理资产查询
        queryAssets(context);
        // 处理数据分组
        groupAssets(context);
        // 职责过多，违反单一职责原则
    }
}
```

### 2. 开闭原则（OCP）
处理器应该对扩展开放，对修改关闭。

```java
// ✅ 好的设计 - 通过继承扩展功能
public abstract class BaseAssetQueryProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    
    protected abstract List<AmsPropertyVo> queryAssets(AmsPropertyDeprProcessContext context);
    
    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        List<AmsPropertyVo> assets = queryAssets(context);
        context.setAssets(assets);
        log.debug("查询到 {} 条资产记录", assets.size());
    }
}

public class NormalAssetQueryProcessor extends BaseAssetQueryProcessor {
    @Override
    protected List<AmsPropertyVo> queryAssets(AmsPropertyDeprProcessContext context) {
        // 实现普通资产查询逻辑
        return queryNormalAssets(context);
    }
}
```

### 3. 依赖倒置原则（DIP）
依赖抽象而不是具体实现。

```java
// ✅ 好的设计 - 依赖抽象接口
@Component
public class AmsPropertyDataProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    
    @Autowired
    private AmsPropertyReadMapper propertyReadMapper; // 依赖接口
    
    @Autowired
    private AmsBasicCfgReadMapper basicCfgReadMapper; // 依赖接口
}
```

## 🛠️ 开发步骤

### 步骤1：分析业务需求

在开发新处理器之前，需要明确：

1. **处理器职责**：这个处理器要解决什么问题？
2. **输入数据**：需要从上下文中获取哪些数据？
3. **输出数据**：需要向上下文中设置哪些数据？
4. **依赖关系**：需要调用哪些外部服务或数据访问层？
5. **处理顺序**：在责任链中的位置？

### 步骤2：创建处理器类

```java
package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 新功能处理器
 * 负责处理XXX业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Component
public class NewFeatureProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    
    @Autowired
    private SomeService someService;
    
    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理新功能");
        
        // 1. 参数验证
        if (!validateInput(context)) {
            log.warn("输入参数验证失败，跳过处理");
            return;
        }
        
        // 2. 业务逻辑处理
        processBusinessLogic(context);
        
        // 3. 结果设置
        setProcessResult(context);
        
        log.debug("新功能处理完成");
    }
    
    /**
     * 验证输入参数
     */
    private boolean validateInput(AmsPropertyDeprProcessContext context) {
        // 实现验证逻辑
        return context.getQueryDto() != null;
    }
    
    /**
     * 处理业务逻辑
     */
    private void processBusinessLogic(AmsPropertyDeprProcessContext context) {
        // 实现具体业务逻辑
    }
    
    /**
     * 设置处理结果
     */
    private void setProcessResult(AmsPropertyDeprProcessContext context) {
        // 将处理结果设置到上下文中
    }
}
```

### 步骤3：更新处理器工厂

```java
@Component
public class AmsPropertyDeprProcessorFactory {
    
    // 添加新处理器的依赖注入
    @Autowired
    private NewFeatureProcessor newFeatureProcessor;
    
    public AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> createSummaryProcessorChain() {
        log.debug("创建 queryDeprSummary 处理器链");
        
        // 在适当位置添加新处理器
        sourceConfigProcessor
                .setNext(houseRepairQueryProcessor)
                .setNext(normalAssetsQueryProcessor)
                .setNext(newFeatureProcessor)  // 新增处理器
                .setNext(mixSourceProcessor)
                // ... 其他处理器
        
        return sourceConfigProcessor;
    }
}
```

### 步骤4：编写单元测试

```java
@ExtendWith(MockitoExtension.class)
class NewFeatureProcessorTest {
    
    @Mock
    private SomeService someService;
    
    @InjectMocks
    private NewFeatureProcessor processor;
    
    @Test
    void testDoProcess_Success() {
        // Given
        AmsPropertyDeprProcessContext context = createTestContext();
        when(someService.someMethod(any())).thenReturn(createMockResult());
        
        // When
        processor.doProcess(context);
        
        // Then
        assertThat(context.getSomeResult()).isNotNull();
        verify(someService).someMethod(any());
    }
    
    @Test
    void testDoProcess_InvalidInput() {
        // Given
        AmsPropertyDeprProcessContext context = new AmsPropertyDeprProcessContext(null);
        
        // When
        processor.doProcess(context);
        
        // Then
        verify(someService, never()).someMethod(any());
    }
    
    private AmsPropertyDeprProcessContext createTestContext() {
        AmsPropertyDeprDto dto = new AmsPropertyDeprDto();
        dto.setYm("202501");
        return new AmsPropertyDeprProcessContext(dto);
    }
}
```

## 📊 上下文数据管理

### 读取上下文数据

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    // 获取查询参数
    AmsPropertyDeprDto queryDto = context.getQueryDto();
    String ym = context.getYm();
    boolean useNewType = context.isUseNewType();
    
    // 获取中间处理结果
    List<AmsPropertyVo> normalAssets = context.getNormalAssets();
    Map<String, String> sourceCodeMap = context.getSourceCodeMap();
    
    // 获取配置数据
    List<AmsBasicCfgVo> sourceList = context.getSourceList();
}
```

### 设置上下文数据

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    // 设置查询结果
    List<AmsPropertyVo> assets = queryAssets();
    context.setNormalAssets(assets);
    
    // 设置配置数据
    Map<String, String> mapping = buildMapping();
    context.setSourceCodeMap(mapping);
    
    // 设置处理结果
    List<AmsPropertyDepr2Vo> result = processData();
    context.setResult(result);
}
```

## 🔍 错误处理和日志

### 异常处理策略

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    try {
        // 业务逻辑处理
        processBusinessLogic(context);
        
    } catch (DataAccessException e) {
        log.error("数据访问异常", e);
        throw new AppException("数据查询失败", e);
        
    } catch (BusinessException e) {
        log.error("业务逻辑异常", e);
        throw e;
        
    } catch (Exception e) {
        log.error("未知异常", e);
        throw new AppException("处理器执行失败", e);
    }
}
```

### 日志记录规范

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    log.debug("开始处理XXX，参数: {}", context.getQueryDto().getYm());
    
    // 关键步骤日志
    log.debug("查询到 {} 条资产记录", assets.size());
    
    // 性能日志
    long startTime = System.currentTimeMillis();
    processData();
    long endTime = System.currentTimeMillis();
    log.debug("数据处理完成，耗时: {}ms", endTime - startTime);
    
    // 结果日志
    log.debug("处理完成，生成 {} 条结果记录", result.size());
}
```

## 🧪 测试最佳实践

### 1. 测试覆盖率要求

- **分支覆盖率**：≥ 80%
- **行覆盖率**：≥ 90%
- **方法覆盖率**：100%

### 2. 测试用例设计

```java
@ExtendWith(MockitoExtension.class)
class ProcessorTest {
    
    @Test
    void testNormalCase() {
        // 测试正常情况
    }
    
    @Test
    void testEmptyInput() {
        // 测试空输入
    }
    
    @Test
    void testInvalidInput() {
        // 测试无效输入
    }
    
    @Test
    void testExceptionHandling() {
        // 测试异常处理
    }
    
    @Test
    void testBoundaryConditions() {
        // 测试边界条件
    }
}
```

### 3. Mock 对象使用

```java
@Mock
private AmsPropertyReadMapper propertyReadMapper;

@Test
void testDataQuery() {
    // Given
    List<AmsPropertyVo> mockData = createMockData();
    when(propertyReadMapper.queryList(any())).thenReturn(mockData);
    
    // When
    processor.doProcess(context);
    
    // Then
    verify(propertyReadMapper).queryList(argThat(dto -> 
        dto.getType().equals("1") && dto.getIsCanc().equals("0")));
}
```

## 📋 代码审查清单

### 开发完成后检查

- [ ] 处理器继承了 `AbstractAmsPropertyDeprProcessor`
- [ ] 实现了 `doProcess` 方法
- [ ] 添加了 `@Component` 注解
- [ ] 添加了 `@Slf4j` 注解
- [ ] 包含适当的 JavaDoc 注释
- [ ] 添加了必要的日志记录
- [ ] 处理了可能的异常情况
- [ ] 编写了完整的单元测试
- [ ] 更新了处理器工厂
- [ ] 更新了相关文档

### 代码质量检查

- [ ] 方法长度不超过50行
- [ ] 圈复杂度不超过10
- [ ] 没有重复代码
- [ ] 变量命名清晰
- [ ] 没有魔法数字
- [ ] 遵循编码规范

## 🚀 性能优化建议

### 1. 数据库查询优化

```java
// ✅ 批量查询
List<String> faCodes = assets.stream()
        .map(AmsPropertyVo::getFaCode)
        .collect(Collectors.toList());
List<SomeData> batchResult = mapper.queryBatch(faCodes);

// ❌ 循环查询
for (AmsPropertyVo asset : assets) {
    SomeData data = mapper.queryByFaCode(asset.getFaCode());
}
```

### 2. 集合操作优化

```java
// ✅ 使用 Stream API 进行并行处理
List<ProcessedData> result = largeDataList.parallelStream()
        .filter(this::shouldProcess)
        .map(this::processData)
        .collect(Collectors.toList());

// ✅ 预分配集合容量
List<String> result = new ArrayList<>(expectedSize);
Map<String, Object> cache = new HashMap<>(expectedSize);
```

### 3. 内存使用优化

```java
// ✅ 及时清理不需要的数据
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    List<TempData> tempData = processTemporaryData();
    
    // 使用完后立即清理
    processResult(tempData);
    tempData.clear();
    tempData = null;
}
```

## 📚 参考资源

- [责任链模式详解](../refactor/责任链模式技术实现详解.md)
- [单元测试最佳实践](../testing/unit-testing-best-practices.md)
- [代码规范指南](../standards/coding-standards.md)
- [性能优化指南](../performance/performance-optimization.md)

---

**文档版本**：v1.0  
**最后更新**：2025-01-20  
**维护人员**：开发团队
