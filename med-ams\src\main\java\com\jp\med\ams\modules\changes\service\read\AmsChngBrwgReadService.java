package com.jp.med.ams.modules.changes.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;

import java.util.List;

/**
 * 资产借用信息表
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 16:01:14
 */
public interface AmsChngBrwgReadService extends IService<AmsChngBrwgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsChngBrwgVo> queryList(AmsChngBrwgDto dto);

    /**
     * 查询汇总情况
     * @param dto
     * @return
     */
    AmsChngBrwgVo queryCount(AmsChngBrwgDto dto);

    Integer queryWarnNum(AmsChngBrwgDto dto);
}

