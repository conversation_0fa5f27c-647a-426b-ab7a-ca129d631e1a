package com.jp.med.ams.modules.it.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class ModifyFileDto {

    /** 标题 */
    private String title;

    /** 表名 */
    private String tableName;

    /** 职工id */
    private Integer empId;

    /** 表id */
    private Long id;

    /** 名称 */
    private String fieldName;

    /** 真实的字段（驼峰） */
    private String realFileName;

    /** 文件字段名称 */
    private String fileFieldName;

    /** 真实的文件字段名称 */
    private String realFileFieldName;

    /** 文件路径 */
    private String filePath;

    /** 文件名称 */
    private String fileName;

    /** 原值 */
    private String originValue;

    /** 显示的原值 */
    private String displayOriginValue;

    /** 字段显示名称 */
    private String fieldDisplayName;

    /** 修改文件 */
    private List<MultipartFile> files;

    /** 类型 1修改 2删除 3新增 */
    private String type;
}
