package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItChkDetailDto;
import com.jp.med.ams.modules.it.vo.AmsItChkDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 耗材申请审核详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItChkDetailReadMapper extends BaseMapper<AmsItChkDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItChkDetailVo> queryList(AmsItChkDetailDto dto);
}
