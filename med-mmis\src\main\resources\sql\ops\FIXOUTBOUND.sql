-- =================================================================================================
-- 出库数据修复脚本：修复组织字段映射关系
-- 功能：将out_target_org_id的数据移到out_target_org，然后重新查询正确的组织ID
-- 作者：数据修复
-- 创建时间：2025年7月16日
-- =================================================================================================

-- 开始事务处理，确保数据一致性
BEGIN;

-- =================================================================================================
-- 第一步：数据备份和检查 🔍
-- =================================================================================================

-- 检查当前数据状态
SELECT
    '修复前数据检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) as has_org_id,
    COUNT(CASE WHEN out_org IS NOT NULL THEN 1 END) as has_org_name,
    COUNT(DISTINCT out_target_org_id) as unique_org_ids,
    COUNT(DISTINCT out_org) as unique_org_names
FROM mmis_temp_xinxike_outbound_six;

-- 显示当前的组织数据样例
SELECT
    '修复前数据样例' as sample_type,
    out_target_org_id,
    out_org,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six
WHERE out_target_org_id IS NOT NULL OR out_org IS NOT NULL
GROUP BY out_target_org_id, out_org
ORDER BY record_count DESC
LIMIT 10;

-- =================================================================================================
-- 第二步：添加备份字段 💾
-- =================================================================================================

-- 添加备份字段（如果不存在）
ALTER TABLE mmis_temp_xinxike_outbound_six
ADD COLUMN IF NOT EXISTS out_target_org_id_backup varchar(100),
ADD COLUMN IF NOT EXISTS out_org_backup varchar(100);

-- 备份原始数据
UPDATE mmis_temp_xinxike_outbound_six
SET
    out_target_org_id_backup = out_target_org_id,
    out_org_backup = out_org;

-- =================================================================================================
-- 第三步：数据移动 🔄
-- =================================================================================================

-- 将out_target_org_id的数据移动到out_org字段
UPDATE mmis_temp_xinxike_outbound_six
SET out_org = COALESCE(out_org, out_target_org_id)
WHERE out_target_org_id IS NOT NULL;

-- 验证数据移动结果
SELECT
    '数据移动验证' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_org IS NOT NULL THEN 1 END) as has_org_name_after_move,
    COUNT(CASE WHEN out_org IS NOT NULL AND TRIM(out_org) != '' THEN 1 END) as valid_org_names
FROM mmis_temp_xinxike_outbound_six;

-- =================================================================================================
-- 第四步：组织信息查询和匹配 🔗
-- =================================================================================================

-- 检查hrm_org表中的组织结构
SELECT
    'hrm_org表检查' as check_type,
    COUNT(*) as total_orgs,
    COUNT(CASE WHEN parent_org_id IS NULL OR parent_org_id = '' THEN 1 END) as root_orgs,
    COUNT(CASE WHEN parent_org_id IS NOT NULL AND parent_org_id != '' THEN 1 END) as child_orgs
FROM hrm_org
WHERE hospital_id = 'zjxrmyy' AND active_flag = '1';

-- 创建临时表存储叶子节点查询结果
CREATE TEMP TABLE temp_leaf_org_mapping AS
WITH RECURSIVE org_hierarchy AS (
    -- 基础查询：找到所有组织
    SELECT
        org_id,
        org_name,
        parent_org_id,
        0 as level
    FROM hrm_org
    WHERE hospital_id = 'zjxrmyy'
      AND active_flag = '1'

    UNION ALL

    -- 递归查询：构建层级关系
    SELECT
        o.org_id,
        o.org_name,
        o.parent_org_id,
        oh.level + 1
    FROM hrm_org o
    INNER JOIN org_hierarchy oh ON o.parent_org_id = oh.org_id
    WHERE o.hospital_id = 'zjxrmyy'
      AND o.active_flag = '1'
      AND oh.level < 10  -- 防止无限递归
),
leaf_nodes AS (
    -- 找到叶子节点（没有子节点的节点）
    SELECT
        oh.org_id,
        oh.org_name,
        oh.parent_org_id,
        oh.level,
        CASE
            WHEN NOT EXISTS (
                SELECT 1 FROM hrm_org child
                WHERE child.parent_org_id = oh.org_id
                  AND child.hospital_id = 'zjxrmyy'
                  AND child.active_flag = '1'
            ) THEN 1
            ELSE 0
        END as is_leaf
    FROM org_hierarchy oh
),
org_name_mapping AS (
    -- 为每个组织名称找到对应的叶子节点
    SELECT DISTINCT
        source_org.out_org as source_org_name,
        ln.org_id as target_org_id,
        ln.org_name as target_org_name,
        ln.level,
        ln.is_leaf,
        ROW_NUMBER() OVER (
            PARTITION BY source_org.out_org
            ORDER BY ln.is_leaf DESC, ln.level DESC, ln.org_id
        ) as priority_rank
    FROM (
        SELECT DISTINCT out_org
        FROM mmis_temp_xinxike_outbound_six
        WHERE out_org IS NOT NULL AND TRIM(out_org) != ''
    ) source_org
    LEFT JOIN leaf_nodes ln ON (
        ln.org_name = source_org.out_org OR
        ln.org_name LIKE '%' || source_org.out_org || '%' OR
        source_org.out_org LIKE '%' || ln.org_name || '%'
    )
    WHERE ln.org_id IS NOT NULL
)
SELECT
    source_org_name,
    target_org_id,
    target_org_name,
    level,
    is_leaf,
    priority_rank
FROM org_name_mapping
WHERE priority_rank = 1;  -- 只取优先级最高的匹配结果

-- 显示组织映射结果
SELECT
    '组织映射结果' as mapping_type,
    source_org_name,
    target_org_id,
    target_org_name,
    CASE WHEN is_leaf = 1 THEN '✅ 叶子节点' ELSE '⚠️ 非叶子节点' END as node_type,
    level as org_level
FROM temp_leaf_org_mapping
ORDER BY source_org_name;

-- =================================================================================================
-- 第五步：更新组织ID 🔄
-- =================================================================================================

-- 清空out_target_org_id字段，准备重新填充
UPDATE mmis_temp_xinxike_outbound_six
SET out_target_org_id = NULL;

-- 根据映射表更新out_target_org_id
UPDATE mmis_temp_xinxike_outbound_six t
SET out_target_org_id = m.target_org_id
FROM temp_leaf_org_mapping m
WHERE t.out_org = m.source_org_name;

-- 对于没有匹配到的组织，尝试精确匹配
UPDATE mmis_temp_xinxike_outbound_six t
SET out_target_org_id = o.org_id
FROM hrm_org o
WHERE t.out_target_org_id IS NULL
  AND t.out_org = o.org_name
  AND o.hospital_id = 'zjxrmyy'
  AND o.active_flag = '1';

-- 对于仍然没有匹配的，设置默认值（信息科）
UPDATE mmis_temp_xinxike_outbound_six
SET out_target_org_id = '521001'  -- 信息科的组织ID
WHERE out_target_org_id IS NULL
  AND out_org IS NOT NULL
  AND TRIM(out_org) != '';

-- =================================================================================================
-- 第六步：验证修复结果 ✅
-- =================================================================================================

-- 修复结果统计
SELECT
    '修复结果统计' as result_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_org IS NOT NULL AND TRIM(out_org) != '' THEN 1 END) as has_org_name,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_org_id,
    COUNT(CASE WHEN out_org IS NOT NULL AND out_target_org_id IS NOT NULL THEN 1 END) as both_fields_filled,
    ROUND(
        COUNT(CASE WHEN out_org IS NOT NULL AND out_target_org_id IS NOT NULL THEN 1 END) * 100.0 /
        NULLIF(COUNT(CASE WHEN out_org IS NOT NULL AND TRIM(out_org) != '' THEN 1 END), 0),
        2
    ) as success_rate_percent
FROM mmis_temp_xinxike_outbound_six;

-- 显示修复后的组织映射情况
SELECT
    '修复后组织映射' as mapping_result,
    t.out_org as org_name,
    t.out_target_org_id as org_id,
    o.org_name as verified_org_name,
    COUNT(*) as record_count,
    CASE
        WHEN o.org_id IS NOT NULL THEN '✅ 映射正确'
        ELSE '❌ 映射失败'
    END as mapping_status
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_org o ON t.out_target_org_id = o.org_id
    AND o.hospital_id = 'zjxrmyy'
    AND o.active_flag = '1'
WHERE t.out_org IS NOT NULL AND TRIM(t.out_org) != ''
GROUP BY t.out_org, t.out_target_org_id, o.org_name
ORDER BY record_count DESC;

-- 检查未成功映射的记录
SELECT
    '未成功映射的记录' as issue_type,
    out_org,
    out_target_org_id,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six
WHERE (out_org IS NOT NULL AND TRIM(out_org) != '')
  AND (out_target_org_id IS NULL OR TRIM(out_target_org_id) = '')
GROUP BY out_org, out_target_org_id
ORDER BY record_count DESC;

-- =================================================================================================
-- 第七步：清理临时数据 🧹
-- =================================================================================================

-- 删除临时表
DROP TABLE IF EXISTS temp_leaf_org_mapping;

-- 显示最终修复总结
SELECT
    '🎯 修复总结' as summary_type,
    '组织字段修复完成' as operation,
    CASE
        WHEN COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*) >= 95
        THEN '✅ 修复成功率 >= 95%'
        WHEN COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*) >= 80
        THEN '⚠️ 修复成功率 >= 80%'
        ELSE '❌ 修复成功率 < 80%，需要人工检查'
    END as result_status,
    ROUND(
        COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
    ) as success_rate
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL AND TRIM(out_org) != '';

-- =================================================================================================
-- 事务控制 ⚠️
-- =================================================================================================

-- 提交事务（如果一切正常）
-- COMMIT;

-- 如果需要回滚，使用以下命令：
-- ROLLBACK;

-- =================================================================================================
-- 使用说明 📖
-- =================================================================================================

/*
脚本功能说明：
1. 将 out_target_org_id 字段的数据移动到 out_target_org 字段
2. 通过 hrm_org 表查询组织信息，优先选择叶子节点
3. 将查询到的正确组织ID写回 out_target_org_id 字段
4. 提供完整的数据备份和验证机制

执行步骤：
1. 数据备份：自动备份原始数据到 *_backup 字段
2. 数据移动：将组织ID数据移动到组织名称字段
3. 智能匹配：通过递归查询找到叶子节点组织
4. 数据更新：将正确的组织ID写回目标字段
5. 结果验证：提供详细的修复结果统计

注意事项：
- 脚本在事务中执行，可以安全回滚
- 优先匹配叶子节点（最底层组织）
- 对于无法匹配的组织，设置默认值为信息科
- 保留原始数据备份，便于问题排查
*/