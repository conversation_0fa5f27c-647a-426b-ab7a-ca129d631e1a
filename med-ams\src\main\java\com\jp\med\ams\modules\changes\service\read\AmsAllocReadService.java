package com.jp.med.ams.modules.changes.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.changes.dto.AmsAllocDto;
import com.jp.med.ams.modules.changes.vo.AmsAllocVo;

import java.util.List;

/**
 * 资产划拨
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
public interface AmsAllocReadService extends IService<AmsAllocDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsAllocVo> queryList(AmsAllocDto dto);

    Object queryPropertyDetail(AmsAllocDto dto);

    Integer queryAuditCount(AmsAllocDto dto);
}

