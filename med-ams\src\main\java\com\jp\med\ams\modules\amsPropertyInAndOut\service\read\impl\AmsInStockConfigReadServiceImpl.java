package com.jp.med.ams.modules.amsPropertyInAndOut.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsInStockConfigReadMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInStockConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInStockConfigVo;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.read.AmsInStockConfigReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsInStockConfigReadServiceImpl extends ServiceImpl<AmsInStockConfigReadMapper, AmsInStockConfigDto> implements AmsInStockConfigReadService {

    @Autowired
    private AmsInStockConfigReadMapper amsInStockConfigReadMapper;

    @Override
    public List<AmsInStockConfigVo> queryList(AmsInStockConfigDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsInStockConfigReadMapper.queryList(dto);
    }

}
