package com.jp.med.erp.modules.vcrGen.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrItemDetailDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpVcrItemDetailVo;

import java.util.List;

/**
 * 凭证信息明细
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
public interface ErpVcrItemDetailReadService extends IService<ErpVcrItemDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrItemDetailVo> queryList(ErpVcrItemDetailDto dto);
}

