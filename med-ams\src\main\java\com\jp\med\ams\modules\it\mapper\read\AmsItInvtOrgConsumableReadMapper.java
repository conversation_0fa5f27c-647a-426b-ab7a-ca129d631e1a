package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItInvtAddDto;
import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.dto.AmsItInvtOrgConsumableDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 科室耗材配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 15:04:47
 */
@Mapper
public interface AmsItInvtOrgConsumableReadMapper extends BaseMapper<AmsItInvtOrgConsumableDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItInvtOrgConsumableVo> queryList(AmsItInvtOrgConsumableDto dto);


    List<AmsItInvtOrgConsumableVo> selectByOrgId(String orgId);

    List<AmsItInvtOrgConsumableVo> selectOrgList(AmsItInvtOrgConsumableDto dto);

    List<AmsItInvtOrgConsumableVo> queryIsUsed(AmsItInvtCfgDto addDto);
}
