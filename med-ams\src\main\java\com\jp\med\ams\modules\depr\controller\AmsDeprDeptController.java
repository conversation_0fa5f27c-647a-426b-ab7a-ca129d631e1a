package com.jp.med.ams.modules.depr.controller;

import com.jp.med.ams.modules.depr.dto.AmsDeprDeptDto;
import com.jp.med.ams.modules.depr.service.read.impl.AmsDeprDeptReadServiceImpl;
import com.jp.med.ams.modules.depr.service.write.AmsDeprDeptWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 存储部门信息，包括折旧额和其他相关数据
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-07-19 16:16:26
 */
@Api(value = "存储部门信息，包括折旧额和其他相关数据", tags = "存储部门信息，包括折旧额和其他相关数据")
@RestController
@RequestMapping("amsDeprDept")
public class AmsDeprDeptController {

    @Autowired
    private AmsDeprDeptReadServiceImpl amsDeprDeptReadService;

    @Autowired
    private AmsDeprDeptWriteService amsDeprDeptWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询存储部门信息，包括折旧额和其他相关数据")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody AmsDeprDeptDto dto) {
        return CommonResult.paging(amsDeprDeptReadService.queryPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询存储部门信息，包括折旧额和其他相关数据")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsDeprDeptDto dto) {
        return CommonResult.success(amsDeprDeptReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增存储部门信息，包括折旧额和其他相关数据")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsDeprDeptDto dto) {
        amsDeprDeptWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改存储部门信息，包括折旧额和其他相关数据")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsDeprDeptDto dto) {
        amsDeprDeptWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除存储部门信息，包括折旧额和其他相关数据")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsDeprDeptDto dto) {
        amsDeprDeptWriteService.removeById(dto);
        return CommonResult.success();
    }

}
