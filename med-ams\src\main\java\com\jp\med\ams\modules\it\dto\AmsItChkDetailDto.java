package com.jp.med.ams.modules.it.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 耗材申请审核详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
@TableName("ams_it_chk_detail" )
public class AmsItChkDetailDto extends CommonQueryDto {


    @TableId("id")
    private Integer id;

    /** 申请详情id */
    @TableField("apply_detail_id")
    private Integer applyDetailId;

    /** 名称 */
    @TableField("name")
    private String name;

    /** 耗材名称 */
    @TableField("name2")
    private String name2;

    /** 供应商 */
    @TableField("provider")
    private String provider;

    /** 品牌 */
    @TableField("brand")
    private String brand;

    /** 生产厂家 */
    @TableField("factory")
    private String factory;

    /** 型号 */
    @TableField("model")
    private String model;

    /** 耗材型号 */
    private String model2;

    /** 价格 */
    @TableField("price")
    private BigDecimal price;

    /** 出库数量 */
    @TableField("out_num")
    private Integer outNum;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 更新人 */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

    /** 删除人 */
    @TableField("delter")
    private String delter;

    /** 删除时间 */
    @TableField("delete_time")
    private String deleteTime;

    /** 有效标志(1:删除) */
    @TableField("active_flag")
    private String activeFlag;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /**
    *申请详情集合
    *
    */
    @TableField(exist = false)
    private List<Integer> applyDetailIds;

}
