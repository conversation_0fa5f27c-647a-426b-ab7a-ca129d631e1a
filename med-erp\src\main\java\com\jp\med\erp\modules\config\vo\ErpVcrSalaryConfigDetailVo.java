package com.jp.med.erp.modules.config.vo;

import lombok.Data;

/**
 * 财务核算-工资凭证科目映射配置明细
 * <AUTHOR>
 * @email -
 * @date 2024-08-22 09:54:23
 */
@Data
public class ErpVcrSalaryConfigDetailVo {

	/** id */
	private Integer id;

	/** 工资配置id */
	private Integer salaryConfigId;

	/** 会计科目代码 */
	private String actigSubCode;

	/** 会计科目名称 */
	private String actigSubName;

	/** 科目类型  1.会计 2.预算 */
	private String actigSys;

	/** 科室代码 */
	private String deptCode;

	/** 科室名称 */
	private String deptName;

	/** 往来单位代码 */
	private String relCoCode;

	/** 往来单位名称 */
	private String relCoName;

	/** 功能科目代码 */
	private String funSubCode;

	/** 功能科目名称 */
	private String funSubName;

	/** 经济科目代码 */
	private String econSubCode;

	/** 经济科目名称 */
	private String econSubName;

	/** 项目代码 */
	private String projCode;

	/** 项目名称 */
	private String projName;

	/** 现金流量代码 */
	private String cashFlowCode;

	/** 现金流量名称 */
	private String cashFlowName;

	/** 金额类型 1:借 2:贷 */
	private String actigAmtType;

	/** 摘要 */
	private String abst;

}
