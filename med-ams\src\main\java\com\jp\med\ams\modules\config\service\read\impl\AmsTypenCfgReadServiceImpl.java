package com.jp.med.ams.modules.config.service.read.impl;

import com.jp.med.common.util.TreeNewUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.config.mapper.read.AmsTypenCfgReadMapper;
import com.jp.med.ams.modules.config.dto.AmsTypenCfgDto;
import com.jp.med.ams.modules.config.vo.AmsTypenCfgVo;
import com.jp.med.ams.modules.config.service.read.AmsTypenCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsTypenCfgReadServiceImpl extends ServiceImpl<AmsTypenCfgReadMapper, AmsTypenCfgDto> implements AmsTypenCfgReadService {

    @Autowired
    private AmsTypenCfgReadMapper amsTypenCfgReadMapper;

    @Override
    public List<AmsTypenCfgVo> queryList(AmsTypenCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<AmsTypenCfgVo> typenCfgVos = amsTypenCfgReadMapper.queryList(dto);
        TreeNewUtil<String, AmsTypenCfgVo> treeNewUtil = new TreeNewUtil<>();
        return treeNewUtil.buildTree(typenCfgVos);
    }

    @Override
    public List<AmsTypenCfgVo> queryNormal(AmsTypenCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return amsTypenCfgReadMapper.queryList(dto);
    }

}
