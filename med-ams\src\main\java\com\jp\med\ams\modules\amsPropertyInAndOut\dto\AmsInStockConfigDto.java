package com.jp.med.ams.modules.amsPropertyInAndOut.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInStockConfigVo;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Data
@TableName("ams_out_stock_config")
public class AmsInStockConfigDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 出库科室
     */
    @TableField("out_dept")
    private String outDept;

    /**
     * 字段
     */
    @TableField("fld")
    private String fld;

    /**
     * 是否必填(1：必填)
     */
    @TableField("mustl")
    private String mustl;

    /**
     * 字段类型（1：资产字段）
     */
    @TableField("fld_type")
    private String fldType;

    @TableField(exist = false)
    private List<AmsInStockConfigVo> list;

    @TableField(exist = false)
    private List<String> flds;


}
