package com.jp.med.ams.modules.changes.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.changes.dto.AmsAllocDto;
import com.jp.med.ams.modules.changes.vo.AmsAllocVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产划拨
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
@Mapper
public interface AmsAllocReadMapper extends BaseMapper<AmsAllocDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsAllocVo> queryList(AmsAllocDto dto);

    /**
     * 划拨申请提交校验
     * @param dto
     * @return
     */
    boolean validateAllocApply(AmsAllocDto dto);


    List<AmsPropertyDto> selectAllocPropertyRefFacode(AmsAllocDto amsAllocDto);

    /**
     * 批量查询划拨记录对应的资产代码
     *
     * @param allocIds 划拨记录ID列表
     * @return 划拨记录关联的资产代码列表，包含allocId信息
     */
    List<AmsPropertyDto> batchSelectAllocPropertyRefFacode(@Param("allocIds") List<Long> allocIds);

    Integer queryAuditCount(AmsAllocDto dto);

    /**
     * 删除校验
     * @param dto
     * @return
     */
    boolean validateAllocCanDel(AmsAllocDto dto);
}
