package com.jp.med.ams.modules.inventory.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;
import com.jp.med.ams.modules.inventory.vo.AmsIntrDetailVo;
import com.jp.med.ams.modules.inventory.vo.AmsIntrTaskVo;

import java.util.List;
import java.util.Map;

/**
 * 资产盘点任务
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 10:12:34
 */
public interface AmsIntrTaskReadService extends IService<AmsIntrTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsIntrTaskVo> queryList(AmsIntrTaskDto dto);

    /**
     * 查询资产盘点任务
     * @param dto
     * @return
     */
    List<AmsIntrTaskVo> queryListNoPaging(AmsIntrTaskDto dto);

    /**
     * 查询资产盘点情况
     * @param dto
     * @return
     */
    List<AmsIntrDetailVo> queryIntr(AmsIntrTaskDto dto);

    Map<String, List<AmsIntrDetailVo>> diff(AmsIntrTaskDto dto);
}

