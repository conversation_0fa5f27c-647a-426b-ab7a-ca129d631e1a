package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary2;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 数据合并处理器
 * 负责合并本月和上月的折旧数据
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyDataMergeProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> {

    @Override
    protected void doProcess(AmsPropertyDeprSummary2Context context) {
        log.debug("开始处理数据合并");

        // 合并数据
        mergeDeprData(
                context.getCurrentMonthPropertyDepr2Vos(),
                context.getCurrentMonthMap(),
                context.getLastMonthMap(),
                context.isUseTypeNCode());

        log.debug("数据合并处理完成");
    }

    /**
     * 合并折旧数据
     * 
     * @param currentMonthPropertyDepr2Vos 本月折旧数据列表
     * @param currentMonthMap              本月数据映射
     * @param lastMonthMap                 上月数据映射
     * @param useTypeNCode                 是否使用新类型代码
     */
    private void mergeDeprData(java.util.List<AmsPropertyDepr2Vo> currentMonthPropertyDepr2Vos,
            Map<String, AmsPropertyDepr2Vo> currentMonthMap,
            Map<String, AmsPropertyDepr2Vo> lastMonthMap,
            boolean useTypeNCode) {

        for (AmsPropertyDepr2Vo currentVo : currentMonthPropertyDepr2Vos) {
            String key = createMergeKey(currentVo, useTypeNCode);
            AmsPropertyDepr2Vo lastMonthVo = lastMonthMap.get(key);

            if (lastMonthVo != null) {
                // 设置上月原值总额
                currentVo.setLastMonthDeprAmt(lastMonthVo.getMonthDeprAmt());

                // 计算变化总额
                BigDecimal change = BigDecimal.ZERO;
                if (currentVo.getMonthDeprAmt() != null && lastMonthVo.getMonthDeprAmt() != null) {
                    change = currentVo.getMonthDeprAmt().subtract(lastMonthVo.getMonthDeprAmt());
                } else if (currentVo.getMonthDeprAmt() != null) {
                    change = currentVo.getMonthDeprAmt();
                } else if (lastMonthVo.getMonthDeprAmt() != null) {
                    change = lastMonthVo.getMonthDeprAmt().negate();
                }
                currentVo.setLastMonthDeprAmtChange(change);
            } else {
                // 上月没有数据，设置为0
                currentVo.setLastMonthDeprAmt(BigDecimal.ZERO);
                currentVo.setLastMonthDeprAmtChange(
                        currentVo.getMonthDeprAmt() != null ? currentVo.getMonthDeprAmt() : BigDecimal.ZERO);
            }
        }
    }

    /**
     * 创建合并键
     * 
     * @param vo           折旧VO对象
     * @param useTypeNCode 是否使用新类型代码
     * @return 合并键
     */
    private String createMergeKey(AmsPropertyDepr2Vo vo, boolean useTypeNCode) {
        String typeKey = useTypeNCode ? vo.getAssetTypeCode() : vo.getAssetTypeName();
        return vo.getDeptUseCode() + "-" + typeKey + "-" + vo.getSourceCode();
    }
}
