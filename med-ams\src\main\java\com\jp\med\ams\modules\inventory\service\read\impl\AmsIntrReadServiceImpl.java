package com.jp.med.ams.modules.inventory.service.read.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrDetailReadMapper;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrReadMapper;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrTaskReadMapper;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrReadService;
import com.jp.med.ams.modules.inventory.vo.AmsIntrVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class AmsIntrReadServiceImpl extends ServiceImpl<AmsIntrReadMapper, AmsIntrDto> implements AmsIntrReadService {

    @Autowired
    private AmsIntrReadMapper amsIntrReadMapper;

    @Autowired
    private AmsIntrDetailReadMapper amsIntrDetailReadMapper;

    @Autowired
    private AmsIntrTaskReadMapper amsIntrTaskReadMapper;

    @Override
    public List<AmsIntrVo> queryList(AmsIntrDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsIntrReadMapper.queryList(dto);
    }

    @Override
    public Map<String, Object> getLatestIntrStatus(AmsIntrTodoDto dto) {
        // 1. 获取任务基本信息
        AmsIntrTaskDto taskInfo = amsIntrTaskReadMapper.selectById(dto.getTaskId());

        // 2. 🔥 修复：获取已盘点的资产UID列表，返回标准化格式便于前端处理
        // 数据库中存储的是完整的RFID标签ID，返回时去除前导零以匹配前端处理逻辑
        List<String> inventoriedUids = amsIntrDetailReadMapper.selectList(
                        new QueryWrapper<AmsIntrDetailDto>().lambda()
                                .select(AmsIntrDetailDto::getUid)
                                .eq(AmsIntrDetailDto::getTaskId, dto.getTaskId())
                                .groupBy(AmsIntrDetailDto::getUid)
                ).stream()
                .map(item -> removeLeadingZeros(item.getUid()))
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("taskId", dto.getTaskId());
        result.put("totalAssets", taskInfo != null ? taskInfo.getTotlcnt() : 0);
        result.put("inventoriedCount", inventoriedUids.size());
        result.put("inventoriedUids", inventoriedUids);
        result.put("lastUpdateTime", System.currentTimeMillis());

        return result;
    }

    /**
     * 移除UID前导零
     *
     * @param uid 原始UID
     * @return 移除前导零后的UID
     */
    private String removeLeadingZeros(String uid) {
        return uid != null ? uid.replaceFirst("^0+", "") : "";
    }

}
