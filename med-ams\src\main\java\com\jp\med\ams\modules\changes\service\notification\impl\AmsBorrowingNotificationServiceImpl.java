package com.jp.med.ams.modules.changes.service.notification.impl;

import com.jp.med.ams.modules.changes.dto.AmsBorrowingAuditNotificationDto;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.feign.AmsMessageFeignService;
import com.jp.med.ams.modules.changes.mapper.read.AmsChngBrwgReadMapper;
import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.ienum.SysMessageTypeEnum;
import com.jp.med.common.util.FeignExecuteUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * 资产借用消息通知服务实现类
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
@Slf4j
@Service
public class AmsBorrowingNotificationServiceImpl implements AmsBorrowingNotificationService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private AmsMessageFeignService amsMessageFeignService;
    @Autowired
    private AmsChngBrwgReadMapper amsChngBrwgReadMapper;

    @Override
    public void sendApplicationSubmittedNotification(AmsChngBrwgVo borrowingRecord) {
        log.info("📝 发送借用申请提交通知: 申请人={}, 资产={}", borrowingRecord.getCrterName(), borrowingRecord.getAssetName());

        try {
            // 构建消息给审核人员（这里简化为发给创建人的科室负责人）
            SysMessageDto message = new SysMessageDto();

            // 设置接收人（实际应该查询科室负责人或审核人员）
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getCrter()); // 临时发给创建人，实际应该发给审核人
            message.setUsers(recipients.toArray(new String[0]));

            // 设置消息内容
            message.setCreator("SYSTEM");
            message.setTitle("新的资产借用申请待审核");
            message.setPushText(String.format("%s申请借用资产【%s】，请及时审核处理",
                    borrowingRecord.getCrterName(),
                    !borrowingRecord.getAssetName().isEmpty() ? borrowingRecord.getAssetName() : ""));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/audit/" + borrowingRecord.getId());

            // 发送消息
            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 借用申请提交通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送借用申请提交通知失败", e);
        }
    }

    @Override
    public void sendApplicationApprovedNotification(AmsChngBrwgVo borrowingRecord) {
        log.info("✅ 发送借用申请通过通知: 借用人={}, 资产={}", borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName());

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用申请已通过");
            message.setPushText(String.format("您申请借用的资产【%s】已审核通过，请按时归还。预计归还时间：%s",
                    borrowingRecord.getAssetName(), borrowingRecord.getExpRtnTime()));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-borrowings");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 借用申请通过通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送借用申请通过通知失败", e);
        }
    }

    @Override
    public void sendApplicationRejectedNotification(AmsChngBrwgVo borrowingRecord, String rejectReason) {
        log.info("❌ 发送借用申请拒绝通知: 借用人={}, 资产={}, 原因={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), rejectReason);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用申请被拒绝");
            message.setPushText(String.format("您申请借用的资产【%s】被拒绝。拒绝原因：%s",
                    borrowingRecord.getAssetName(), rejectReason != null ? rejectReason : "未提供原因"));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-applications");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 借用申请拒绝通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送借用申请拒绝通知失败", e);
        }
    }

    @Override
    public void sendBorrowingSuccessNotification(AmsChngBrwgVo borrowingRecord) {
        log.info("🎉 发送借用成功通知: 借用人={}, 资产={}", borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName());

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用成功");
            message.setPushText(String.format("您已成功借用资产【%s】，请妥善保管并按时归还。预计归还时间：%s",
                    borrowingRecord.getAssetName(), borrowingRecord.getExpRtnTime()));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-borrowings");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 借用成功通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送借用成功通知失败", e);
        }
    }

    @Override
    public void sendBorrowingFailureNotification(AmsChngBrwgVo borrowingRecord, String failureReason) {
        log.info("💥 发送借用失败通知: 借用人={}, 资产={}, 原因={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), failureReason);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用失败");
            message.setPushText(String.format("资产【%s】借用失败。失败原因：%s",
                    borrowingRecord.getAssetName(), failureReason != null ? failureReason : "系统异常"));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-applications");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 借用失败通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送借用失败通知失败", e);
        }
    }

    @Override
    public void sendReturnReminderNotification(AmsChngBrwgVo borrowingRecord, int daysBeforeExpiry) {
        log.info("⏰ 发送归还提醒通知: 借用人={}, 资产={}, 剩余天数={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), daysBeforeExpiry);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产归还提醒");

            String reminderText;
            if (daysBeforeExpiry > 0) {
                reminderText = String.format("您借用的资产【%s】将在%d天后到期，请及时归还。预计归还时间：%s",
                        borrowingRecord.getAssetName(), daysBeforeExpiry, borrowingRecord.getExpRtnTime());
            } else if (daysBeforeExpiry == 0) {
                reminderText = String.format("您借用的资产【%s】今天到期，请及时归还。预计归还时间：%s",
                        borrowingRecord.getAssetName(), borrowingRecord.getExpRtnTime());
            } else {
                reminderText = String.format("您借用的资产【%s】已逾期%d天，请立即归还！预计归还时间：%s",
                        borrowingRecord.getAssetName(), Math.abs(daysBeforeExpiry), borrowingRecord.getExpRtnTime());
            }

            message.setPushText(reminderText);
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/return/" + borrowingRecord.getId());

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 归还提醒通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送归还提醒通知失败", e);
        }
    }

    @Override
    public void sendOverdueWarningNotification(AmsChngBrwgVo borrowingRecord, int overdueDays) {
        log.info("🚨 发送逾期警告通知: 借用人={}, 资产={}, 逾期天数={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), overdueDays);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人和管理员
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            // 这里应该添加管理员或科室负责人
            recipients.add(borrowingRecord.getCrter()); // 临时添加创建人
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产逾期警告");
            message.setPushText(String.format("⚠️ 资产【%s】已逾期%d天未归还，请立即处理！借用人：%s，预计归还时间：%s",
                    borrowingRecord.getAssetName(), overdueDays, borrowingRecord.getLoaneeName(),
                    borrowingRecord.getExpRtnTime()));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/overdue");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 逾期警告通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送逾期警告通知失败", e);
        }
    }

    @Override
    public void sendReturnConfirmationNotification(AmsChngBrwgVo borrowingRecord) {
        log.info("✅ 发送归还确认通知: 借用人={}, 资产={}", borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName());

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产归还确认");
            message.setPushText(String.format("您借用的资产【%s】已成功归还，感谢您的配合。归还时间：%s",
                    borrowingRecord.getAssetName(), borrowingRecord.getActRtnTime()));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/history");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 归还确认通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送归还确认通知失败", e);
        }
    }

    @Override
    public void sendExtensionRequestNotification(AmsChngBrwgVo borrowingRecord, String extensionReason,
                                                 String newExpectedReturnTime) {
        log.info("📋 发送续期申请通知: 借用人={}, 资产={}, 新归还时间={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), newExpectedReturnTime);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给审核人员
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getCrter()); // 临时发给创建人，实际应该发给审核人
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用续期申请");
            message.setPushText(String.format("%s申请延长资产【%s】的借用期限至%s，续期原因：%s",
                    borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), newExpectedReturnTime,
                    extensionReason));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/extension-audit/" + borrowingRecord.getId());

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 续期申请通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送续期申请通知失败", e);
        }
    }

    @Override
    public void sendExtensionApprovedNotification(AmsChngBrwgVo borrowingRecord, String newExpectedReturnTime) {
        log.info("✅ 发送续期申请通过通知: 借用人={}, 资产={}, 新归还时间={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), newExpectedReturnTime);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用续期申请已通过");
            message.setPushText(String.format("您的资产【%s】续期申请已通过，新的归还时间为：%s，请按时归还",
                    borrowingRecord.getAssetName(), newExpectedReturnTime));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-borrowings");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 续期申请通过通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送续期申请通过通知失败", e);
        }
    }

    @Override
    public void sendExtensionRejectedNotification(AmsChngBrwgVo borrowingRecord, String rejectReason) {
        log.info("❌ 发送续期申请拒绝通知: 借用人={}, 资产={}, 原因={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), rejectReason);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用续期申请被拒绝");
            message.setPushText(String.format("您的资产【%s】续期申请被拒绝，请按原定时间归还。拒绝原因：%s",
                    borrowingRecord.getAssetName(), rejectReason != null ? rejectReason : "未提供原因"));
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-borrowings");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 续期申请拒绝通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送续期申请拒绝通知失败", e);
        }
    }

    @Override
    public void sendBatchExpiryReminders() {
        log.info("🔄 开始批量发送到期提醒通知");

        try {
            // 查询即将到期的借用记录（3天内到期）
            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setProsstas(MedConst.TYPE_4); // 借用中状态
            queryDto.setBusstas(MedConst.TYPE_1); // 业务状态为借用中

            List<AmsChngBrwgVo> borrowingRecords = amsChngBrwgReadMapper.queryList(queryDto);

            LocalDateTime now = LocalDateTime.now();
            int reminderCount = 0;

            for (AmsChngBrwgVo record : borrowingRecords) {
                if (record.getExpRtnTime() != null) {
                    try {
                        LocalDateTime expTime = LocalDateTime.parse(record.getExpRtnTime(), DATE_FORMATTER);
                        long daysUntilExpiry = ChronoUnit.DAYS.between(now, expTime);

                        // 在到期前3天、1天和当天发送提醒
                        if (daysUntilExpiry == 3 || daysUntilExpiry == 1 || daysUntilExpiry == 0) {
                            sendReturnReminderNotification(record, (int) daysUntilExpiry);
                            reminderCount++;
                        }
                    } catch (Exception e) {
                        log.warn("⚠️ 解析到期时间失败: recordId={}, expTime={}", record.getId(), record.getExpRtnTime());
                    }
                }
            }

            log.info("✅ 批量到期提醒发送完成，共发送{}条提醒", reminderCount);

        } catch (Exception e) {
            log.error("❌ 批量发送到期提醒失败", e);
        }
    }

    @Override
    public void sendBatchOverdueWarnings() {
        log.info("🔄 开始批量发送逾期警告通知");

        try {
            // 查询逾期的借用记录
            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setProsstas(MedConst.TYPE_4); // 借用中状态
            queryDto.setBusstas(MedConst.TYPE_1); // 业务状态为借用中

            List<AmsChngBrwgVo> borrowingRecords = amsChngBrwgReadMapper.queryList(queryDto);

            LocalDateTime now = LocalDateTime.now();
            int warningCount = 0;

            for (AmsChngBrwgVo record : borrowingRecords) {
                if (record.getExpRtnTime() != null) {
                    try {
                        LocalDateTime expTime = LocalDateTime.parse(record.getExpRtnTime(), DATE_FORMATTER);
                        long daysSinceExpiry = ChronoUnit.DAYS.between(expTime, now);

                        // 逾期1天、3天、7天、15天、30天发送警告
                        if (daysSinceExpiry > 0 && (daysSinceExpiry == 1 || daysSinceExpiry == 3 ||
                                daysSinceExpiry == 7 || daysSinceExpiry == 15 || daysSinceExpiry == 30)) {
                            sendOverdueWarningNotification(record, (int) daysSinceExpiry);
                            warningCount++;
                        }
                    } catch (Exception e) {
                        log.warn("⚠️ 解析到期时间失败: recordId={}, expTime={}", record.getId(), record.getExpRtnTime());
                    }
                }
            }

            log.info("✅ 批量逾期警告发送完成，共发送{}条警告", warningCount);

        } catch (Exception e) {
            log.error("❌ 批量发送逾期警告失败", e);
        }
    }

    @Override
    public void sendSystemMaintenanceNotification(String maintenanceMessage, String startTime, String endTime) {
        log.info("🔧 发送系统维护通知: 开始时间={}, 结束时间={}", startTime, endTime);

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给所有相关用户（这里简化处理，实际应该查询所有活跃用户）
            List<String> recipients = new ArrayList<>();
            recipients.add("ALL_USERS"); // 特殊标识，表示发送给所有用户
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("系统维护通知");
            message.setPushText(String.format("系统将于%s至%s进行维护，期间可能影响资产借用功能的使用。%s",
                    startTime, endTime, maintenanceMessage));
            message.setType(SysMessageTypeEnum.SYSTEM.getCode());
            message.setGotoUrl("/system/maintenance");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 系统维护通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送系统维护通知失败", e);
        }
    }

    // ==================== 增强版通知方法（包含审核人信息） ====================

    @Override
    public void sendApplicationApprovedNotification(AmsChngBrwgVo borrowingRecord, AmsBorrowingAuditNotificationDto notificationDto) {
        log.info("✅ 发送增强版借用申请通过通知: 借用人={}, 资产={}, 审核人={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), notificationDto.getAuditorName());

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用申请已通过");

            // 增强的消息内容，包含审核人信息
            String enhancedMessage = String.format("您申请借用的资产【%s】已审核通过，请按时归还。\n" +
                            "审核人：%s（%s）\n" +
                            "审核时间：%s\n" +
                            "预计归还时间：%s",
                    borrowingRecord.getAssetName(),
                    notificationDto.getAuditorName(),
                    notificationDto.getAuditorDept() != null ? notificationDto.getAuditorDept() : "未知科室",
                    notificationDto.getAuditTime(),
                    borrowingRecord.getExpRtnTime());

            message.setPushText(enhancedMessage);
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-borrowings");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 增强版借用申请通过通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送增强版借用申请通过通知失败", e);
        }
    }

    @Override
    public void sendApplicationRejectedNotification(AmsChngBrwgVo borrowingRecord, AmsBorrowingAuditNotificationDto notificationDto) {
        log.info("❌ 发送增强版借用申请拒绝通知: 借用人={}, 资产={}, 审核人={}, 原因={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(),
                notificationDto.getAuditorName(), notificationDto.getAuditRemarks());

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产借用申请被拒绝");

            // 增强的消息内容，包含审核人信息和详细拒绝原因
            String enhancedMessage = String.format("您申请借用的资产【%s】被拒绝。\n" +
                            "审核人：%s（%s）\n" +
                            "审核时间：%s\n" +
                            "拒绝原因：%s",
                    borrowingRecord.getAssetName(),
                    notificationDto.getAuditorName(),
                    notificationDto.getAuditorDept() != null ? notificationDto.getAuditorDept() : "未知科室",
                    notificationDto.getAuditTime(),
                    notificationDto.getAuditRemarks() != null ? notificationDto.getAuditRemarks() : "未提供原因");

            message.setPushText(enhancedMessage);
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/my-applications");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 增强版借用申请拒绝通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送增强版借用申请拒绝通知失败", e);
        }
    }

    @Override
    public void sendReturnConfirmationNotification(AmsChngBrwgVo borrowingRecord, AmsBorrowingAuditNotificationDto notificationDto) {
        log.info("✅ 发送增强版归还确认通知: 借用人={}, 资产={}, 确认人={}",
                borrowingRecord.getLoaneeName(), borrowingRecord.getAssetName(), notificationDto.getAuditorName());

        try {
            SysMessageDto message = new SysMessageDto();

            // 发送给借用人
            List<String> recipients = new ArrayList<>();
            recipients.add(borrowingRecord.getLoanee());
            message.setUsers(recipients.toArray(new String[0]));

            message.setCreator("SYSTEM");
            message.setTitle("资产归还确认");

            // 增强的消息内容，包含确认人信息
            String enhancedMessage = String.format("您借用的资产【%s】已成功归还，感谢您的配合。\n" +
                            "确认人：%s（%s）\n" +
                            "确认时间：%s\n" +
                            "实际归还时间：%s",
                    borrowingRecord.getAssetName(),
                    notificationDto.getAuditorName(),
                    notificationDto.getAuditorDept() != null ? notificationDto.getAuditorDept() : "未知科室",
                    notificationDto.getAuditTime(),
                    borrowingRecord.getActRtnTime());

            message.setPushText(enhancedMessage);
            message.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());
            message.setGotoUrl("/ams/borrowing/history");

            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(message));
            log.info("✅ 增强版归还确认通知发送成功");

        } catch (Exception e) {
            log.error("❌ 发送增强版归还确认通知失败", e);
        }
    }
}
