package com.jp.med.ams.modules.changes.strategy;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.common.context.UserContext;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 抽象变更记录处理器
 * 使用模板方法模式定义处理流程
 */
public abstract class AbstractChangeRecordProcessor implements ChangeRecordProcessor {

    @Override
    public final ProcessResult process(AmsChgRcdDto dto, ProcessContext context) {
        try {
            // 模板方法：定义处理流程
            List<String> faCodes = extractFaCodes(dto);
            if (faCodes.isEmpty()) {
                return new ProcessResult(false, "资产编码列表为空");
            }

            List<AmsChgRcdDto> changeRecords = new ArrayList<>();
            List<AmsPropertyDto> propertyUpdates = new ArrayList<>();

            for (String faCode : faCodes) {
                // 创建变更记录
                AmsChgRcdDto changeRecord = createChangeRecord(dto, faCode, context);
                changeRecords.add(changeRecord);

                // 处理资产更新（如果需要）
                AmsPropertyDto propertyUpdate = createPropertyUpdate(dto, faCode, context);
                if (propertyUpdate != null) {
                    propertyUpdates.add(propertyUpdate);
                }
            }

            return new ProcessResult(changeRecords, propertyUpdates);

        } catch (Exception e) {
            return new ProcessResult(false, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 提取资产编码列表
     */
    protected List<String> extractFaCodes(AmsChgRcdDto dto) {
        List<String> faCodes = dto.getFaCodes();
        if (faCodes != null) {
            return faCodes.stream()
                    .filter(faCode -> faCode != null && !faCode.trim().isEmpty())
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 创建变更记录
     */
    protected AmsChgRcdDto createChangeRecord(AmsChgRcdDto dto, String faCode, ProcessContext context) {
        AmsChgRcdDto changeRecord = new AmsChgRcdDto();
        changeRecord.setFaCode(faCode);
        changeRecord.setChgNo(context.getMaxChgCode() + 1);
        changeRecord.setRedcWay(dto.getRedcWay());
        changeRecord.setChgDate(context.getFormattedDateTime());
        changeRecord.setChgType(dto.getChgType());
        changeRecord.setChgRea(dto.getChgRea());
        changeRecord.setMemo(dto.getMemo());
        changeRecord.setCreateTime(context.getFormattedDateTime());
        changeRecord.setTransferTo(dto.getTransferTo());
        changeRecord.setHospitalId(dto.getHospitalId());

        // 设置变更前后值
        setChangeValues(changeRecord, faCode, context);

        // 批量处理时设置审核信息
        if (isBatchProcessing(dto)) {
            setBatchProcessingInfo(changeRecord);
        } else {
            setNormalProcessingInfo(changeRecord, dto);
        }

        return changeRecord;
    }

    /**
     * 设置变更前后值
     */
    protected void setChangeValues(AmsChgRcdDto changeRecord, String faCode, ProcessContext context) {
        AmsPropertyDto queryDto = new AmsPropertyDto();
        queryDto.setFaCode(faCode);
        AmsPropertyVo propertyVo = context.getAmsPropertyReadService().queryByCode(queryDto);

        if (propertyVo != null && propertyVo.getAssetNav() != null) {
            changeRecord.setChgBefore(propertyVo.getAssetNav().toString());
        } else {
            changeRecord.setChgBefore("0");
        }
        changeRecord.setChgAfter("0");
    }

    /**
     * 判断是否为批量处理
     */
    protected boolean isBatchProcessing(AmsChgRcdDto dto) {
        return dto.getBatchProcessing() != null && "1".equals(dto.getBatchProcessing());
    }

    /**
     * 设置批量处理信息
     */
    protected void setBatchProcessingInfo(AmsChgRcdDto changeRecord) {
        String empCode = UserContext.getEmpCode();
        changeRecord.setChger(empCode);
        changeRecord.setChker(empCode);
        changeRecord.setCrter(empCode);
    }

    /**
     * 设置正常处理信息
     */
    protected void setNormalProcessingInfo(AmsChgRcdDto changeRecord, AmsChgRcdDto dto) {
        if (dto.getSysUser() != null && dto.getSysUser().getHrmUser() != null) {
            changeRecord.setChger(dto.getSysUser().getHrmUser().getEmpCode());
        }
        changeRecord.setChker("");
    }

    /**
     * 创建资产更新信息（子类可重写）
     */
    protected abstract AmsPropertyDto createPropertyUpdate(AmsChgRcdDto dto, String faCode, ProcessContext context);
}
