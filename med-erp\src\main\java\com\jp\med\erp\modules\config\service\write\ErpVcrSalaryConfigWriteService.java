package com.jp.med.erp.modules.config.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDto;

/**
 * 工资凭证配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 00:52:39
 */
public interface ErpVcrSalaryConfigWriteService extends IService<ErpVcrSalaryConfigDto> {
    void saveDetails(ErpVcrSalaryConfigDetailDto dto);

    void toCfgTempReduce(ErpVcrSalaryConfigDto dto);

    void toUpdTempReduce(ErpVcrSalaryConfigDto dto);
}

