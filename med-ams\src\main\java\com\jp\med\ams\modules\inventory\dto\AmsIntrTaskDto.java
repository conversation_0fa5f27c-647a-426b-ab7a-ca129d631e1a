package com.jp.med.ams.modules.inventory.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.util.List;

/**
 * 资产盘点任务
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 10:12:34
 */
@Data
@TableName("ams_intr_task" )
public class AmsIntrTaskDto extends CommonQueryDto {

    /** ID */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /** 盘点任务名称 */
    @TableField(value = "intr_task",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String intrTask;

    /** 待盘点科室 */
    @TableField(value = "dept",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String dept;

    /** 总数量 */
    @TableField(value = "totlcnt",insertStrategy = FieldStrategy.NOT_EMPTY)
    private Integer totlcnt;

    /** 创建人 */
    @TableField(value = "crter",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String crter;

    /** 创建时间 */
    @TableField(value = "create_time",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String createTime;

    /** 有效标志 */
    @TableField(value = "flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String flag;

    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

    /** 状态(0:盘点中,1:盘点完成) */
    @TableField(value = "status",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String status;

    /** 资产列表 */
    @TableField(exist = false)
    private List<AmsIntrTodoDto> list;

    /** id集合 */
    @TableField(exist = false)
    private List<Integer> ids;

    /** 是否盘点 */
    @TableField(exist = false)
    private String isIntr;
    /** 存放地点 */
    @TableField(exist = false)
    private String storageArea;
    /** 资产类型(新分类) */
    @TableField(exist = false)
    private String assetTypeN;

    /** 资产类型(新分类) */
    @TableField(exist = false)
    private String faCode;

    /**
     * 创建年份
     */
    @TableField(exist = false)
    private String createYear;

    /**
     * 科室名称（用于模糊搜索）
     */
    @TableField(exist = false)
    private String orgName;

}
