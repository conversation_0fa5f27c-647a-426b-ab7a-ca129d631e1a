package com.jp.med.ams.modules.inventory.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产盘点表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Data
public class AmsIntrVo {

	/** ID */
	private Integer id;

	/** 操作人 */
	private String opter;
	private String opterName;

	/** 同步时间 */
	private String synctime;

	/** 总数量 */
	private Integer totlcnt;

}
