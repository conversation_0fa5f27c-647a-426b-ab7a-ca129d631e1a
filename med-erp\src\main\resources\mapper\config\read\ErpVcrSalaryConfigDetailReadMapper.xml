<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.config.mapper.read.ErpVcrSalaryConfigDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigDetailVo" id="vcrSalaryConfigDetailMap">
        <result property="id" column="id"/>
        <result property="salaryConfigId" column="salary_config_id"/>
        <result property="actigSubCode" column="actig_sub_code"/>
        <result property="actigSubName" column="actig_sub_name"/>
        <result property="actigSys" column="actig_sys"/>
        <result property="deptCode" column="dept_code"/>
        <result property="deptName" column="dept_name"/>
        <result property="relCoCode" column="rel_co_code"/>
        <result property="relCoName" column="rel_co_name"/>
        <result property="funSubCode" column="fun_sub_code"/>
        <result property="funSubName" column="fun_sub_name"/>
        <result property="econSubCode" column="econ_sub_code"/>
        <result property="econSubName" column="econ_sub_name"/>
        <result property="projCode" column="proj_code"/>
        <result property="projName" column="proj_name"/>
        <result property="cashFlowCode" column="cash_flow_code"/>
        <result property="cashFlowName" column="cash_flow_name"/>
        <result property="actigAmtType" column="actig_amt_type"/>
        <result property="abst" column="abst"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigDetailVo">
        select
            id as id,
            salary_config_id as salaryConfigId,
            actig_sub_code as actigSubCode,
            actig_sub_name as actigSubName,
            actig_sys as actigSys,
            dept_code as deptCode,
            dept_name as deptName,
            rel_co_code as relCoCode,
            rel_co_name as relCoName,
            fun_sub_code as funSubCode,
            fun_sub_name as funSubName,
            econ_sub_code as econSubCode,
            econ_sub_name as econSubName,
            proj_code as projCode,
            proj_name as projName,
            cash_flow_code as cashFlowCode,
            cash_flow_name as cashFlowName,
            actig_amt_type as actigAmtType,
            abst as abst
        from erp_vcr_salary_config_detail
        <where>
            <if test="salaryConfigId != null">
                and salary_config_id = #{salaryConfigId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

</mapper>
