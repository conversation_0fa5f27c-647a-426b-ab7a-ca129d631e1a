package com.jp.med.ams.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.config.dto.AmsTypeCfgDto;
import com.jp.med.ams.modules.config.service.read.AmsTypeCfgReadService;
import com.jp.med.ams.modules.config.service.write.AmsTypeCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 资产类型配表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 14:10:05
 */
@Api(value = "资产类型配表", tags = "资产类型配表")
@RestController
@RequestMapping("amsTypeCfg")
public class AmsTypeCfgController {

    @Autowired
    private AmsTypeCfgReadService amsTypeCfgReadService;

    @Autowired
    private AmsTypeCfgWriteService amsTypeCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产类型配表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsTypeCfgDto dto){
        return CommonResult.success(amsTypeCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产类型配表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsTypeCfgDto dto){
        amsTypeCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产类型配表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsTypeCfgDto dto){
        amsTypeCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产类型配表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsTypeCfgDto dto){
        amsTypeCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
