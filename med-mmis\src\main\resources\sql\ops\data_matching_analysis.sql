-- =================================================================================================
-- 数据匹配分析脚本：详细分析源表数据与目标表的匹配情况
-- 功能：分析物资、员工、科室等关键数据的匹配度，识别潜在问题
-- 作者：数据迁移分析
-- 创建时间：2025年1月
-- =================================================================================================

-- =================================================================================================
-- 第一部分：入库数据详细匹配分析 📦
-- =================================================================================================

-- 1.1 入库物资名称匹配详情
SELECT 
    '入库物资匹配详情' as analysis_type,
    t.name as source_material_name,
    COUNT(*) as record_count,
    CASE 
        WHEN a.name IS NOT NULL THEN '✅ 可匹配'
        ELSE '❌ 无匹配'
    END as match_status,
    a.mat_unique_code,
    a.code as item_num,
    a.ref_price
FROM mmis_temp_inport_storage_six t
LEFT JOIN mmis_aset_info_assist a ON t.name = a.name
WHERE t.name IS NOT NULL AND TRIM(t.name) != ''
GROUP BY t.name, a.name, a.mat_unique_code, a.code, a.ref_price
ORDER BY record_count DESC, match_status;

-- 1.2 入库供应商分布分析
SELECT 
    '入库供应商分析' as analysis_type,
    supplier_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT name) as material_types,
    SUM(CASE WHEN num ~ '^[0-9]+\.?[0-9]*$' THEN CAST(num AS numeric) ELSE 0 END) as total_quantity,
    AVG(CASE WHEN price ~ '^[0-9]+\.?[0-9]*$' THEN CAST(price AS numeric) ELSE NULL END) as avg_price
FROM mmis_temp_inport_storage_six
WHERE supplier_name IS NOT NULL AND TRIM(supplier_name) != ''
GROUP BY supplier_name
ORDER BY record_count DESC;

-- 1.3 入库数据按月份分布
SELECT 
    '入库数据月份分布' as analysis_type,
    remark as month_info,
    COUNT(*) as record_count,
    COUNT(DISTINCT supplier_name) as supplier_count,
    COUNT(DISTINCT name) as material_count
FROM mmis_temp_inport_storage_six
WHERE remark IS NOT NULL AND TRIM(remark) != ''
GROUP BY remark
ORDER BY 
    CASE remark
        WHEN '1月' THEN 1
        WHEN '2月' THEN 2
        WHEN '3月' THEN 3
        WHEN '4月' THEN 4
        WHEN '5月' THEN 5
        WHEN '12月' THEN 12
        ELSE 99
    END;

-- =================================================================================================
-- 第二部分：出库数据详细匹配分析 📤
-- =================================================================================================

-- 2.1 出库物资名称匹配详情
SELECT 
    '出库物资匹配详情' as analysis_type,
    t.name as source_material_name,
    COUNT(*) as record_count,
    CASE 
        WHEN a.name IS NOT NULL THEN '✅ 可匹配'
        ELSE '❌ 无匹配'
    END as match_status,
    a.mat_unique_code,
    a.code as item_num,
    a.ref_price
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN mmis_aset_info_assist a ON t.name = a.name
WHERE t.name IS NOT NULL AND TRIM(t.name) != ''
GROUP BY t.name, a.name, a.mat_unique_code, a.code, a.ref_price
ORDER BY record_count DESC, match_status;

-- 2.2 出库员工匹配详情
SELECT 
    '出库员工匹配详情' as analysis_type,
    t.out_emp as source_emp_code,
    COUNT(*) as record_count,
    CASE 
        WHEN e.emp_code IS NOT NULL THEN '✅ 可匹配'
        ELSE '❌ 无匹配'
    END as match_status,
    e.emp_name,
    e.org_name as emp_org
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_employee_info e ON t.out_emp = e.emp_code 
    AND e.hospital_id = 'zjxrmyy' AND e.is_deleted = 0
WHERE t.out_emp IS NOT NULL AND TRIM(t.out_emp) != ''
GROUP BY t.out_emp, e.emp_code, e.emp_name, e.org_name
ORDER BY record_count DESC, match_status;

-- 2.3 出库科室匹配详情
SELECT 
    '出库科室匹配详情' as analysis_type,
    t.out_org as source_org_name,
    COUNT(*) as record_count,
    CASE 
        WHEN o.org_name IS NOT NULL THEN '✅ 可匹配'
        ELSE '❌ 无匹配'
    END as match_status,
    o.org_id,
    o.org_name as matched_org_name
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_org o ON t.out_org = o.org_name 
    AND o.hospital_id = 'zjxrmyy' AND o.active_flag = '1'
WHERE t.out_org IS NOT NULL AND TRIM(t.out_org) != ''
GROUP BY t.out_org, o.org_name, o.org_id
ORDER BY record_count DESC, match_status;

-- 2.4 出库数据按月份分布
SELECT 
    '出库数据月份分布' as analysis_type,
    remark as month_info,
    COUNT(*) as record_count,
    COUNT(DISTINCT out_org) as org_count,
    COUNT(DISTINCT name) as material_count,
    COUNT(DISTINCT out_emp) as emp_count
FROM mmis_temp_xinxike_outbound_six
WHERE remark IS NOT NULL AND TRIM(remark) != ''
GROUP BY remark
ORDER BY 
    CASE remark
        WHEN '1月' THEN 1
        WHEN '2月' THEN 2
        WHEN '3月' THEN 3
        WHEN '4月' THEN 4
        WHEN '5月' THEN 5
        WHEN '12月' THEN 12
        ELSE 99
    END;

-- =================================================================================================
-- 第三部分：数据质量问题识别 ⚠️
-- =================================================================================================

-- 3.1 入库数据质量问题
SELECT 
    '入库数据质量问题' as issue_type,
    '物资名称为空' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_inport_storage_six
WHERE name IS NULL OR TRIM(name) = ''
UNION ALL
SELECT 
    '入库数据质量问题' as issue_type,
    '供应商为空' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_inport_storage_six
WHERE supplier_name IS NULL OR TRIM(supplier_name) = ''
UNION ALL
SELECT 
    '入库数据质量问题' as issue_type,
    '数量格式错误' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_inport_storage_six
WHERE num IS NULL OR NOT (num ~ '^[0-9]+\.?[0-9]*$') OR CAST(num AS numeric) <= 0
UNION ALL
SELECT 
    '入库数据质量问题' as issue_type,
    '价格格式错误' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_inport_storage_six
WHERE price IS NULL OR NOT (price ~ '^[0-9]+\.?[0-9]*$') OR CAST(price AS numeric) <= 0;

-- 3.2 出库数据质量问题
SELECT 
    '出库数据质量问题' as issue_type,
    '物资名称为空' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_xinxike_outbound_six
WHERE name IS NULL OR TRIM(name) = ''
UNION ALL
SELECT 
    '出库数据质量问题' as issue_type,
    '出库科室为空' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NULL OR TRIM(out_org) = ''
UNION ALL
SELECT 
    '出库数据质量问题' as issue_type,
    '员工编号为空' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_xinxike_outbound_six
WHERE out_emp IS NULL OR TRIM(out_emp) = ''
UNION ALL
SELECT 
    '出库数据质量问题' as issue_type,
    '数量格式错误' as issue_description,
    COUNT(*) as issue_count
FROM mmis_temp_xinxike_outbound_six
WHERE num IS NULL OR NOT (num ~ '^[0-9]+\.?[0-9]*$') OR CAST(num AS numeric) <= 0;

-- =================================================================================================
-- 第四部分：导入预估统计 📈
-- =================================================================================================

-- 4.1 入库导入预估
WITH storage_groups AS (
    SELECT DISTINCT
        supplier_name,
        remark,
        CASE 
            WHEN remark = '12月' THEN '2024-12-25'::date
            WHEN remark = '1月' THEN '2025-01-01'::date
            WHEN remark = '2月' THEN '2025-02-01'::date
            WHEN remark = '3月' THEN '2025-03-01'::date
            WHEN remark = '4月' THEN '2025-04-01'::date
            WHEN remark = '5月' THEN '2025-05-01'::date
            ELSE '2025-01-01'::date
        END as bill_date
    FROM mmis_temp_inport_storage_six
    WHERE supplier_name IS NOT NULL AND supplier_name != ''
      AND name IS NOT NULL AND name != ''
      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
)
SELECT 
    '入库导入预估' as estimate_type,
    '预计生成入库单数量' as metric,
    COUNT(*) as value,
    '张' as unit
FROM storage_groups
UNION ALL
SELECT 
    '入库导入预估' as estimate_type,
    '预计导入明细记录数' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_inport_storage_six
WHERE supplier_name IS NOT NULL AND supplier_name != ''
  AND name IS NOT NULL AND name != ''
  AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0;

-- 4.2 出库导入预估
WITH outbound_groups AS (
    SELECT DISTINCT
        out_org,
        remark,
        CASE 
            WHEN remark = '12月' THEN '2024-12-25'::date
            WHEN remark = '1月' THEN '2025-01-01'::date
            WHEN remark = '2月' THEN '2025-02-01'::date
            WHEN remark = '3月' THEN '2025-03-01'::date
            WHEN remark = '4月' THEN '2025-04-01'::date
            WHEN remark = '5月' THEN '2025-05-01'::date
            ELSE '2025-01-01'::date
        END as bill_date
    FROM mmis_temp_xinxike_outbound_six
    WHERE out_org IS NOT NULL AND out_org != ''
      AND name IS NOT NULL AND name != ''
      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
)
SELECT 
    '出库导入预估' as estimate_type,
    '预计生成出库申请单数量' as metric,
    COUNT(*) as value,
    '张' as unit
FROM outbound_groups
UNION ALL
SELECT 
    '出库导入预估' as estimate_type,
    '预计导入明细记录数' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL AND out_org != ''
  AND name IS NOT NULL AND name != ''
  AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0;
