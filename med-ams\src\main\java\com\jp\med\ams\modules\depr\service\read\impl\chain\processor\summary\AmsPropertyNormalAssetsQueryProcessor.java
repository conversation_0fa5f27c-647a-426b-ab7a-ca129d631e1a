package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.property.dto.AmsPropertyMonthlySnapshotDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyMonthlySnapshotReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 普通资产查询处理器
 * 负责查询普通资产数据（排除房屋维修资产）
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyNormalAssetsQueryProcessor
        extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Autowired
    private AmsPropertyMonthlySnapshotReadMapper amsPropertyMonthlySnapshotReadMapper;

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始查询普通资产");

        // 获取房屋维修资产的资产代码集合，用于排除
        Set<String> houseRepairFaCodes = context.getHouseRepairAssets().stream()
                .map(AmsPropertyVo::getFaCode)
                .collect(Collectors.toSet());

        // 构建普通资产查询条件
        AmsPropertyMonthlySnapshotDto normalAssetsQuery = new AmsPropertyMonthlySnapshotDto();
        normalAssetsQuery.setType("1");
        normalAssetsQuery.setIsCanc("0");
        normalAssetsQuery.setIsChk("1");
        normalAssetsQuery.setMonthlySnapshot(context.getYm());

        // 根据是否使用新类型设置资产类型条件
        if (context.isUseNewType()) {
            normalAssetsQuery.setAssetTypeN(context.getAssetTypeCode());
        } else {
            normalAssetsQuery.setAssetType(context.getAssetTypeCode());
        }

        // 排除房屋维修资产
        normalAssetsQuery.setExcludeFaCodes(new ArrayList<>(houseRepairFaCodes));

        // 执行查询
        List<AmsPropertyVo> normalAssets = amsPropertyMonthlySnapshotReadMapper.queryList(normalAssetsQuery);
        context.setNormalAssets(normalAssets);

        log.debug("普通资产查询完成，共查询到 {} 条记录", normalAssets.size());

        // 检查数据完整性
        if (context.getHouseRepairAssets().isEmpty() || normalAssets.isEmpty()) {
            log.warn("房屋维修资产或普通资产为空，可能影响后续处理");
        }
    }
}
