<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.vcrGen.mapper.write.ErpVcrDetailWriteMapper">

    <insert id="saveVcrApplyMsg">
        INSERT INTO
        erp_vcr_apply (
               organ_code,
               type,
               accounting_supervisor,
               certificate_date,
               record_person_id,
               record_person_name,
               attach_amount,
               certificate_abs,
               create_time,
               idpzh,
               organ_name,
               sup_type
        )
        VALUES
            (
             #{organCode,jdbcType=VARCHAR},
             #{type,jdbcType=VARCHAR},
             #{accountingSupervisor,jdbcType=VARCHAR},
             #{certificateDate,jdbcType=VARCHAR},
             #{recordPersonId,jdbcType=VARCHAR},
             #{recordPersonName,jdbcType=VARCHAR},
             #{attachAmount,jdbcType=INTEGER},
             #{certificateAbs,jdbcType=VARCHAR},
             #{createTime,jdbcType=VARCHAR},
             #{idpzh,jdbcType=VARCHAR},
             #{organName,jdbcType=VARCHAR},
             #{supType,jdbcType=VARCHAR}
            )
    </insert>

    <delete id="delVcrApplyMsg">
        DELETE
        FROM
            erp_vcr_apply
        WHERE
            idpzh IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <insert id="insertReimAsst">
    INSERT INTO ecs_reim_asst_detail(
        reim_detail_id,
        pay_type_code,
        pay_type_name,
        actig_sub_code,
        actig_sub_name,
        actig_sys,
        dept_code,
        dept_name,
        rel_co_code,
        rel_co_name,
        fun_sub_code,
        fun_sub_name,
        econ_sub_code,
        econ_sub_name,
        proj_code,
        proj_name,
        cash_flow_code,
        cash_flow_name,
        actig_amt_type,
        actig_amt,
        crter,
        create_time,
        hospital_id,
        reim_dept_code,
        reim_dept_name,
        asst_no,
        abst,
        sup_type,
        fund_type,
        fund_type_name,
        vpzh
        )
        VALUES
        (
            #{reimDetailId,jdbcType=INTEGER},
            #{payTypeCode,jdbcType=VARCHAR},
            #{payTypeName,jdbcType=VARCHAR},
            #{actigSubCode,jdbcType=VARCHAR},
            #{actigSubName,jdbcType=VARCHAR},
            #{actigSys,jdbcType=VARCHAR},
            #{deptCode,jdbcType=VARCHAR},
            #{deptName,jdbcType=VARCHAR},
            #{relCoCode,jdbcType=VARCHAR},
            #{relCoName,jdbcType=VARCHAR},
            #{funSubCode,jdbcType=VARCHAR},
            #{funSubName,jdbcType=VARCHAR},
            #{econSubCode,jdbcType=VARCHAR},
            #{econSubName,jdbcType=VARCHAR},
            #{projCode,jdbcType=VARCHAR},
            #{projName,jdbcType=VARCHAR},
            #{cashFlowCode,jdbcType=VARCHAR},
            #{cashFlowName,jdbcType=VARCHAR},
            #{actigAmtType,jdbcType=VARCHAR},
            #{actigAmt,jdbcType=VARCHAR},
            #{crter,jdbcType=VARCHAR},
            #{createTime,jdbcType=VARCHAR},
            #{hospitalId,jdbcType=VARCHAR},
            #{reimDeptCode,jdbcType=VARCHAR},
            #{reimDeptName,jdbcType=VARCHAR},
            #{asstNo,jdbcType=INTEGER},
            #{abst,jdbcType=VARCHAR},
            #{supType,jdbcType=VARCHAR},
            #{fundType,jdbcType=VARCHAR},
            #{fundTypeName,jdbcType=VARCHAR},
            #{vpzh,jdbcType=VARCHAR}
        )
    </insert>

    <delete id="deleteVcrReimAsst">
        DELETE
        FROM
            ecs_reim_asst_detail A
        WHERE
            A.sup_type = #{supType,jdbcType=VARCHAR}
            AND A.reim_detail_id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <delete id="deleteVcrReimAsstById">
        DELETE
        FROM
            ecs_reim_asst_detail A
        WHERE
            A.id = #{id,jdbcType=INTEGER}
    </delete>

    <update id="updateVcrAsstAmt">
        UPDATE ecs_reim_asst_detail
        <set>
            <if test="actigAmt !=null">
                actig_amt = #{actigAmt,jdbcType=DOUBLE},
            </if>
            <if test="certificateAbs !=null and certificateAbs!=''">
                abst = #{certificateAbs,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE
            "id" = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateDeprTask">
        UPDATE ecs_reim_depr_task
        set
            reim_flag = '1'
        WHERE
            "id" = #{id,jdbcType=INTEGER}
    </update>
    
    <delete id="deleteAsstByVpzh">
        delete from ecs_reim_asst_detail 
        where vpzh in 
        <foreach collection="vpzhs" item="vpzh" open="(" separator="," close=")">
            #{vpzh,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateVcrPZstatus">
        update ecs_reim_detail set has_pz = #{status,jdbcType=VARCHAR} where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
