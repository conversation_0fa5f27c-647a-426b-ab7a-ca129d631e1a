<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.config.mapper.read.ErpReimSalaryTaskDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskDetailVo" id="reimSalaryTaskDetailMap">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="orgId" column="org_id"/>
        <result property="reimType" column="reim_type"/>
        <result property="reimAmt" column="reim_amt"/>
        <result property="reimDesc" column="reim_desc"/>
        <result property="type" column="type"/>
        <result property="empCode" column="emp_code"/>
        <result property="reimName" column="reim_name"/>
        <result property="empType" column="emp_type"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskDetailVo">
        select
            id as id,
            task_id as taskId,
            org_id as orgId,
            reim_type as reimType,
            reim_amt as reimAmt,
            reim_desc as reimDesc,
            type as type,
            emp_code as empCode,
            reim_name as reimName,
            emp_type as empType
        from ecs_reim_salary_task_detail
    </select>

</mapper>
