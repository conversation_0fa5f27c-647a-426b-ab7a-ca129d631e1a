package com.jp.med.ams.modules.changes.strategy;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 变更记录处理器工厂
 * 使用工厂模式管理不同的处理器
 */
@Component
public class ChangeRecordProcessorFactory {
    
    private final List<ChangeRecordProcessor> processors;
    
    @Autowired
    public ChangeRecordProcessorFactory(List<ChangeRecordProcessor> processors) {
        this.processors = processors;
    }
    
    /**
     * 根据DTO获取合适的处理器
     * @param dto 变更记录DTO
     * @return 处理器
     */
    public ChangeRecordProcessor getProcessor(AmsChgRcdDto dto) {
        return processors.stream()
                .filter(processor -> processor.supports(dto))
                .findFirst()
                .orElseThrow(() -> new AppException("未找到合适的变更记录处理器"));
    }
}
