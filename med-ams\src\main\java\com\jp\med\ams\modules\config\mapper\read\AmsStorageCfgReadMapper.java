package com.jp.med.ams.modules.config.mapper.read;

import com.jp.med.ams.modules.config.dto.AmsStorageCfgDto;
import com.jp.med.ams.modules.config.vo.AmsStorageCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 存放地点配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-12 10:23:40
 */
@Mapper
public interface AmsStorageCfgReadMapper extends BaseMapper<AmsStorageCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsStorageCfgVo> queryList(AmsStorageCfgDto dto);
}
