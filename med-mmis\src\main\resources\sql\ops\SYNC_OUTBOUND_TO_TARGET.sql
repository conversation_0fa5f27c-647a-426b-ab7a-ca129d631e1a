-- =================================================================================================
-- 出库数据同步脚本：将修复后的源表数据同步到目标表
-- 功能：将mmis_temp_xinxike_outbound_six的修复数据同步到mmis_outbound_apply
-- 执行时机：在FIXOUTBOUND.sql执行完成后
-- =================================================================================================

BEGIN;

-- =================================================================================================
-- 第一步：检查源表修复状态 🔍
-- =================================================================================================

-- 检查源表修复完成情况
SELECT 
    '源表修复状态检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_org IS NOT NULL AND TRIM(out_org) != '' THEN 1 END) as has_org_name,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_org_id,
    ROUND(
        COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
    ) as repair_success_rate
FROM mmis_temp_xinxike_outbound_six;

-- 显示源表修复后的数据样例
SELECT 
    '源表修复后数据样例' as sample_type,
    out_org,
    out_target_org_id,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six
GROUP BY out_org, out_target_org_id
ORDER BY record_count DESC
LIMIT 5;

-- =================================================================================================
-- 第二步：检查目标表当前状态 📊
-- =================================================================================================

-- 检查目标表中的出库申请记录
SELECT 
    '目标表当前状态' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_taget_org IS NOT NULL AND TRIM(out_taget_org) != '' THEN 1 END) as has_target_org_name,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_target_org_id
FROM mmis_outbound_apply
WHERE hospital_id = 'zjxrmyy';

-- =================================================================================================
-- 第三步：数据同步策略 🔄
-- =================================================================================================

-- 方案1：更新现有记录的组织字段
-- 假设通过docment_num或其他关键字段关联

-- 方案2：直接更新所有相关记录
-- 如果目标表中的记录是通过导入脚本创建的，可以批量更新

-- 检查是否有可以关联的字段
SELECT 
    '关联字段检查' as check_type,
    'docment_num' as field_name,
    COUNT(DISTINCT docment_num) as unique_values_in_target
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' AND docment_num IS NOT NULL;

-- =================================================================================================
-- 第四步：执行数据同步 🚀
-- =================================================================================================

-- 方案A：如果目标表记录是最近导入的，直接更新组织字段
UPDATE mmis_outbound_apply 
SET 
    out_taget_org = (
        SELECT DISTINCT t.out_org 
        FROM mmis_temp_xinxike_outbound_six t 
        WHERE t.out_org IS NOT NULL 
        LIMIT 1
    ),
    out_target_org_id = (
        SELECT DISTINCT t.out_target_org_id 
        FROM mmis_temp_xinxike_outbound_six t 
        WHERE t.out_target_org_id IS NOT NULL 
        LIMIT 1
    )
WHERE hospital_id = 'zjxrmyy' 
  AND (out_taget_org IS NULL OR out_target_org_id IS NULL)
  AND bill_date = '2025-07-16';  -- 只更新今天导入的数据

-- 方案B：通过更精确的匹配更新（如果有多个不同的组织）
-- 这里需要根据实际业务逻辑调整匹配条件

-- 如果源表中有多个不同的组织，需要更复杂的匹配逻辑
-- 可以通过remark字段或其他业务字段进行匹配

-- =================================================================================================
-- 第五步：验证同步结果 ✅
-- =================================================================================================

-- 检查同步后的结果
SELECT 
    '同步结果验证' as result_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_taget_org IS NOT NULL AND TRIM(out_taget_org) != '' THEN 1 END) as has_target_org_name,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_target_org_id,
    ROUND(
        COUNT(CASE WHEN out_taget_org IS NOT NULL AND out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
    ) as sync_success_rate
FROM mmis_outbound_apply
WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16';

-- 显示同步后的数据样例
SELECT 
    '同步后数据样例' as sample_type,
    docment_num,
    out_taget_org,
    out_target_org_id,
    appy_org_id,
    bill_date
FROM mmis_outbound_apply
WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16'
ORDER BY docment_num
LIMIT 5;

-- 验证组织ID的正确性
SELECT 
    '组织ID验证' as validation_type,
    oa.out_taget_org,
    oa.out_target_org_id,
    ho.org_name as verified_org_name,
    COUNT(*) as record_count,
    CASE 
        WHEN ho.org_id IS NOT NULL THEN '✅ 组织ID正确'
        ELSE '❌ 组织ID无效'
    END as validation_status
FROM mmis_outbound_apply oa
LEFT JOIN hrm_org ho ON oa.out_target_org_id = ho.org_id 
    AND ho.hospital_id = 'zjxrmyy' 
    AND ho.active_flag = '1'
WHERE oa.hospital_id = 'zjxrmyy' 
  AND oa.bill_date = '2025-07-16'
  AND oa.out_taget_org IS NOT NULL
GROUP BY oa.out_taget_org, oa.out_target_org_id, ho.org_name
ORDER BY record_count DESC;

-- =================================================================================================
-- 第六步：最终总结 📋
-- =================================================================================================

SELECT 
    '🎯 数据同步总结' as summary_type,
    '源表到目标表同步完成' as operation,
    (SELECT COUNT(*) FROM mmis_temp_xinxike_outbound_six) as source_records,
    (SELECT COUNT(*) FROM mmis_outbound_apply WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16') as target_records,
    (SELECT COUNT(*) FROM mmis_outbound_apply WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16' AND out_taget_org IS NOT NULL) as synced_records,
    CASE 
        WHEN (SELECT COUNT(*) FROM mmis_outbound_apply WHERE hospital_id = 'zjxrmyy' AND bill_date = '2025-07-16' AND out_taget_org IS NOT NULL) > 0
        THEN '✅ 同步成功'
        ELSE '❌ 同步失败，需要检查'
    END as sync_status;

-- 提交事务
COMMIT;
