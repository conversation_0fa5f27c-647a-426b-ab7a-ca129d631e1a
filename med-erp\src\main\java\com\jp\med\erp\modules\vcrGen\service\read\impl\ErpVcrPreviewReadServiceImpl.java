package com.jp.med.erp.modules.vcrGen.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrPreviewReadMapper;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrPreviewDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpVcrPreviewVo;
import com.jp.med.erp.modules.vcrGen.service.read.ErpVcrPreviewReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class ErpVcrPreviewReadServiceImpl extends ServiceImpl<ErpVcrPreviewReadMapper, ErpVcrPreviewDto> implements ErpVcrPreviewReadService {

    @Autowired
    private ErpVcrPreviewReadMapper erpVcrPreviewReadMapper;

    @Override
    public List<ErpVcrPreviewVo> queryList(ErpVcrPreviewDto dto) {
        return erpVcrPreviewReadMapper.queryList(dto);
    }

    @Override
    public List<ErpVcrPreviewVo> queryPageList(ErpVcrPreviewDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpVcrPreviewReadMapper.queryList(dto);
    }

}
