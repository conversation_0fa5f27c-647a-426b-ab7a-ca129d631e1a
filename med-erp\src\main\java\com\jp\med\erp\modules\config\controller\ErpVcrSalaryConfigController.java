package com.jp.med.erp.modules.config.controller;

import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDto;
import com.jp.med.erp.modules.config.service.read.ErpVcrSalaryConfigReadService;
import com.jp.med.erp.modules.config.service.write.ErpVcrSalaryConfigWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 工资凭证配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 00:52:39
 */
@Api(value = "工资凭证配置表", tags = "工资凭证配置表")
@RestController
@RequestMapping("erpVcrSalaryConfig")
public class ErpVcrSalaryConfigController {

    @Autowired
    private ErpVcrSalaryConfigReadService erpVcrSalaryConfigReadService;

    @Autowired
    private ErpVcrSalaryConfigWriteService erpVcrSalaryConfigWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询工资凭证配置表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody ErpVcrSalaryConfigDto dto){
        return CommonResult.paging(erpVcrSalaryConfigReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询工资凭证配置表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody ErpVcrSalaryConfigDto dto){
        return CommonResult.success(erpVcrSalaryConfigReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增工资凭证配置表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody ErpVcrSalaryConfigDto dto){
        erpVcrSalaryConfigWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改工资凭证配置表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody ErpVcrSalaryConfigDto dto){
        erpVcrSalaryConfigWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除工资凭证配置表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody ErpVcrSalaryConfigDto dto){
        erpVcrSalaryConfigWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增财务核算-工资凭证科目映射配置明细")
    @PostMapping("/saveDetails")
    public CommonResult<?> saveDetails(@RequestBody ErpVcrSalaryConfigDetailDto dto){
        erpVcrSalaryConfigWriteService.saveDetails(dto);
        return CommonResult.success();
    }

    /**
     * 生成个人扣款-临时扣款配置
     * @param dto
     * @return
     */
    @ApiOperation("生成个人扣款-临时扣款配置")
    @PostMapping("/toCfgTempReduce")
    public CommonResult<?> toCfgTempReduce(@RequestBody ErpVcrSalaryConfigDto dto) {
        erpVcrSalaryConfigWriteService.toCfgTempReduce(dto);
        return CommonResult.success();
    }

    /**
     * 修改个人扣款-临时扣款配置
     * @param dto
     * @return
     */
    @ApiOperation("修改个人扣款-临时扣款配置")
    @PostMapping("/toUpdTempReduce")
    public CommonResult<?> toUpdTempReduce(@RequestBody ErpVcrSalaryConfigDto dto) {
        erpVcrSalaryConfigWriteService.toUpdTempReduce(dto);
        return CommonResult.success();
    }
}
