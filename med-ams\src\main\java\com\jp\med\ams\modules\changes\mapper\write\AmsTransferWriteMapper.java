package com.jp.med.ams.modules.changes.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.changes.dto.AmsTransferDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 资产转移
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
@Mapper
public interface AmsTransferWriteMapper extends BaseMapper<AmsTransferDto> {

    /**
     * 更改资产信息
     *
     * @param dto
     */
    void updateAssetInfo(String bchno);

    /**
     * 更新资产转移业务状态
     *
     * @param bchno
     * @param prosstas
     */
    void updateApplyProsstas(@Param("bchno") String bchno, @Param("prosstas") String prosstas);
    /**
     * 新增资产划拨属性关联
     * @param amsAllocDto
     * @return
     */
    int insertTransferPropertyRef(AmsTransferDto dto);




}
