package com.jp.med.ams.modules.inventory.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrDetailReadMapper;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.vo.AmsIntrDetailVo;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrDetailReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsIntrDetailReadServiceImpl extends ServiceImpl<AmsIntrDetailReadMapper, AmsIntrDetailDto> implements AmsIntrDetailReadService {

    @Autowired
    private AmsIntrDetailReadMapper amsIntrDetailReadMapper;

    @Override
    public List<AmsIntrDetailVo> queryList(AmsIntrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return amsIntrDetailReadMapper.queryList(dto);
    }

    @Override
    public List<AmsIntrDetailVo> queryToDoDetail(AmsIntrTodoDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return amsIntrDetailReadMapper.queryToDoDetail(dto);
    }

    @Override
    public Object startIntr(AmsIntrTodoDto dto) {
        return null;
    }

}
