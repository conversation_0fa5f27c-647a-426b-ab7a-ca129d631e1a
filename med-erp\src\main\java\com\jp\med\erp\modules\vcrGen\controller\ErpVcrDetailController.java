package com.jp.med.erp.modules.vcrGen.controller;

import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrDetailDto;
import com.jp.med.erp.modules.vcrGen.entity.Certificate;
import com.jp.med.erp.modules.vcrGen.service.read.ErpVcrDetailReadService;
import com.jp.med.erp.modules.vcrGen.service.write.ErpVcrDetailWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 凭证信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Api(value = "凭证信息", tags = "凭证信息")
@RestController
@RequestMapping("erpVcrDetail")
public class ErpVcrDetailController {

    @Autowired
    private ErpVcrDetailReadService erpVcrDetailReadService;

    @Autowired
    private ErpVcrDetailWriteService erpVcrDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询凭证信息")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.paging(erpVcrDetailReadService.queryList(dto));
    }


    /**
     * 删除
     */
    @ApiOperation("删除凭证信息")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody ErpVcrDetailDto dto){
        erpVcrDetailWriteService.deleteVcr(dto);
        return CommonResult.success();
    }

    /**
     * 可生成凭证的报销项信息
     * @param dto
     * @return
     */
    @ApiOperation("查询待生成凭证信息")
    @PostMapping("/vcrGenList")
    public CommonResult<?> queryVcrGenList(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.paging(erpVcrDetailReadService.queryVcrGenList(dto));
    }

    /**
     * 药品可生成凭证的报销项信息
     * @param dto
     * @return
     */
    @ApiOperation("查询待生成凭证信息")
    @PostMapping("/drugToVcrList")
    public CommonResult<?> drugToVcrList(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.paging(erpVcrDetailReadService.drugToVcrList(dto));
    }

    /**
     * 工资可生成凭证的报销项信息
     * @param dto
     * @return
     */
    @ApiOperation("查询待生成凭证信息")
    @PostMapping("/salaryToVcrList")
    public CommonResult<?> salaryToVcrList(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.paging(erpVcrDetailReadService.salaryToVcrList(dto));
    }

    /**
     * 折旧可生成凭证的报销项信息
     * @param dto
     * @return
     */
    @ApiOperation("查询待生成凭证信息")
    @PostMapping("/deprToVcrList")
    public CommonResult<?> deprToVcrList(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.paging(erpVcrDetailReadService.deprToVcrList(dto));
    }

    /**
     * 凭证生成
     * @param dto
     * @return
     */
    @ApiOperation("凭证生成")
    @PostMapping("/generateVcr")
    public CommonResult<?> generateVcr(@RequestBody Certificate dto){
        erpVcrDetailWriteService.generateVcr(dto);
        return CommonResult.success();
    }

    /**
     * 保存凭证辅助项目
     * @param dto
     * @return
     */
    @ApiOperation("保存凭证辅助项目")
    @PostMapping("/saveVcrAssts")
    public CommonResult<?> saveErpAssts(@RequestBody Certificate dto){
        return CommonResult.success(erpVcrDetailWriteService.saveVcrAssts(dto));
    }

    /**
     * 保存凭证辅助项目带文件
     * @param dto
     * @return
     */
    @ApiOperation("保存凭证辅助项目")
    @PostMapping("/saveErpAsstsWithFile")
    public CommonResult<?> saveErpAsstsWithFile(Certificate dto){
        return CommonResult.success(erpVcrDetailWriteService.saveErpAsstsWithFile(dto));
    }

    /**
     * 保存工资凭证辅助项目
     * @param dto
     * @return
     */
    @ApiOperation("保存凭证辅助项目")
    @PostMapping("/saveSalaryVcrAssts")
    public CommonResult<?> saveSalaryVcrAssts(@RequestBody ErpReimSalaryTaskDto dto){
        erpVcrDetailWriteService.saveSalaryVcrAssts(dto);
        return CommonResult.success();
    }

    /**
     * 保存折旧凭证辅助项目
     * @param dto
     * @return
     */
    @ApiOperation("保存折旧凭证辅助项目")
    @PostMapping("/saveDeprVcrAssts")
    public CommonResult<?> saveDeprVcrAssts(@RequestBody EcsReimDeprTaskDto dto){
        erpVcrDetailWriteService.saveDeprVcrAssts(dto);
        return CommonResult.success();
    }

    /**
     * 保存药品凭证辅助项目
     * @param dto
     * @return
     */
    @ApiOperation("保存药品凭证辅助项目")
    @PostMapping("/saveDrugVcrAssts")
    public CommonResult<?> saveDrugVcrAssts(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.success(erpVcrDetailWriteService.saveDrugVcrAssts(dto));
    }

    /**
     * 删除药品报销已生成的vpzh相关数据
     * @param dto
     * @return
     */
    @ApiOperation("保存药品凭证辅助项目")
    @PostMapping("/delDrugVpzhMsg")
    public CommonResult<?> delDrugVpzhMsg(@RequestBody ErpVcrDetailDto dto){
        erpVcrDetailWriteService.delDrugVpzhMsg(dto);
        return CommonResult.success();
    }

    /**
     * 查询当前报销的vpzh
     * @param dto
     * @return
     */
    @ApiOperation("保存药品凭证辅助项目")
    @PostMapping("/queryDrugVpzh")
    public CommonResult<?> queryDrugVpzh(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.success(erpVcrDetailReadService.queryDrugVpzh(dto));
    }

    /**
     * 修改凭证辅助项目  (新增、修改)
     * @param dto
     * @return
     */
    @ApiOperation("保存凭证辅助项目")
    @PostMapping("/updVcrAssts")
    public CommonResult<?> updVcrAssts(@RequestBody Certificate dto){
        erpVcrDetailWriteService.updVcrAssts(dto);
        return CommonResult.success();
    }

    /**
     * 删除凭证辅助项目  (新增、修改)
     * @param dto
     * @return
     */
    @ApiOperation("保存凭证辅助项目")
    @PostMapping("/delVcrAssts")
    public CommonResult<?> delVcrAssts(@RequestBody Certificate dto){
        erpVcrDetailWriteService.delVcrAssts(dto);
        return CommonResult.success();
    }

    /**
     * 更新凭证辅助项目金额
     * @param dto
     * @return
     */
    @ApiOperation("更新凭证辅助项目金额")
    @PostMapping("/updateVcrAsstAmt")
    public CommonResult<?> updateVcrAsstAmt(@RequestBody Certificate dto){
        erpVcrDetailWriteService.updateVcrAsstAmt(dto);
        return CommonResult.success();
    }

    /**
     * 查询待生成凭证辅助项
     * @param dto
     * @return
     */
    @ApiOperation("查询待生成凭证辅助项")
    @PostMapping("/queryAsst")
    public CommonResult<?> queryAsst(@RequestBody Certificate dto){
        return CommonResult.success(erpVcrDetailReadService.queryAsst(dto));
    }

    /**
     * 查询凭证
     * @param dto
     * @return
     */
    @ApiOperation("查询凭证信息")
    @PostMapping("/queryVcr")
    public CommonResult<?> queryVcr(@RequestBody ErpVcrDetailDto dto){
        return CommonResult.success(erpVcrDetailReadService.queryVcr(dto));
    }

    /**
     * 查询申请信息
     * @param dto
     * @return
     */
    @ApiOperation("查询申请信息")
    @PostMapping("/queryApprInfo")
    public CommonResult<?> queryApprInfo(@RequestBody ErpVcrDetailDto dto) {
        return CommonResult.success(erpVcrDetailReadService.queryApprInfo(dto));
    }

    /**
     * 查询报销信息 by Ids
     * @param dto
     * @return
     */
    @ApiOperation("查询报销信息")
    @PostMapping("/queryReimInfo")
    public CommonResult<?> queryReimInfo(@RequestBody ErpVcrDetailDto dto) {
        return CommonResult.success(erpVcrDetailReadService.queryReimInfo(dto));
    }

    /**
     * 查询个人扣款未配置的用户
     * @param dto
     * @return
     */
    @ApiOperation("查询个人扣款未配置的用户")
    @PostMapping("/queryToCfgTempReduce")
    public CommonResult<?> queryToConfigSalary(@RequestBody ErpVcrDetailDto dto) {
        return CommonResult.success(erpVcrDetailReadService.queryToConfigSalary(dto));
    }

    /**
     * 修改凭证的凭证号
     * @param dto
     * @return
     */
    @ApiOperation("修改凭证的凭证号")
    @PostMapping("/updErpVcrDetailPzh")
    public CommonResult<?> updErpVcrDetailPzh(@RequestBody ErpVcrDetailDto dto) {
        erpVcrDetailWriteService.updErpVcrDetailPzh(dto);
        return CommonResult.success();
    }
}
