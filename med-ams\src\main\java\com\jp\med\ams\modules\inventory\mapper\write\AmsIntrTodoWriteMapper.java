package com.jp.med.ams.modules.inventory.mapper.write;

import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 待盘点资产表
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 13:56:27
 */
@Mapper
public interface AmsIntrTodoWriteMapper extends BaseMapper<AmsIntrTodoDto> {

    int deleteByTaskId(AmsIntrTodoDto dto);

}
