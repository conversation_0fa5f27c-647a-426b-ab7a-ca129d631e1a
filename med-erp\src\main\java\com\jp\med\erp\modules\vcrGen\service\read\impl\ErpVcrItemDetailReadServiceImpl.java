package com.jp.med.erp.modules.vcrGen.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrItemDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrItemDetailDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpVcrItemDetailVo;
import com.jp.med.erp.modules.vcrGen.service.read.ErpVcrItemDetailReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class ErpVcrItemDetailReadServiceImpl extends ServiceImpl<ErpVcrItemDetailReadMapper, ErpVcrItemDetailDto> implements ErpVcrItemDetailReadService {

    @Autowired
    private ErpVcrItemDetailReadMapper erpVcrItemDetailReadMapper;

    @Override
    public List<ErpVcrItemDetailVo> queryList(ErpVcrItemDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpVcrItemDetailReadMapper.queryList(dto);
    }

}
