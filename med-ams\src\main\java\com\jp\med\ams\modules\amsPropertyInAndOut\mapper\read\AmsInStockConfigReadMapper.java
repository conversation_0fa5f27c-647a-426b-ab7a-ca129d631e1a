package com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read;

import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInStockConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInStockConfigVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Mapper
public interface AmsInStockConfigReadMapper extends BaseMapper<AmsInStockConfigDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsInStockConfigVo> queryList(AmsInStockConfigDto dto);
}
