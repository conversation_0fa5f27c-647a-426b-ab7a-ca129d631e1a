package com.jp.med.ams.modules.dashboard.controller;

import com.jp.med.ams.modules.dashboard.dto.AmsDashboardFilterDto;
import com.jp.med.ams.modules.dashboard.service.AmsDashboardService;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardChartVo;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardMetricsVo;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 资产盘点仪表盘控制器
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Api(value = "资产盘点仪表盘", tags = "资产盘点仪表盘")
@RestController
@RequestMapping("amsDashboard")
public class AmsDashboardController {

    @Autowired
    private AmsDashboardService amsDashboardService;

    /**
     * 获取仪表盘指标数据
     */
    @ApiOperation("获取仪表盘指标数据")
    @PostMapping("/metrics")
    public CommonResult<AmsDashboardMetricsVo> getMetrics(@RequestBody AmsDashboardFilterDto filterDto) {
        AmsDashboardMetricsVo metrics = amsDashboardService.getMetrics(filterDto);
        return CommonResult.success(metrics);
    }

    /**
     * 获取仪表盘图表数据
     */
    @ApiOperation("获取仪表盘图表数据")
    @PostMapping("/charts")
    public CommonResult<AmsDashboardChartVo> getCharts(@RequestBody AmsDashboardFilterDto filterDto) {
        AmsDashboardChartVo charts = amsDashboardService.getCharts(filterDto);
        return CommonResult.success(charts);
    }

    /**
     * 获取仪表盘表格数据
     */
    @ApiOperation("获取仪表盘表格数据")
    @PostMapping("/table")
    public CommonResult<?> getTableData(@RequestBody AmsDashboardFilterDto filterDto) {
        return CommonResult.paging(amsDashboardService.getTableData(filterDto));
    }

    /**
     * 获取完整的仪表盘数据
     */
    @ApiOperation("获取完整的仪表盘数据")
    @PostMapping("/all")
    public CommonResult<Map<String, Object>> getAllDashboardData(@RequestBody AmsDashboardFilterDto filterDto) {
        Map<String, Object> dashboardData = amsDashboardService.getAllDashboardData(filterDto);
        return CommonResult.success(dashboardData);
    }

    /**
     * 导出仪表盘数据
     */
    @ApiOperation("导出仪表盘数据")
    @PostMapping("/export")
    public void exportDashboardData(@RequestBody AmsDashboardFilterDto filterDto, HttpServletResponse response) {
        amsDashboardService.exportDashboardData(filterDto, response);
    }

    /**
     * 获取部门选项
     */
    @ApiOperation("获取部门选项")
    @GetMapping("/departments")
    public CommonResult<List<Map<String, Object>>> getDepartmentOptions() {
        List<Map<String, Object>> departments = amsDashboardService.getDepartmentOptions();
        return CommonResult.success(departments);
    }

    /**
     * 获取资产类型选项
     */
    @ApiOperation("获取资产类型选项")
    @GetMapping("/assetTypes")
    public CommonResult<List<Map<String, Object>>> getAssetTypeOptions() {
        List<Map<String, Object>> assetTypes = amsDashboardService.getAssetTypeOptions();
        return CommonResult.success(assetTypes);
    }
} 