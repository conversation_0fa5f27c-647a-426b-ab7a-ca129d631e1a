package com.jp.med.ams.modules.dashboard.vo;

import lombok.Data;

/**
 * 仪表盘表格数据VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class AmsDashboardTableVo {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 盘点任务名称
     */
    private String taskName;

    /**
     * 科室
     */
    private String department;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 资产总数
     */
    private Integer totalAssets;

    /**
     * 已盘点数
     */
    private Integer inventoriedAssets;

    /**
     * 未盘点数
     */
    private Integer uninventoriedAssets;

    /**
     * 完成率
     */
    private Double completionRate;

    /**
     * 盘盈数
     */
    private Integer profitCount;

    /**
     * 盘亏数
     */
    private Integer lossCount;

    /**
     * 盘点时间
     */
    private String inventoryDate;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 状态 (0:盘点中,1:盘点完成)
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 备注
     */
    private String remark;
} 