package com.jp.med.ams.modules.it.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.ams.modules.it.vo.AmsItChkDetailVo;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 信息科库房耗材申请主表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
@TableName("ams_it_invt_apply")
public class AmsItInvtApplyDto extends CommonQueryDto {

    /**
     * $column.comments
     */
    @TableId("id")
    private Integer id;

    /**
     * 申请科室
     */
    @TableField("apply_org")
    private String applyOrg;

    /**
     * 申请理由
     */
    @TableField("apply_remark")
    private String applyRemark;

    /**
     * 审核备注
     */
    @TableField("chk_remark")
    private String chkRemark;

    /**
     * 审核状态(0:待审核,1:审核通过,2:审核拒绝,4:撤销申请)
     */
    @TableField("status")
    private String status;

    /**
     * 审核人
     */
    @TableField("chker")
    private String chker;

    /**
     * 审核时间
     */
    @TableField("chk_time")
    private String chkTime;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 更新人
     */
    @TableField("updtr")
    private String updtr;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private String updateTime;

    /**
     * 删除人
     */
    @TableField("delter")
    private String delter;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private String deleteTime;

    /**
     * 有效标志(1:删除)
     */
    @TableField("active_flag")
    private String activeFlag;

    /**
     * 医疗机构id
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 耗材出库状态
     */
    @TableField("out_status")
    private String outStatus;

    /**
     * 耗材出库人
     */
    @TableField("out_emp")
    private String outEmp;

    /**
     * 耗材出库时间
     */
    @TableField("out_time")
    private String outTime;

    /**
     * 出库备注
     */
    @TableField("out_remark")
    private String outRemark;

    /**
     * 申请详情
     */
    @TableField(exist = false)
    private List<AmsItApplyDetailDto> applyDetail;

    /**
     * 出库详情
     */
    @TableField(exist = false)
    private List<AmsItChkDetailDto> outDetail;

    /**
     * 申请时间范围-起时间
     */
    @TableField(exist = false)
    private String crStartTime;

    /**
     * 申请时间范围-结束时间
     */
    @TableField(exist = false)
    private String crEndTime;

    /**
     * 审核时间范围-起时间
     */
    @TableField(exist = false)
    private String checkStartTime;

    /**
     * 审核时间范围-结束时间
     */
    @TableField(exist = false)
    private String checkEndTime;

    /**
     * 出库时间范围-起时间
     */
    @TableField(exist = false)
    private String outStartTime;

    /**
     * 出库时间范围-结束时间
     */
    @TableField(exist = false)
    private String outEndTime;
}
