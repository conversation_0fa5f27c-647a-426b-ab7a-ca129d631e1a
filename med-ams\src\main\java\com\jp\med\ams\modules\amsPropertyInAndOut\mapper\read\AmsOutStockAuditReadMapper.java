package com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-27 20:15:50
 */
@Mapper
public interface AmsOutStockAuditReadMapper extends BaseMapper<AmsOutStockAuditDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsOutStockAuditDto> queryList(AmsOutStockAuditDto dto);

    /**
     * 校验资产是是否正在出库
     */
    boolean validateOutApply(AmsOutStockAuditDto dto);

    /**
     *
     * @param dto
     */
    int queryOutCount(AmsOutStockAuditDto dto);

    int queryAuditCount(AmsOutStockAuditDto dto);
}
