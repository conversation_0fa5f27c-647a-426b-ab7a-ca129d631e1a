package com.jp.med.ams.modules.depr.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDeprVo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyMonthDeprVo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyValueAnalysisVo;
import com.jp.med.common.dto.erp.ErpPropertyDeprDto;
import com.jp.med.common.vo.ErpAmsPropertyDepr2Vo;

import java.util.List;

/**
 * 资产折旧表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 20:21:29
 */
public interface AmsPropertyDeprReadService extends IService<AmsPropertyDeprDto> {

    /**
     * 查询列表
     * 
     * @param dto
     * @return
     */
    List<AmsPropertyDeprVo> queryList(AmsPropertyDeprDto dto);

    /**
     * 查询资产折旧汇总
     * 
     * @param dto
     * @return
     */
//    List<AmsPropertyDeprVo> queryDeprSummary(AmsPropertyDeprDto dto);

    List<AmsPropertyDepr2Vo> queryDeprSummary2(AmsPropertyDeprDto dto);


    List<ErpAmsPropertyDepr2Vo> erpQueryDeprSummary2(ErpPropertyDeprDto dto);

    List<AmsPropertyMonthDeprVo> queryDeprMonth(AmsPropertyDeprDto dto);

    /**
     * 查询资产价值构成分析表
     *
     * @param dto 查询条件（资产类别、使用管理科室）
     * @return 资产价值构成分析数据列表
     */
    List<AmsPropertyValueAnalysisVo> queryValueAnalysis(AmsPropertyDeprDto dto);
}
