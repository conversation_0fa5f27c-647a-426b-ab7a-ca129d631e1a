package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItInvtApplyDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtApplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 信息科库房耗材申请主表
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItInvtApplyReadMapper extends BaseMapper<AmsItInvtApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItInvtApplyVo> queryList(AmsItInvtApplyDto dto);

    List<AmsItInvtApplyVo> adminQueryList(AmsItInvtApplyDto dto);
}
