package com.jp.med.erp.modules.vcrGen.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.erp.modules.vcrGen.mapper.write.ErpVcrItemDetailWriteMapper;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrItemDetailDto;
import com.jp.med.erp.modules.vcrGen.service.write.ErpVcrItemDetailWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 凭证信息明细
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Service
@Transactional(readOnly = false)
public class ErpVcrItemDetailWriteServiceImpl extends ServiceImpl<ErpVcrItemDetailWriteMapper, ErpVcrItemDetailDto> implements ErpVcrItemDetailWriteService {
}
