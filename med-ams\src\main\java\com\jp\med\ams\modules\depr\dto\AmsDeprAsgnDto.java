package com.jp.med.ams.modules.depr.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 资产折旧分配
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Data
@TableName("ams_depr_asgn" )
public class AmsDeprAsgnDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 固定资产码 */
    @TableField(value = "fa_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String faCode;

    /** 科室 */
    @TableField(value = "org_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String orgId;

    /** 比例 */
    @TableField(value = "prop",insertStrategy = FieldStrategy.NOT_EMPTY)
    private BigDecimal prop;

    /** 更新时间 */
    @TableField(value = "update_time",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String updateTime;

    /** 操作人 */
    @TableField(value = "opter",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String opter;

    /** 修改原因 */
    @TableField(value = "modi_rea",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String modiRea;

    /** 有效标志 */
    @TableField(value = "active_flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

    /** 资产 */
    @TableField(exist = false)
    private String asset;

    /** 分配结果 */
    @TableField(exist = false)
    private List<AmsDeprAsgnDto> list;
}
