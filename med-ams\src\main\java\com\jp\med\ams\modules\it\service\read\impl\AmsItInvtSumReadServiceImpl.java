package com.jp.med.ams.modules.it.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.it.mapper.read.AmsItInvtAddReadMapper;
import com.jp.med.ams.modules.it.service.read.AmsItInvtAddReadService;
import com.jp.med.ams.modules.it.vo.AmsItInvtAddVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.it.mapper.read.AmsItInvtSumReadMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtSumDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtSumVo;
import com.jp.med.ams.modules.it.service.read.AmsItInvtSumReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class AmsItInvtSumReadServiceImpl extends ServiceImpl<AmsItInvtSumReadMapper, AmsItInvtSumDto> implements AmsItInvtSumReadService {

    @Autowired
    private AmsItInvtSumReadMapper amsItInvtSumReadMapper;

    @Override
    public List<AmsItInvtSumVo> queryList(AmsItInvtSumDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        if (Objects.isNull(dto.getEndDate())) {
            return amsItInvtSumReadMapper.queryList(dto);
        } else {
            return amsItInvtSumReadMapper.queryInvtSum(dto);
        }
    }

    @Override
    public List<AmsItInvtSumVo> modelList(AmsItInvtSumDto dto) {
        return amsItInvtSumReadMapper.modelList(dto);
    }

    /**
     * 查询耗材阈值列表
     * */
    @Override
    public List<AmsItInvtSumVo> queryAlarmList(AmsItInvtSumDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<AmsItInvtSumVo> alarmListVos = amsItInvtSumReadMapper.queryAlarmList(dto);
        for (AmsItInvtSumVo vo:alarmListVos) {
            if (Objects.isNull(vo.getNum())){
                vo.setNum(0);
            }
        }
        return alarmListVos;
    }
    /**
     * 查询耗材阈值列表
     * */
    @Override
    public List<AmsItInvtSumVo> queryAlarmListDetial(AmsItInvtSumDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<AmsItInvtSumVo> alarmListVos = amsItInvtSumReadMapper.queryAlarmListDetial(dto);
        for (AmsItInvtSumVo vo:alarmListVos) {
            if (Objects.isNull(vo.getNum())){
                vo.setNum(0);
            }
        }
        return alarmListVos;
    }

    /**
     * 查询生成要货单选中id的详细信息
     * */
    @Override
    public List<AmsItInvtSumVo> queryCheckedAlarmList(AmsItInvtSumDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<AmsItInvtSumVo> alarmListVos = amsItInvtSumReadMapper.queryCheckedList(dto);
        for (AmsItInvtSumVo vo:alarmListVos) {
            if (Objects.isNull(vo.getNum())){
                vo.setNum(0);
            }
        }
        return alarmListVos;
    }
}
