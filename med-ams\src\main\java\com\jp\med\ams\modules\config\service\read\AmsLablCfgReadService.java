package com.jp.med.ams.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.config.dto.AmsLablCfgDto;
import com.jp.med.ams.modules.config.vo.AmsLablCfgVo;

import java.util.List;

/**
 * 资产标签配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 17:36:48
 */
public interface AmsLablCfgReadService extends IService<AmsLablCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsLablCfgVo> queryList(AmsLablCfgDto dto);
}

