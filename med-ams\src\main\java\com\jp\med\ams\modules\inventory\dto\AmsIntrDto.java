package com.jp.med.ams.modules.inventory.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 资产盘点表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Data
@TableName("ams_intr" )
public class AmsIntrDto extends CommonQueryDto {

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 操作人 */
    @TableField("opter")
    private String opter;

    /**
     * 盘点任务
     */
    @TableField("task_id")
    private Integer taskId;

    /** 同步时间 */
    @TableField("synctime")
    private String synctime;

    /** 总数量 */
    @TableField("totlcnt")
    private Integer totlcnt;

    /** 医疗机构编号 */
    @TableField("hospital_id")
    private String hospitalId;

}
