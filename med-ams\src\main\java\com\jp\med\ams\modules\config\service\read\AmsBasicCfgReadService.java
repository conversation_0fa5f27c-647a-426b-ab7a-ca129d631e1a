package com.jp.med.ams.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import com.jp.med.common.vo.SelectOptionVo;

import java.util.List;
import java.util.Map;

/**
 * 资产配置表
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:47:25
 */
public interface AmsBasicCfgReadService extends IService<AmsBasicCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsBasicCfgVo> queryList(AmsBasicCfgDto dto);

    /**
     * 判断是否唯一
     * @param dto
     */
    void checkOnly(AmsBasicCfgDto dto);

    /**
     * 查询配置
     * @param dto
     * @return
     */
    Map<String, List<SelectOptionVo>> queryConfig(AmsBasicCfgDto dto);
}

