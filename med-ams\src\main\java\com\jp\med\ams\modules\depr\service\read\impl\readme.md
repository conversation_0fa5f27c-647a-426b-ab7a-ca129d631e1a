```mermaid
graph TD
    A[开始: queryDeprSummary2 方法] --> B[获取查询条件 ym]
    B --> C[计算上个月期号 lastMonthYm]
    C --> D[获取上个月资产快照数据 amsPropertyMonthlySnapshotVos]
    D --> E[获取资金来源配置]
    E --> F[获取房屋维修资产 houseRepairAssets]
    F --> G[获取普通资产 normalAssets]
    G --> H[处理混合资金来源拆分 processMixSource]
    H --> I1{是否使用新分类且<br>按大类计算折旧?}
    I1 -->|是| I2[转换资产分类为大类]
    I1 -->|否| J
    I2 --> J{是否计算往年财政资金?}
    J -->|是| K[处理往年财政补助资金标记]
    J -->|否| L
    K --> L[按科室、资产类型、资金来源分组]
    L --> M1[处理普通资产: processNormalAssets]
    M1 --> M2[获取科室分摊比例]
    M2 --> M3[处理房屋维修资产: processHouseRepairAssets]
    M3 --> N[排序和资金来源名称处理]
    N --> O[合并相同科室、资产类型、资金来源的记录]
    O --> P[根据查询条件筛选结果]
    P --> Q[构建科室代码和名称映射]
    Q --> R[按科室分组汇总]
    R --> S[结束: 返回最终结果]

    subgraph 资产处理
    T1[createDeprVo] --> T2[计算本月原值]
    T2 --> T3[计算上月原值]
    T3 --> T4[计算原值变化]
    T4 --> T5[计算月折旧额]
    T5 --> T6[计算累计折旧额]
    T6 --> T7[计算加权平均折旧率]
    end

    M1 --> T1
    M3 --> T1
a
```