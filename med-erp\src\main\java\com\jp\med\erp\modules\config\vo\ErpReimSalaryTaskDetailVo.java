package com.jp.med.erp.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 应发工资报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Data
public class ErpReimSalaryTaskDetailVo {

	/** id */
	private Integer id;

	/** 报销任务id */
	private Integer taskId;

	/** 报销科室 */
	private String orgId;

	/** 报销类型 */
	private String reimType;

	/** 报销金额 */
	private BigDecimal reimAmt;

	/** 报销摘要 */
	private String reimDesc;

	/** 工资项明细type */
	private String type;

	/** 个人扣减员工编号 */
	private String empCode;

	/** 报销项目 */
	private String reimName;

	/** 人员类型(在编 招聘 临聘) */
	private String empType;

}
