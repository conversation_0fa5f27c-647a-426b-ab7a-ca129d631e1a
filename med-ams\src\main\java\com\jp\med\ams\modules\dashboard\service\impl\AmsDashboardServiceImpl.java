package com.jp.med.ams.modules.dashboard.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.dashboard.dto.AmsDashboardFilterDto;
import com.jp.med.ams.modules.dashboard.mapper.AmsDashboardMapper;
import com.jp.med.ams.modules.dashboard.service.AmsDashboardService;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardChartVo;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardMetricsVo;
import com.jp.med.ams.modules.dashboard.vo.AmsDashboardTableVo;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrDetailReadMapper;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrTaskReadMapper;
import com.jp.med.common.constant.MedConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产盘点仪表盘服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class AmsDashboardServiceImpl implements AmsDashboardService {

    @Autowired
    private AmsDashboardMapper amsDashboardMapper;

    @Autowired
    private AmsIntrTaskReadMapper amsIntrTaskReadMapper;

    @Autowired
    private AmsIntrDetailReadMapper amsIntrDetailReadMapper;

    @Override
    public AmsDashboardMetricsVo getMetrics(AmsDashboardFilterDto filterDto) {
        processFilterDto(filterDto);
        AmsDashboardMetricsVo metrics = new AmsDashboardMetricsVo();

        // 获取总任务数
        Integer totalTasks = amsDashboardMapper.countTotalTasks(filterDto);
        metrics.setTotalTasks(totalTasks != null ? totalTasks : 0);

        // 获取已完成任务数
        filterDto.setStatus("1");
        Integer completedTasks = amsDashboardMapper.countTotalTasks(filterDto);
        filterDto.setStatus(null);

        // 计算完成率
        double completionRate = 0.0;
        if (totalTasks != null && totalTasks > 0) {
            completionRate = (completedTasks * 100.0) / totalTasks;
        }
        metrics.setCompletionRate(BigDecimal.valueOf(completionRate).setScale(1, RoundingMode.HALF_UP).doubleValue());

        // 获取资产统计数据
        Map<String, Object> assetStats = amsDashboardMapper.getAssetStatistics(filterDto);
        if (assetStats != null) {
            metrics.setTotalAssets(getIntegerValue(assetStats.get("totalAssets")));
            metrics.setInventoriedAssets(getIntegerValue(assetStats.get("inventoriedAssets")));
            metrics.setUninventoriedAssets(getIntegerValue(assetStats.get("uninventoriedAssets")));
            metrics.setProfitAssets(getIntegerValue(assetStats.get("profitAssets")));
            metrics.setLossAssets(getIntegerValue(assetStats.get("lossAssets")));
        }

        // 计算趋势数据
        calculateTrends(metrics, filterDto);

        return metrics;
    }

    @Override
    public AmsDashboardChartVo getCharts(AmsDashboardFilterDto filterDto) {
        processFilterDto(filterDto);
        AmsDashboardChartVo charts = new AmsDashboardChartVo();

        // 获取完成率趋势数据
        charts.setCompletionTrend(getCompletionTrendData(filterDto));

        // 获取科室统计数据
        charts.setDepartmentStats(getDepartmentStatsData(filterDto));

        // 获取资产类型分布数据
        charts.setAssetTypeDistribution(getAssetTypeDistributionData(filterDto));

        // 获取盘盈盘亏对比数据
        charts.setProfitLossComparison(getProfitLossComparisonData(filterDto));

        // 获取月度任务统计数据
        charts.setMonthlyTasks(getMonthlyTasksData(filterDto));

        // 获取异常资产分析数据
        charts.setAbnormalAssets(getAbnormalAssetsData(filterDto));

        return charts;
    }

    @Override
    public List<AmsDashboardTableVo> getTableData(AmsDashboardFilterDto filterDto) {
        processFilterDto(filterDto);

        // 设置分页
        if (filterDto.getPageNum() != null && filterDto.getPageSize() != null) {
            PageHelper.startPage(filterDto.getPageNum(), filterDto.getPageSize());
        }

        // 查询表格数据
        List<Map<String, Object>> dataList = amsDashboardMapper.getTableData(filterDto);

        // 转换为VO对象
        return dataList.stream().map(this::convertToTableVo).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getAllDashboardData(AmsDashboardFilterDto filterDto) {
        Map<String, Object> result = new HashMap<>();

        // 获取所有数据
        result.put("metrics", getMetrics(filterDto));
        result.put("charts", getCharts(filterDto));

        // 表格数据不分页
        filterDto.setPageNum(null);
        filterDto.setPageSize(null);
        result.put("table", getTableData(filterDto));

        return result;
    }

    @Override
    public void exportDashboardData(AmsDashboardFilterDto filterDto, HttpServletResponse response) {
        try {
            // 获取导出数据
            filterDto.setPageNum(null);
            filterDto.setPageSize(null);
            List<AmsDashboardTableVo> tableData = getTableData(filterDto);

            // 创建Excel写入器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            // 设置表头
            writer.addHeaderAlias("taskName", "盘点任务");
            writer.addHeaderAlias("departmentName", "科室");
            writer.addHeaderAlias("totalAssets", "资产总数");
            writer.addHeaderAlias("inventoriedAssets", "已盘点数");
            writer.addHeaderAlias("uninventoriedAssets", "未盘点数");
            writer.addHeaderAlias("completionRate", "完成率(%)");
            writer.addHeaderAlias("profitCount", "盘盈数");
            writer.addHeaderAlias("lossCount", "盘亏数");
            writer.addHeaderAlias("statusDesc", "状态");
            writer.addHeaderAlias("createTime", "创建时间");
            writer.addHeaderAlias("creator", "创建人");

            // 写入数据
            writer.write(tableData, true);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            String fileName = URLEncoder.encode("资产盘点仪表盘数据_" + DateUtil.format(new Date(), "yyyyMMddHHmmss"), "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 输出到响应流
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            out.close();

        } catch (Exception e) {
            log.error("导出仪表盘数据失败", e);
            throw new RuntimeException("导出数据失败");
        }
    }

    @Override
    public List<Map<String, Object>> getDepartmentOptions() {
        return amsDashboardMapper.getDepartmentOptions(MedConst.HOSPITAL_ID);
    }

    @Override
    public List<Map<String, Object>> getAssetTypeOptions() {
        return amsDashboardMapper.getAssetTypeOptions(MedConst.HOSPITAL_ID);
    }

    /**
     * 处理筛选条件
     */
    private void processFilterDto(AmsDashboardFilterDto filterDto) {
        // 处理时间范围
        if (filterDto.getDateRange() != null && filterDto.getDateRange().length == 2) {
            filterDto.setStartDate(filterDto.getDateRange()[0]);
            filterDto.setEndDate(filterDto.getDateRange()[1]);
        }

        // 设置医院ID
        if (StrUtil.isBlank(filterDto.getHospitalId())) {
            filterDto.setHospitalId(MedConst.HOSPITAL_ID);
        }

        // 如果没有指定年度，默认当前年度
        if (filterDto.getYear() == null) {
            filterDto.setYear(DateUtil.year(new Date()));
        }
    }

    /**
     * 计算趋势数据
     */
    private void calculateTrends(AmsDashboardMetricsVo metrics, AmsDashboardFilterDto filterDto) {
        // 获取上期数据进行对比
        Integer lastYear = filterDto.getYear() - 1;
        filterDto.setYear(lastYear);

        // 获取上期任务数
        Integer lastTotalTasks = amsDashboardMapper.countTotalTasks(filterDto);
        AmsDashboardMetricsVo.TrendData tasksTrend = new AmsDashboardMetricsVo.TrendData();
        calculateTrendData(tasksTrend, metrics.getTotalTasks(), lastTotalTasks);
        metrics.setTasksTrend(tasksTrend);

        // 恢复当前年度
        filterDto.setYear(lastYear + 1);

        // 其他趋势数据计算类似...
    }

    /**
     * 计算单个趋势数据
     */
    private void calculateTrendData(AmsDashboardMetricsVo.TrendData trend, Integer current, Integer last) {
        if (current == null) current = 0;
        if (last == null || last == 0) {
            trend.setValue(current.doubleValue());
            trend.setType("stable");
            trend.setPercentage(0.0);
        } else {
            double change = current - last;
            double percentage = (change / last) * 100;
            trend.setValue(change);
            trend.setType(change > 0 ? "up" : (change < 0 ? "down" : "stable"));
            trend.setPercentage(BigDecimal.valueOf(percentage).setScale(1, RoundingMode.HALF_UP).doubleValue());
        }
    }

    /**
     * 获取完成率趋势数据
     */
    private AmsDashboardChartVo.ChartData getCompletionTrendData(AmsDashboardFilterDto filterDto) {
        AmsDashboardChartVo.ChartData chartData = new AmsDashboardChartVo.ChartData();
        List<Map<String, Object>> trendData = amsDashboardMapper.getCompletionTrendData(filterDto);

        List<String> xAxis = new ArrayList<>();
        List<Number> completionRates = new ArrayList<>();

        for (Map<String, Object> item : trendData) {
            xAxis.add(String.valueOf(item.get("month")));
            Integer total = getIntegerValue(item.get("total"));
            Integer completed = getIntegerValue(item.get("completed"));
            double rate = total > 0 ? (completed * 100.0 / total) : 0;
            completionRates.add(BigDecimal.valueOf(rate).setScale(1, RoundingMode.HALF_UP));
        }

        chartData.setXAxis(xAxis);

        AmsDashboardChartVo.ChartData.Series series = new AmsDashboardChartVo.ChartData.Series();
        series.setName("完成率");
        series.setData(completionRates);
        series.setType("line");

        chartData.setSeries(Collections.singletonList(series));

        return chartData;
    }

    /**
     * 获取科室统计数据
     */
    private AmsDashboardChartVo.ChartData getDepartmentStatsData(AmsDashboardFilterDto filterDto) {
        AmsDashboardChartVo.ChartData chartData = new AmsDashboardChartVo.ChartData();
        List<Map<String, Object>> deptData = amsDashboardMapper.getDepartmentStatsData(filterDto);

        List<String> xAxis = new ArrayList<>();
        List<Number> totalAssets = new ArrayList<>();
        List<Number> inventoriedAssets = new ArrayList<>();

        for (Map<String, Object> item : deptData) {
            xAxis.add(String.valueOf(item.get("deptName")));
            totalAssets.add(getIntegerValue(item.get("totalAssets")));
            inventoriedAssets.add(getIntegerValue(item.get("inventoriedAssets")));
        }

        chartData.setXAxis(xAxis);

        List<AmsDashboardChartVo.ChartData.Series> seriesList = new ArrayList<>();

        AmsDashboardChartVo.ChartData.Series series1 = new AmsDashboardChartVo.ChartData.Series();
        series1.setName("资产总数");
        series1.setData(totalAssets);
        series1.setType("bar");
        seriesList.add(series1);

        AmsDashboardChartVo.ChartData.Series series2 = new AmsDashboardChartVo.ChartData.Series();
        series2.setName("已盘点数");
        series2.setData(inventoriedAssets);
        series2.setType("bar");
        seriesList.add(series2);

        chartData.setSeries(seriesList);

        return chartData;
    }

    /**
     * 获取资产类型分布数据
     */
    private List<AmsDashboardChartVo.PieData> getAssetTypeDistributionData(AmsDashboardFilterDto filterDto) {
        List<Map<String, Object>> typeData = amsDashboardMapper.getAssetTypeDistributionData(filterDto);

        // 计算总数
        int total = typeData.stream()
                .mapToInt(item -> getIntegerValue(item.get("count")))
                .sum();

        return typeData.stream().map(item -> {
            AmsDashboardChartVo.PieData pieData = new AmsDashboardChartVo.PieData();
            pieData.setName(String.valueOf(item.get("assetType")));
            Integer count = getIntegerValue(item.get("count"));
            pieData.setValue(count);

            // 计算百分比
            double percentage = total > 0 ? (count * 100.0 / total) : 0;
            pieData.setPercentage(BigDecimal.valueOf(percentage).setScale(1, RoundingMode.HALF_UP).doubleValue());

            return pieData;
        }).collect(Collectors.toList());
    }

    /**
     * 获取盘盈盘亏对比数据
     */
    private AmsDashboardChartVo.ChartData getProfitLossComparisonData(AmsDashboardFilterDto filterDto) {
        AmsDashboardChartVo.ChartData chartData = new AmsDashboardChartVo.ChartData();
        List<Map<String, Object>> comparisonData = amsDashboardMapper.getProfitLossComparisonData(filterDto);

        List<String> xAxis = new ArrayList<>();
        List<Number> profitData = new ArrayList<>();
        List<Number> lossData = new ArrayList<>();

        for (Map<String, Object> item : comparisonData) {
            xAxis.add(String.valueOf(item.get("month")));
            profitData.add(getIntegerValue(item.get("profitCount")));
            lossData.add(getIntegerValue(item.get("lossCount")));
        }

        chartData.setXAxis(xAxis);

        List<AmsDashboardChartVo.ChartData.Series> seriesList = new ArrayList<>();

        AmsDashboardChartVo.ChartData.Series series1 = new AmsDashboardChartVo.ChartData.Series();
        series1.setName("盘盈");
        series1.setData(profitData);
        series1.setType("bar");
        seriesList.add(series1);

        AmsDashboardChartVo.ChartData.Series series2 = new AmsDashboardChartVo.ChartData.Series();
        series2.setName("盘亏");
        series2.setData(lossData);
        series2.setType("bar");
        seriesList.add(series2);

        chartData.setSeries(seriesList);

        return chartData;
    }

    /**
     * 获取月度任务统计数据
     */
    private AmsDashboardChartVo.ChartData getMonthlyTasksData(AmsDashboardFilterDto filterDto) {
        AmsDashboardChartVo.ChartData chartData = new AmsDashboardChartVo.ChartData();
        List<Map<String, Object>> monthlyData = amsDashboardMapper.getMonthlyTasksData(filterDto);

        List<String> xAxis = Arrays.asList("1月", "2月", "3月", "4月", "5月", "6月",
                "7月", "8月", "9月", "10月", "11月", "12月");
        List<Number> taskCounts = new ArrayList<>();

        // 创建月份映射
        Map<Integer, Integer> monthMap = new HashMap<>();
        for (Map<String, Object> item : monthlyData) {
            Integer month = getIntegerValue(item.get("month"));
            Integer count = getIntegerValue(item.get("taskCount"));
            monthMap.put(month, count);
        }

        // 填充数据
        for (int i = 1; i <= 12; i++) {
            taskCounts.add(monthMap.getOrDefault(i, 0));
        }

        chartData.setXAxis(xAxis);

        AmsDashboardChartVo.ChartData.Series series = new AmsDashboardChartVo.ChartData.Series();
        series.setName("任务数");
        series.setData(taskCounts);
        series.setType("bar");

        chartData.setSeries(Collections.singletonList(series));

        return chartData;
    }

    /**
     * 获取异常资产分析数据
     */
    private AmsDashboardChartVo.RadarData getAbnormalAssetsData(AmsDashboardFilterDto filterDto) {
        AmsDashboardChartVo.RadarData radarData = new AmsDashboardChartVo.RadarData();

        // 设置指标
        List<AmsDashboardChartVo.RadarData.Indicator> indicators = Arrays.asList(
                createIndicator("超期未盘点", 100),
                createIndicator("频繁盘亏", 100),
                createIndicator("盘点效率异常", 100),
                createIndicator("数据质量问题", 100),
                createIndicator("资产变更频繁", 100)
        );
        radarData.setIndicator(indicators);

        // 获取数据
        Map<String, Object> abnormalData = amsDashboardMapper.getAbnormalAssetsData(filterDto);

        AmsDashboardChartVo.RadarData.RadarSeries series = new AmsDashboardChartVo.RadarData.RadarSeries();
        series.setName("异常指数");
        series.setValue(Arrays.asList(
                getIntegerValue(abnormalData.get("overdueCount")),
                getIntegerValue(abnormalData.get("frequentLossCount")),
                getIntegerValue(abnormalData.get("efficiencyAbnormalCount")),
                getIntegerValue(abnormalData.get("dataQualityCount")),
                getIntegerValue(abnormalData.get("frequentChangeCount"))
        ));

        radarData.setSeries(Collections.singletonList(series));

        return radarData;
    }

    /**
     * 创建雷达图指标
     */
    private AmsDashboardChartVo.RadarData.Indicator createIndicator(String name, Number max) {
        AmsDashboardChartVo.RadarData.Indicator indicator = new AmsDashboardChartVo.RadarData.Indicator();
        indicator.setName(name);
        indicator.setMax(max);
        return indicator;
    }

    /**
     * 转换为表格VO
     */
    private AmsDashboardTableVo convertToTableVo(Map<String, Object> data) {
        AmsDashboardTableVo vo = new AmsDashboardTableVo();

        vo.setId(String.valueOf(data.get("id")));
        vo.setTaskName(String.valueOf(data.get("taskName")));
        vo.setDepartment(String.valueOf(data.get("department")));
        vo.setDepartmentName(String.valueOf(data.get("departmentName")));
        vo.setTotalAssets(getIntegerValue(data.get("totalAssets")));
        vo.setInventoriedAssets(getIntegerValue(data.get("inventoriedAssets")));
        vo.setUninventoriedAssets(getIntegerValue(data.get("uninventoriedAssets")));

        // 计算完成率
        Integer total = vo.getTotalAssets();
        Integer inventoried = vo.getInventoriedAssets();
        double rate = total > 0 ? (inventoried * 100.0 / total) : 0;
        vo.setCompletionRate(BigDecimal.valueOf(rate).setScale(1, RoundingMode.HALF_UP).doubleValue());

        vo.setProfitCount(getIntegerValue(data.get("profitCount")));
        vo.setLossCount(getIntegerValue(data.get("lossCount")));
        vo.setInventoryDate(String.valueOf(data.get("inventoryDate")));
        vo.setCreateTime(String.valueOf(data.get("createTime")));
        vo.setCreator(String.valueOf(data.get("creator")));

        String status = String.valueOf(data.get("status"));
        vo.setStatus(status);
        vo.setStatusDesc("1".equals(status) ? "盘点完成" : "盘点中");

        vo.setRemark(String.valueOf(data.get("remark")));

        return vo;
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntegerValue(Object value) {
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(String.valueOf(value));
        } catch (NumberFormatException e) {
            return 0;
        }
    }
} 