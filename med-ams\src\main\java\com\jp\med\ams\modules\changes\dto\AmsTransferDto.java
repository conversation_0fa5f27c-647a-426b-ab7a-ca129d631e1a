package com.jp.med.ams.modules.changes.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.ams.modules.changes.entity.AmsTransferChkEntity;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;

import java.util.List;

/**
 * 资产转移
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
@Data
@TableName("ams_transfer")
public class AmsTransferDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 审核批次号
     */
    @TableField("bchno")
    private String bchno;

    /**
     * 资产表id
     */
    @TableField("fa_code")
    private String faCode;

    /**
     * 转出科室
     */
    @TableField("traf_out_dept")
    private String trafOutDept;

    /**
     * 转入使用科室代码
     */
    @TableField("traf_in_dept")
    private String trafInDept;

    /**
     * 转入管理科室代码
     */
    @TableField("traf_in_dept_manage")
    private String trafInDeptManage;

    /**
     * 转入存放位置代码
     */
    @TableField("traf_in_storage_area")
    private String trafInStorageArea;

    /**
     * 转入保存地点代码
     */
    @TableField("traf_in_storage_location")
    private String trafInStorageLocation;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 接收时间
     */
    @TableField("rec_time")
    private String recTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 接收备注
     */
    @TableField("rec_remarks")
    private String recRemarks;

    /**
     * 业务状态
     */
    @TableField("prosstas")
    private String prosstas;

    /**
     * 医疗机构id
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 有效标准
     */
    @TableField("active_flag")
    private String activeFlag;

    /**
     * 审核流程详情
     */
    @TableField(exist = false)
    private List<AuditDetail> auditDetails;

    /**
     * 审核表id
     */
    @TableField(exist = false)
    private List<Long> ids;

    /**
     * 状态
     */
    @TableField(exist = false)
    private String status;

    /**
     * 查询科室字段
     */
    @TableField(exist = false)
    private String deptField;

    /**
     * 查询科室
     */
    @TableField(exist = false)
    private String deptCode;

    /**
     * 最多审核人数
     */
    @TableField(exist = false)
    private Integer curChkSeq;

    /**
     * 需要更新的审核信息
     */
    @TableField(exist = false)
    private AmsTransferChkEntity chk;

    /**
     * 转出科室名称
     */
    @TableField(exist = false)
    private String trafOutDeptName;

    /**
     * 转入使用科室名称
     */
    @TableField(exist = false)
    private String trafInDeptName;

    /**
     * 转入管理科室名称
     */
    @TableField(exist = false)
    private String trafInDeptManageName;

    /**
     * 转入存放位置名称
     */
    @TableField(exist = false)
    private String trafInStorageAreaName;

    /**
     * 转入保存地点名称
     */
    @TableField(exist = false)
    private String trafInstorageLocationName;

    /**
     * 资产名称
     */
    @TableField(exist = false)
    private String faCodeName;

    /**
     * 资产faCodeList
     */
    @TableField(exist = false)
    private List<String> faCodes;
}
