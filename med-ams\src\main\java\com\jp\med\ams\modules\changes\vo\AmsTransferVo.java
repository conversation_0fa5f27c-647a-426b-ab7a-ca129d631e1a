package com.jp.med.ams.modules.changes.vo;

import lombok.Data;

import java.util.List;

/**
 * 资产转移
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
@Data
public class AmsTransferVo {

    /**
     * id
     */
    private Long id;

    /**
     * 资产
     */
    private String faCode;

    /**
     * 转出科室
     */
    private String trafOutDept;

    /**
     * 转入科室
     */
    private String trafInDept;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 业务状态
     */
    private String prosstas;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    /**
     * 有效标准
     */
    private String activeFlag;

    /**
     * 转出科室名称
     */
    private String trafOutDeptName;
    private String trafInDeptManageName;

    /**
     * 转入科室名称
     */
    private String trafInDeptName;

    /**
     * 创建人名称
     */
    private String crterName;

    /**
     * 接收人名称
     */
    private String recerName;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 审核批次号
     */
    private String bchno;

    private String recer;

    private String trafInStorageLocation;
    private String trafInStorageAreaName;
    /**
     * 资产名称
     */
    private String assetNames;
    private List<AmsPropertyInfo> propertyInfos;

    private String trafOutDeptCode;
    private String trafInDeptCode;
    private String trafInDeptManage;
    private String trafInStorageArea;

    private List<String> faCodes;

    public String getTrafInStorageArea() {
        return trafInStorageArea;
    }

    public void setTrafInStorageArea(String trafInStorageArea) {
        this.trafInStorageArea = trafInStorageArea;
    }

    public String getTrafInDeptManage() {
        return trafInDeptManage;
    }

    public void setTrafInDeptManage(String trafInDeptManage) {
        this.trafInDeptManage = trafInDeptManage;
    }

    public String getTrafInDeptCode() {
        return trafInDeptCode;
    }

    public void setTrafInDeptCode(String trafInDeptCode) {
        this.trafInDeptCode = trafInDeptCode;
    }

    public String getTrafOutDeptCode() {
        return trafOutDeptCode;
    }

    public void setTrafOutDeptCode(String trafOutDeptCode) {
        this.trafOutDeptCode = trafOutDeptCode;
    }
}
