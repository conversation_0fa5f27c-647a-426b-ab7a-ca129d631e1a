package com.jp.med.ams.modules.changes.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.service.read.AmsChgRcdReadService;
import com.jp.med.ams.modules.changes.service.write.AmsChgRcdWriteService;
import com.jp.med.common.entity.common.CommonResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 资产变更记录表
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
@Api(value = "资产变更记录表", tags = "资产变更记录表")
@RestController
@RequestMapping("amsChgRcd")
public class AmsChgRcdController {

    @Autowired
    private AmsChgRcdReadService amsChgRcdReadService;

    @Autowired
    private AmsChgRcdWriteService amsChgRcdWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产变更记录表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsChgRcdDto dto) {
        return CommonResult.paging(amsChgRcdReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产变更记录表")
    @PostMapping("/save")
    public CommonResult<Object> save(@RequestBody AmsChgRcdDto dto) {

        return CommonResult.success(amsChgRcdWriteService.saveAmsChgRcd(dto));
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产变更记录表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsChgRcdDto dto) {
        amsChgRcdWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产变更记录表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsChgRcdDto dto) {
        amsChgRcdWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 审核
     */
    @ApiOperation("审核")
    @PostMapping("/approval")
    public CommonResult<?> approval(@RequestBody AmsChgRcdDto dto) {
        amsChgRcdWriteService.approval(dto);
        return CommonResult.success();
    }

}
