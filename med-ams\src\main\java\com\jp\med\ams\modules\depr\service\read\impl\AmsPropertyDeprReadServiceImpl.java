package com.jp.med.ams.modules.depr.service.read.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.config.mapper.read.AmsTypeCfgReadMapper;
import com.jp.med.ams.modules.config.mapper.read.AmsTypenCfgReadMapper;
import com.jp.med.ams.modules.config.vo.AmsTypeCfgVo;
import com.jp.med.ams.modules.config.vo.AmsTypenCfgVo;
import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.mapper.read.AmsDeprAsgnReadMapper;
import com.jp.med.ams.modules.depr.mapper.read.AmsPropertyDeprReadMapper;
import com.jp.med.ams.modules.depr.service.read.AmsPropertyDeprReadService;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.factory.AmsPropertyDeprProcessorFactory;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDeprVo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyMonthDeprVo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyValueAnalysisVo;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyMonthlySnapshotReadMapper;
import com.jp.med.common.dto.erp.ErpPropertyDeprDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.vo.ErpAmsPropertyDepr2Vo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//@Transactional(readOnly = true)
@Slf4j
@Service
public class AmsPropertyDeprReadServiceImpl extends ServiceImpl<AmsPropertyDeprReadMapper, AmsPropertyDeprDto>
        implements AmsPropertyDeprReadService {

    @Autowired
    @Lazy
    private AmsPropertyDeprReadMapper amsPropertyDeprReadMapper;

    @Autowired
    private AmsPropertyMonthlySnapshotReadMapper amsPropertyMonthlySnapshotReadMapper;

    @Autowired
    private AmsTypenCfgReadMapper amsTypenCfgReadMapper;

    @Resource
    private AmsTypeCfgReadMapper amsTypeCfgReadMapper;

    @Resource
    private AmsDeprAsgnReadMapper amsDeprAsgnReadMapper;

    @Autowired
    private AmsPropertyDeprProcessorFactory processorFactory;

    /**
     * 查询资产折旧表
     *
     * @param dto 查询条件参数对象
     * @return 返回资产折旧数据列表
     */
    @Override
    @Deprecated
    public List<AmsPropertyDeprVo> queryList(AmsPropertyDeprDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // amsPropertyDeprVos.stream().collect(Collectors.toMap(AmsPropertyDeprVo::getOrgName))
        return amsPropertyDeprReadMapper.queryList(dto);
    }

    /**
     * 查询资产折旧汇总数据2（使用责任链模式重构）
     *
     * @param dto 查询条件参数对象
     * @return 返回包含上月对比的折旧汇总数据列表
     */
    public List<AmsPropertyDepr2Vo> queryDeprSummary2(AmsPropertyDeprDto dto) {
        log.debug("开始执行 queryDeprSummary2，参数: {}", JSON.toJSONString(dto));

        // 创建处理上下文
        AmsPropertyDeprSummary2Context context = new AmsPropertyDeprSummary2Context(dto);

        // 获取处理器链并执行
        AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> processorChain = processorFactory
                .createSummary2ProcessorChain();
        processorChain.handle(context);

        log.debug("queryDeprSummary2 执行完成，返回 {} 条记录",
                context.getResult() != null ? context.getResult().size() : 0);

        return context.getResult();
    }

    /**
     * 查询资产折旧汇总数据（使用责任链模式重构）
     *
     * @param dto 查询条件参数对象
     * @return 返回按科室和资产类型汇总的折旧数据列表
     */
    public List<AmsPropertyDepr2Vo> queryDeprSummary(AmsPropertyDeprDto dto) {
        log.debug("开始执行 queryDeprSummary，参数: {}", JSON.toJSONString(dto));

        // 创建处理上下文
        AmsPropertyDeprProcessContext context = new AmsPropertyDeprProcessContext(dto);

        // 获取处理器链并执行
        AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> processorChain = processorFactory
                .createSummaryProcessorChain();
        processorChain.handle(context);

        log.debug("queryDeprSummary 执行完成，返回 {} 条记录",
                context.getResult() != null ? context.getResult().size() : 0);

        return context.getResult();
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public List<ErpAmsPropertyDepr2Vo> erpQueryDeprSummary2(ErpPropertyDeprDto dto) {
        AmsPropertyDeprDto amsDto = new AmsPropertyDeprDto();
        amsDto.setYm(dto.getYm());
        amsDto.setTypen(true);
        List<AmsPropertyDepr2Vo> amsPropertyDepr2Vos = queryDeprSummary2(amsDto);
        List<ErpAmsPropertyDepr2Vo> result = new ArrayList<>();
        amsPropertyDepr2Vos.stream().forEach(vo -> {
            ErpAmsPropertyDepr2Vo erpVo = new ErpAmsPropertyDepr2Vo();
            BeanUtil.copyProperties(vo, erpVo);
            result.add(erpVo);
        });
        return result;
    }

    /**
     * 查询资产每个月份的折旧金额，并按科室汇总每个月的数据
     *
     * @param dto 包含查询条件的数据传输对象
     * @return 返回包含每个月份折旧数据的 AmsPropertyMonthDeprVo 对象列表
     */
    @Override
    public List<AmsPropertyMonthDeprVo> queryDeprMonth(AmsPropertyDeprDto dto) {

        throw new AppException("已弃用");
    }

    @Override
    public List<AmsPropertyValueAnalysisVo> queryValueAnalysis(AmsPropertyDeprDto dto) {

        // 查询期号是否存在折旧快照
        int hasSnapshot = amsPropertyMonthlySnapshotReadMapper.checkBackupExists(dto.getYm());
        dto.setUseSnapshot(false);

        if (hasSnapshot != 0) {
            // 存在折旧快照，则更新折旧数据

            // 查询折旧快照
            dto.setUseSnapshot(true);
        }

        // 查询资产价值构成分析数据

        var result = amsPropertyDeprReadMapper.queryValueAnalysis(dto);
        if (dto.getBigCategoryModel()) {
            if (dto.getTypen()) {
                // 新分类 前三位为大类
                var amsTypenCfgVos = amsTypenCfgReadMapper.queryParent();
                System.out.println(JSON.toJSON(amsTypenCfgVos));
                // 按照前三位分类代码分组汇总
                Map<String, AmsPropertyValueAnalysisVo> summaryMap = new HashMap<>();

                for (AmsPropertyValueAnalysisVo vo : result) {
                    if (vo.getAssetTypeCode() == null || vo.getAssetTypeCode().length() < 3) {
                        continue;
                    }

                    String parentCode = vo.getAssetTypeCode().substring(0, 3);
                    String parentName = amsTypenCfgVos.stream()
                            .filter(cfg -> cfg.getCode().substring(0, 3).equals(parentCode))
                            .findFirst()
                            .map(AmsTypenCfgVo::getAssetTypeName)
                            .orElse(parentCode);

                    log.info("parentCode:{},parentName:{}", parentCode, parentName);
                    sumAmsPropertyValueAnalysisVo(vo, summaryMap, parentCode, parentName);
                }

                // 在完成所有累加后，对最终结果进行一次四舍五入处理
                for (AmsPropertyValueAnalysisVo vo : summaryMap.values()) {
                    // 对所有金额字段进行一次性四舍五入
                    if (vo.getOriginalValue() != null) {
                        vo.setOriginalValue(vo.getOriginalValue().setScale(2, RoundingMode.HALF_UP));
                    }
                    if (vo.getTotalDeprAmount() != null) {
                        vo.setTotalDeprAmount(vo.getTotalDeprAmount().setScale(2, RoundingMode.HALF_UP));
                    }
                    if (vo.getNetValue() != null) {
                        vo.setNetValue(vo.getNetValue().setScale(2, RoundingMode.HALF_UP));
                    }
                    if (vo.getCurrentDepreciation() != null) {
                        vo.setCurrentDepreciation(vo.getCurrentDepreciation().setScale(2, RoundingMode.HALF_UP));
                    }

                    // 计算比率
                    if (vo.getOriginalValue() != null && vo.getOriginalValue().compareTo(BigDecimal.ZERO) > 0) {
                        // 计算累计折旧率并保留2位小数
                        if (vo.getTotalDeprAmount() != null) {
                            vo.setTotalDeprRate(vo.getTotalDeprAmount()
                                    .multiply(new BigDecimal("100"))
                                    .divide(vo.getOriginalValue(), 2, RoundingMode.HALF_UP));
                        }
                        // 计算净值率并保留2位小数
                        if (vo.getNetValue() != null) {
                            vo.setNetValueRate(vo.getNetValue()
                                    .multiply(new BigDecimal("100"))
                                    .divide(vo.getOriginalValue(), 2, RoundingMode.HALF_UP));
                        }
                    }
                }

                return new ArrayList<>(summaryMap.values());
            } else {
                // 老分类第一位为大类
                var amsTypeCfgVos = amsTypeCfgReadMapper.queryParent();
                // 按照第一位分类代码分组汇总
                Map<String, AmsPropertyValueAnalysisVo> summaryMap = new HashMap<>();

                for (AmsPropertyValueAnalysisVo vo : result) {
                    if (vo.getAssetTypeCode() == null || vo.getAssetTypeCode().isEmpty()) {
                        continue;
                    }

                    String parentCode = vo.getAssetTypeCode().substring(0, 1);
                    String parentName = amsTypeCfgVos.stream()
                            .filter(cfg -> cfg.getAssetTypeCode().equals(parentCode))
                            .findFirst()
                            .map(AmsTypeCfgVo::getAssetTypeName)
                            .orElse(parentCode);

                    sumAmsPropertyValueAnalysisVo(vo, summaryMap, parentCode, parentName);
                }

                // 在完成所有累加后，对最终结果进行一次四舍五入处理
                for (AmsPropertyValueAnalysisVo vo : summaryMap.values()) {
                    // 对所有金额字段进行一次性四舍五入
                    if (vo.getOriginalValue() != null) {
                        vo.setOriginalValue(vo.getOriginalValue().setScale(2, RoundingMode.HALF_UP));
                    }
                    if (vo.getTotalDeprAmount() != null) {
                        vo.setTotalDeprAmount(vo.getTotalDeprAmount().setScale(2, RoundingMode.HALF_UP));
                    }
                    if (vo.getNetValue() != null) {
                        vo.setNetValue(vo.getNetValue().setScale(2, RoundingMode.HALF_UP));
                    }
                    if (vo.getCurrentDepreciation() != null) {
                        vo.setCurrentDepreciation(vo.getCurrentDepreciation().setScale(2, RoundingMode.HALF_UP));
                    }

                    // 计算比率
                    if (vo.getOriginalValue() != null && vo.getOriginalValue().compareTo(BigDecimal.ZERO) > 0) {
                        // 计算累计折旧率并保留2位小数
                        if (vo.getTotalDeprAmount() != null) {
                            vo.setTotalDeprRate(vo.getTotalDeprAmount()
                                    .multiply(new BigDecimal("100"))
                                    .divide(vo.getOriginalValue(), 2, RoundingMode.HALF_UP));
                        }
                        // 计算净值率并保留2位小数
                        if (vo.getNetValue() != null) {
                            vo.setNetValueRate(vo.getNetValue()
                                    .multiply(new BigDecimal("100"))
                                    .divide(vo.getOriginalValue(), 2, RoundingMode.HALF_UP));
                        }
                    }
                }

                return new ArrayList<>(summaryMap.values());
            }
        }

        // 对非大类模式的结果也进行四舍五入处理，保持一致性
        for (AmsPropertyValueAnalysisVo vo : result) {
            if (vo.getOriginalValue() != null) {
                vo.setOriginalValue(vo.getOriginalValue().setScale(2, RoundingMode.HALF_UP));
            }
            if (vo.getTotalDeprAmount() != null) {
                vo.setTotalDeprAmount(vo.getTotalDeprAmount().setScale(2, RoundingMode.HALF_UP));
            }
            if (vo.getNetValue() != null) {
                vo.setNetValue(vo.getNetValue().setScale(2, RoundingMode.HALF_UP));
            }
            if (vo.getCurrentDepreciation() != null) {
                vo.setCurrentDepreciation(vo.getCurrentDepreciation().setScale(2, RoundingMode.HALF_UP));
            }
            // 重新计算比率，确保精度一致
            if (vo.getOriginalValue() != null && vo.getOriginalValue().compareTo(BigDecimal.ZERO) > 0) {
                if (vo.getTotalDeprAmount() != null) {
                    vo.setTotalDeprRate(vo.getTotalDeprAmount()
                            .multiply(new BigDecimal("100"))
                            .divide(vo.getOriginalValue(), 2, RoundingMode.HALF_UP));
                }
                if (vo.getNetValue() != null) {
                    vo.setNetValueRate(vo.getNetValue()
                            .multiply(new BigDecimal("100"))
                            .divide(vo.getOriginalValue(), 2, RoundingMode.HALF_UP));
                }
            }
        }

        return result;
    }
    //

    private static void sumAmsPropertyValueAnalysisVo(AmsPropertyValueAnalysisVo vo,
            Map<String, AmsPropertyValueAnalysisVo> summaryMap, String parentCode, String parentName) {
        AmsPropertyValueAnalysisVo summaryVo = summaryMap.computeIfAbsent(parentCode, k -> {
            AmsPropertyValueAnalysisVo newVo = new AmsPropertyValueAnalysisVo();
            newVo.setAssetTypeCode(parentCode);
            newVo.setAssetTypeName(parentName);
            newVo.setQuantity(0);
            // 初始化为BigDecimal.ZERO，但不设置精度，避免累加过程中的精度损失
            newVo.setOriginalValue(BigDecimal.ZERO);
            newVo.setTotalDeprAmount(BigDecimal.ZERO);
            newVo.setNetValue(BigDecimal.ZERO);
            newVo.setCurrentDepreciation(BigDecimal.ZERO);
            return newVo;
        });

        // 累加数量和金额，不进行中间四舍五入
        if (vo.getQuantity() != null) {
            summaryVo.setQuantity(summaryVo.getQuantity() + vo.getQuantity());
        }
        if (vo.getOriginalValue() != null) {
            summaryVo.setOriginalValue(summaryVo.getOriginalValue().add(vo.getOriginalValue()));
        }
        if (vo.getTotalDeprAmount() != null) {
            summaryVo.setTotalDeprAmount(summaryVo.getTotalDeprAmount().add(vo.getTotalDeprAmount()));
        }
        if (vo.getNetValue() != null) {
            summaryVo.setNetValue(summaryVo.getNetValue().add(vo.getNetValue()));
        }
        if (vo.getCurrentDepreciation() != null) {
            summaryVo.setCurrentDepreciation(summaryVo.getCurrentDepreciation().add(vo.getCurrentDepreciation()));
        }
    }

}
