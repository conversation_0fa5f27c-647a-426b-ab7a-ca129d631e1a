package com.jp.med.erp.modules.vcrGen.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("ecs_reim_share_detail")
public class ErpReimShareEntity {

    /**
     * id
     */
    @TableField("id")
    private Integer id;

    /**
     * 报销明细id
     */
    @TableField("reim_detail_id")
    private Integer reimDetailId;

    /**
     * 分摊类型
     */
    @TableField("share_type")
    private String shareType;

    /**
     * 科室编码
     */
    @TableField("dept_code")
    private String deptCode;

    /**
     * 科室名称
     */
    @TableField("dept_name")
    private String deptName;

    /**
     * 金额
     */
    @TableField("amt")
    private BigDecimal amt;

    /**
     * 摘要
     */
    @TableField("abs")
    private String abs;

    @TableField(exist = false)
    private Double base;
}
