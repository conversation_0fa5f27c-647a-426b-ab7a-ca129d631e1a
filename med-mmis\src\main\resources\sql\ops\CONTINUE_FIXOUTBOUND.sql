-- =================================================================================================
-- 继续执行出库修复脚本 - 修复GROUP BY错误
-- 在当前事务中继续执行
-- =================================================================================================

-- 显示修复后的组织映射情况（修复GROUP BY错误）
SELECT
    '修复后组织映射' as mapping_result,
    t.out_org as org_name,
    t.out_target_org_id as org_id,
    o.org_name as verified_org_name,
    COUNT(*) as record_count,
    CASE
        WHEN o.org_id IS NOT NULL THEN '✅ 映射正确'
        ELSE '❌ 映射失败'
    END as mapping_status
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_org o ON t.out_target_org_id = o.org_id
    AND o.hospital_id = 'zjxrmyy'
    AND o.active_flag = '1'
WHERE t.out_org IS NOT NULL AND TRIM(t.out_org) != ''
GROUP BY t.out_org, t.out_target_org_id, o.org_name, o.org_id
ORDER BY record_count DESC;

-- 检查未成功映射的记录
SELECT
    '未成功映射的记录' as issue_type,
    out_org,
    out_target_org_id,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six
WHERE (out_org IS NOT NULL AND TRIM(out_org) != '')
  AND (out_target_org_id IS NULL OR TRIM(out_target_org_id) = '')
GROUP BY out_org, out_target_org_id
ORDER BY record_count DESC;

-- 删除临时表
DROP TABLE IF EXISTS temp_leaf_org_mapping;

-- 显示最终修复总结
SELECT
    '🎯 修复总结' as summary_type,
    '组织字段修复完成' as operation,
    CASE
        WHEN COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*) >= 95
        THEN '✅ 修复成功率 >= 95%'
        WHEN COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*) >= 80
        THEN '⚠️ 修复成功率 >= 80%'
        ELSE '❌ 修复成功率 < 80%，需要人工检查'
    END as result_status,
    ROUND(
        COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
    ) as success_rate
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL AND TRIM(out_org) != '';

-- 显示最终数据样例
SELECT
    '最终数据样例' as sample_type,
    out_org,
    out_target_org_id,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL
GROUP BY out_org, out_target_org_id
ORDER BY record_count DESC
LIMIT 10;

-- 提交事务
COMMIT;
