package com.jp.med.ams.modules.amsPropertyInAndOut.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsOutStockAuditReadMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.read.AmsOutStockAuditReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsOutStockAuditReadServiceImpl extends ServiceImpl<AmsOutStockAuditReadMapper, AmsOutStockAuditDto> implements AmsOutStockAuditReadService {

    @Autowired
    private AmsOutStockAuditReadMapper amsOutStockAuditReadMapper;

    @Override
    public List<AmsOutStockAuditDto> queryList(AmsOutStockAuditDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsOutStockAuditReadMapper.queryList(dto);
    }

    /**
     * 查询待审核数量
     *
     * @param dto
     * @return
     */
    @Override
    public Integer queryAuditCount(AmsOutStockAuditDto dto) {
        return amsOutStockAuditReadMapper.queryAuditCount(dto);
    }

    /**
     * 查询待出库数量
     *
     * @param dto
     * @return
     */
    @Override
    public Integer queryOutCount(AmsOutStockAuditDto dto) {
        return amsOutStockAuditReadMapper.queryOutCount(dto);

    }

}
