package com.jp.med.ams.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.config.dto.AmsDeprCfgDto;
import com.jp.med.ams.modules.config.vo.AmsDeprCfgVo;

import java.util.List;

/**
 * 资产折旧配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:09:03
 */
public interface AmsDeprCfgReadService extends IService<AmsDeprCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsDeprCfgVo> queryList(AmsDeprCfgDto dto);
}

