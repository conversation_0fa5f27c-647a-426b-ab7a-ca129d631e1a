package com.jp.med.ams.modules.changes.controller;

import com.jp.med.ams.modules.changes.dto.AmsTransferDto;
import com.jp.med.ams.modules.changes.service.read.AmsTransferReadService;
import com.jp.med.ams.modules.changes.service.write.AmsTransferWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 资产转移
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
@Api(value = "资产转移", tags = "资产转移")
@RestController
@RequestMapping("amsTransfer")
public class AmsTransferController {

    @Autowired
    private AmsTransferReadService amsTransferReadService;

    @Autowired
    private AmsTransferWriteService amsTransferWriteService;



    /**
     * 列表
     */
    @ApiOperation("查询资产转移")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsTransferDto dto) {
        return CommonResult.paging(amsTransferReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产转移")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsTransferDto dto) {
        amsTransferWriteService.saveTransfer(dto);
        return CommonResult.success();
    }

    /**
     * 更新
     */
    @ApiOperation("修改资产转移")
    @PostMapping("/updateData")
    public CommonResult<?> update(@RequestBody AmsTransferDto dto) {
        amsTransferWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产转移")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsTransferDto dto) {
        amsTransferWriteService.removeTransfer(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询转移详情")
    @PostMapping("/propertyDetail")
    public CommonResult<?> propertyDetail(@RequestBody AmsTransferDto dto){

        return CommonResult.success(amsTransferReadService.queryPropertyDetail(dto));
    }

    @ApiOperation("查询资产待审核数量")
    @PostMapping("/queryAuditCount")
    public CommonResult<?> queryAuditCount(@RequestBody AmsTransferDto dto){
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsTransferReadService.queryAuditCount(dto));
    }



}
