package com.jp.med.ams.modules.amsPropertyInAndOut.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Data
@TableName("ams_out_stock_config")
public class AmsOutStockConfigEntity {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 入库科室
     */
    @TableField("out_dept")
    private String outDept;

    /**
     * 字段
     */
    @TableField("fld")
    private String fld;

    /**
     * 是否必填(1：必填)
     */
    @TableField("mustl")
    private String mustl;

    /**
     * 字段类型（1：资产字段）
     */
    @TableField("fld_type")
    private String fldType;

}
