package com.jp.med.ams.modules.inventory.controller;


import com.jp.med.ams.modules.inventory.dto.AmsIntrDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.jp.med.ams.modules.inventory.dto.response.InventoryResponse;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrTodoWriteMapper;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrDetailReadService;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrReadService;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrTaskReadService;
import com.jp.med.ams.modules.inventory.service.write.AmsIntrWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("amsIntrShare")
public class AmsIntrShareController {

    @Autowired
    private AmsIntrTaskReadService amsIntrTaskReadService;

    @Autowired
    private AmsIntrWriteService amsIntrWriteService;

    @Autowired
    private AmsIntrDetailReadService amsIntrDetailReadService;

    @Autowired
    private AmsIntrReadService amsIntrReadService;

    @Autowired
    private AmsIntrTodoWriteMapper amsIntrTodoWriteMapper;


    /**
     * 列表
     */
    @ApiOperation("查询资产盘点任务")
    @PostMapping("/queryListNoPaging")
    public CommonResult<?> queryListNoPaging(@RequestBody AmsIntrTaskDto dto){
        return CommonResult.success(amsIntrTaskReadService.queryListNoPaging(dto));
    }

    @PostMapping("/insertIntr")
    public CommonResult<?> insertIntr(@RequestBody Map<String, Object> map){
        amsIntrWriteService.addIntr(map);
        return CommonResult.success();
    }

    @ApiOperation("获取最新盘点状态")
    @PostMapping("/getLatestStatus")
    public CommonResult<?> getLatestStatus(@RequestBody AmsIntrTodoDto dto) {
        try {
            Map<String, Object> status = amsIntrReadService.getLatestIntrStatus(dto);
            return CommonResult.success(InventoryResponse.success(status, "获取状态成功"));
        } catch (Exception e) {
//            return CommonResult.error(InventoryResponse.error("获取状态失败: " + e.getMessage()));
            return CommonResult.failed("获取状态失败");
        }
    }

    @ApiOperation("同步数据（增强版）")
    @PostMapping("/synchronousData")
    public synchronized CommonResult<?> synchronousData(@RequestBody AmsIntrTodoDto dto) {
        try {
            Map<String, Object> result = amsIntrWriteService.synchronousDataEnhanced(dto);
            String message = String.format("同步完成，新增%d个资产",
                    (Integer) result.getOrDefault("newCount", 0));
            return CommonResult.success(InventoryResponse.success(result, message));
        } catch (Exception e) {
//            return CommonResult.error(InventoryResponse.error("同步失败: " + e.getMessage(), "SYNC_ERROR"));
            return CommonResult.error("同步失败");
        }
    }

    @ApiOperation("开始扫码盘点")
    @PostMapping("/startIntr")
    public CommonResult<?> startIntr(@RequestBody AmsIntrDto dto) {
        try {
            AmsIntrDto result = amsIntrWriteService.startIntr(dto);
            String sessionId = String.valueOf(result.getId());
            return CommonResult.success(InventoryResponse.success(result, "盘点会话启动成功", sessionId));
        } catch (Exception e) {
//            return CommonResult.failed(InventoryResponse.error("启动盘点失败: " + e.getMessage(), "START_ERROR"));
            return CommonResult.failed("启动盘点失败");
        }
    }


    @ApiOperation("查询资产盘点任务")
    @PostMapping("/queryAsset")
    public CommonResult<?> queryAsset(@RequestBody AmsIntrTaskDto dto) {
        return CommonResult.success(amsIntrTaskReadService.queryIntr(dto));
    }

    @PutMapping("/update")
    public CommonResult<?> updateById(@RequestBody AmsIntrTodoDto dto) {
        amsIntrTodoWriteMapper.updateById(dto);

        return CommonResult.success();
    }


}
