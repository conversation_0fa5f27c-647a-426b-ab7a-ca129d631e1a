package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary2;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * 上月数据获取处理器
 * 负责计算上个月的期号并获取上月数据
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyLastMonthDataProcessor
        extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> {

    @Override
    protected void doProcess(AmsPropertyDeprSummary2Context context) {
        log.debug("开始处理上月数据获取");

        // 计算上个月的期号
        String lastMonthYm = getLastMonthYm(context.getCurrentYm());
        context.setLastMonthYm(lastMonthYm);

        log.debug("上月数据获取处理完成，当前月份: {}, 上月份: {}",
                context.getCurrentYm(), lastMonthYm);
    }

    /**
     * 计算上个月的年月字符串 (yyyyMM)
     *
     * @param currentYm 当前月份年月字符串 (yyyyMM)
     * @return 上个月的年月字符串 (yyyyMM)
     */
    private String getLastMonthYm(String currentYm) {
        try {
            YearMonth currentYearMonth = YearMonth.parse(currentYm, DateTimeFormatter.ofPattern("yyyyMM"));
            YearMonth lastMonth = currentYearMonth.minusMonths(1);
            return lastMonth.format(DateTimeFormatter.ofPattern("yyyyMM"));
        } catch (Exception e) {
            log.error("计算上个月期号失败，当前期号: {}", currentYm, e);
            throw new RuntimeException("计算上个月期号失败", e);
        }
    }
}
