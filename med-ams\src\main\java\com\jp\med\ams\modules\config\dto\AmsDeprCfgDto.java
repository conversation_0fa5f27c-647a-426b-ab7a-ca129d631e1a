package com.jp.med.ams.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 资产折旧配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:09:03
 */
@Data
@TableName("ams_depr_cfg" )
public class AmsDeprCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 折旧方法编码 */
    @TableField("depr_code")
    private String deprCode;

    /** 折旧方法名称 */
    @TableField("depr_name")
    private String deprName;

    /** 有效标志 */
    @TableField("flag")
    private String flag;

}
