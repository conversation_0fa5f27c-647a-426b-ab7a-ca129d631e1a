package com.jp.med.erp.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.erp.modules.config.mapper.read.ErpVcrSalaryConfigReadMapper;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo;
import com.jp.med.erp.modules.config.service.read.ErpVcrSalaryConfigReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class ErpVcrSalaryConfigReadServiceImpl extends ServiceImpl<ErpVcrSalaryConfigReadMapper, ErpVcrSalaryConfigDto> implements ErpVcrSalaryConfigReadService {

    @Autowired
    private ErpVcrSalaryConfigReadMapper erpVcrSalaryConfigReadMapper;

    @Override
    public List<ErpVcrSalaryConfigVo> queryList(ErpVcrSalaryConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return erpVcrSalaryConfigReadMapper.queryList(dto);
    }

    @Override
    public List<ErpVcrSalaryConfigVo> queryPageList(ErpVcrSalaryConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpVcrSalaryConfigReadMapper.queryList(dto);
    }

}
