package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 财政补助处理器
 * 负责按照开始使用时间区分是否为"财政补助资金"
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyFinaSubsidyProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理财政补助资金区分");

        // 如果需要计算财政补助，则进行处理
        if (context.shouldCalcFinaSubsidy()) {
            String finaSubsidyCode = context.getFinaSubsidyCode();
            String ym = context.getYm();

            // 添加往年财政补助资金映射
            context.getSourceCodeMap().put("wl" + finaSubsidyCode, "往年财政补助资金");

            // 处理普通资产和房屋维修资产
            processIsFinaSubsidy(context.getNormalAssets(), ym, finaSubsidyCode);
            processIsFinaSubsidy(context.getHouseRepairAssets(), ym, finaSubsidyCode);

            log.debug("财政补助资金区分处理完成");
        } else {
            log.debug("无需进行财政补助资金区分");
        }
    }

    /**
     * 按照开始使用时间区分是否为 "财政补助资金"
     *
     * @param assets          资产列表
     * @param ym              月份 ex:202502
     * @param finaSubsidyCode 财政补助资金代码
     */
    private void processIsFinaSubsidy(List<AmsPropertyVo> assets, String ym, String finaSubsidyCode) {
        // 获取当前年份
        String currentYear = ym.substring(0, 4);

        assets.stream()
                // 过滤为财政补助资金
                .filter(asset -> asset.getSource().equals(finaSubsidyCode))
                // 过滤为空
                .filter(asset -> getEffectiveDate(asset) != null)
                // 过滤为往年资产
                .filter(asset -> {
                    String assetYear = getEffectiveDate(asset).substring(0, 4);
                    return Integer.parseInt(assetYear) < Integer.parseInt(currentYear);
                })
                .forEach(asset -> {
                    asset.setIsFinaSubsidy(true);
                    // 设置资金来源
                    asset.setSource("wl" + asset.getSource());
                });
    }

    /**
     * 获取有效日期，优先使用购买日期(purcDate)，为空时才使用启用日期(openingDate)
     *
     * @param asset 资产对象
     * @return 有效日期字符串，格式为yyyyMMdd
     */
    private String getEffectiveDate(AmsPropertyVo asset) {
        if (asset.getPurcDate() != null && !asset.getPurcDate().isEmpty()) {
            return asset.getPurcDate();
        }
        return asset.getOpeningDate();
    }
}
