package com.jp.med.ams.modules.it.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItInvtSumDto;
import com.jp.med.ams.modules.it.service.read.AmsItInvtSumReadService;
import com.jp.med.ams.modules.it.service.write.AmsItInvtSumWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 耗材库存汇总
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Api(value = "耗材库存汇总", tags = "耗材库存汇总")
@RestController
@RequestMapping("amsItInvtSum")
public class AmsItInvtSumController {

    @Autowired
    private AmsItInvtSumReadService amsItInvtSumReadService;

    @Autowired
    private AmsItInvtSumWriteService amsItInvtSumWriteService;

    /**
     * 获取当前申请项库存耗材型号详细数据
     */
    @ApiOperation("获取当前申请项库存耗材型号详细数据")
    @PostMapping("/modelList")
    public CommonResult<?> modelList(@RequestBody AmsItInvtSumDto dto){
        return CommonResult.success(amsItInvtSumReadService.modelList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询耗材库存汇总")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItInvtSumDto dto){
        return CommonResult.paging(amsItInvtSumReadService.queryList(dto));
    }

    /**
     * 耗材告警列表
     */
    @ApiOperation("查询耗材库存阈值列表")
    @PostMapping("/alarmlist")
    public CommonResult<?> alarmlist(@RequestBody AmsItInvtSumDto dto){
        return CommonResult.paging(amsItInvtSumReadService.queryAlarmList(dto));
    }
    /**
     * 耗材告警列表: 返回中文
     */
    @ApiOperation("查询耗材库存阈值列表")
    @PostMapping("/alarmlistDetial")
    public CommonResult<?> alarmlistDetial(@RequestBody AmsItInvtSumDto dto){
        return CommonResult.paging(amsItInvtSumReadService.queryAlarmListDetial(dto));
    }
    /**
     * 耗材告警列表
     */
    @ApiOperation("查询耗材库存阈值列表")
    @PostMapping("/checkedAlarmList")
    public CommonResult<?> checkedAlarmList(@RequestBody AmsItInvtSumDto dto){
        return CommonResult.paging(amsItInvtSumReadService.queryCheckedAlarmList(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增耗材库存汇总")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsItInvtSumDto dto){
        amsItInvtSumWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改耗材库存汇总")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItInvtSumDto dto){
        amsItInvtSumWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除耗材库存汇总")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItInvtSumDto dto){
        amsItInvtSumWriteService.removeById(dto);
        return CommonResult.success();
    }

}
