package com.jp.med.ams.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.config.mapper.write.AmsDeprCfgWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsDeprCfgDto;
import com.jp.med.ams.modules.config.service.write.AmsDeprCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资产折旧配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:09:03
 */
@Service
@Transactional(readOnly = false)
public class AmsDeprCfgWriteServiceImpl extends ServiceImpl<AmsDeprCfgWriteMapper, AmsDeprCfgDto> implements AmsDeprCfgWriteService {
}
