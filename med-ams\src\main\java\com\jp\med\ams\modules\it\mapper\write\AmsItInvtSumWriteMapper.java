package com.jp.med.ams.modules.it.mapper.write;

import com.jp.med.ams.modules.it.dto.AmsItInvtSumDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 耗材库存汇总
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItInvtSumWriteMapper extends BaseMapper<AmsItInvtSumDto> {
    void batchUpdateById(List<AmsItInvtSumDto> amsItInvtSumDtos);

    void updateInvtSum(AmsItInvtSumDto sumDto);
}
