package com.jp.med.erp.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.service.read.ErpReimSalaryTaskDetailReadService;
import com.jp.med.erp.modules.config.service.write.ErpReimSalaryTaskDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 应发工资报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Api(value = "应发工资报销详情", tags = "应发工资报销详情")
@RestController
@RequestMapping("erpReimSalaryTaskDetail")
public class ErpReimSalaryTaskDetailController {

    @Autowired
    private ErpReimSalaryTaskDetailReadService erpReimSalaryTaskDetailReadService;

    @Autowired
    private ErpReimSalaryTaskDetailWriteService erpReimSalaryTaskDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询应发工资报销详情")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody ErpReimSalaryTaskDetailDto dto){
        return CommonResult.paging(erpReimSalaryTaskDetailReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询应发工资报销详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody ErpReimSalaryTaskDetailDto dto){
        return CommonResult.success(erpReimSalaryTaskDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增应发工资报销详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody ErpReimSalaryTaskDetailDto dto){
        erpReimSalaryTaskDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改应发工资报销详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody ErpReimSalaryTaskDetailDto dto){
        erpReimSalaryTaskDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除应发工资报销详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody ErpReimSalaryTaskDetailDto dto){
        erpReimSalaryTaskDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
