package com.jp.med.erp.modules.vcrGen.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 凭证信息明细
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Data
@TableName("erp_vcr_item_detail" )
public class ErpVcrItemDetailDto extends CommonQueryDto {


    @TableId("id")
    private Integer id;

    /** 分录号 */
    @TableField("flh")
    private Integer flh;

    /** 凭证号id,u8生成 */
    @TableField("idpzh")
    private String idpzh;

    /** 凭证内容id,u8生成 */
    @TableField("idpznr")
    private String idpznr;

    /** 借贷标志 */
    @TableField("jdbz")
    private String jdbz;

    /** 明细金额 */
    @TableField("je")
    private BigDecimal je;

    /** 凭证号 */
    @TableField("pzh")
    private String pzh;

    /** 凭证id,此id为hrp定下的唯一id */
    @TableField("pzid")
    private String pzid;
}
