package com.jp.med.erp.modules.config.mapper.read;

import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Mapper
public interface ErpReimSalaryTaskReadMapper extends BaseMapper<ErpReimSalaryTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpReimSalaryTaskVo> queryList(ErpReimSalaryTaskDto dto);
}
