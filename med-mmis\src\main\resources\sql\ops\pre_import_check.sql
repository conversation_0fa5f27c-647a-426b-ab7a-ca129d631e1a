-- =================================================================================================
-- 导入前预检查脚本：在执行导入脚本前进行全面检查
-- 功能：确保导入环境准备就绪，识别潜在的导入风险
-- 建议：在执行 xinxike_new_storage_import.sql 和 xinxike_new_outbound_import.sql 前运行
-- =================================================================================================

-- =================================================================================================
-- 第一部分：环境检查 🔧
-- =================================================================================================

-- 1.1 检查目标表是否存在
SELECT 
    '🎯 目标表检查' as check_category,
    table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = t.table_name)
        THEN '✅ 存在'
        ELSE '❌ 不存在'
    END as status
FROM (
    VALUES 
        ('mmis_aset_storage'),
        ('mmis_aset_storage_detail'),
        ('mmis_outbound_apply'),
        ('mmis_outbound_apply_details'),
        ('mmis_aset_info_assist'),
        ('hrm_employee_info'),
        ('hrm_org')
) AS t(table_name);

-- 1.2 检查源表是否存在
SELECT 
    '📥 源表检查' as check_category,
    table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = t.table_name)
        THEN '✅ 存在'
        ELSE '❌ 不存在'
    END as status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = t.table_name)
        THEN (
            CASE t.table_name
                WHEN 'mmis_temp_inport_storage_six' THEN (SELECT COUNT(*) FROM mmis_temp_inport_storage_six)
                WHEN 'mmis_temp_xinxike_outbound_six' THEN (SELECT COUNT(*) FROM mmis_temp_xinxike_outbound_six)
                ELSE 0
            END
        )
        ELSE 0
    END as record_count
FROM (
    VALUES 
        ('mmis_temp_inport_storage_six'),
        ('mmis_temp_xinxike_outbound_six')
) AS t(table_name);

-- =================================================================================================
-- 第二部分：数据完整性检查 📊
-- =================================================================================================

-- 2.1 入库源表数据完整性
SELECT 
    '📦 入库数据完整性' as check_category,
    metric,
    value,
    CASE 
        WHEN metric = '总记录数' AND value = 0 THEN '❌ 无数据'
        WHEN metric = '有效记录比例' AND value < 80 THEN '⚠️ 数据质量较低'
        WHEN metric = '有效记录比例' AND value >= 80 THEN '✅ 数据质量良好'
        ELSE '✅ 正常'
    END as status
FROM (
    SELECT '总记录数' as metric, COUNT(*)::text as value
    FROM mmis_temp_inport_storage_six
    UNION ALL
    SELECT '有效记录数' as metric, 
           COUNT(CASE WHEN name IS NOT NULL AND TRIM(name) != '' 
                      AND supplier_name IS NOT NULL AND TRIM(supplier_name) != ''
                      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
                 THEN 1 END)::text as value
    FROM mmis_temp_inport_storage_six
    UNION ALL
    SELECT '有效记录比例' as metric,
           CASE 
               WHEN COUNT(*) = 0 THEN '0'
               ELSE ROUND(
                   COUNT(CASE WHEN name IS NOT NULL AND TRIM(name) != '' 
                              AND supplier_name IS NOT NULL AND TRIM(supplier_name) != ''
                              AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
                         THEN 1 END) * 100.0 / COUNT(*), 2
               )::text
           END as value
    FROM mmis_temp_inport_storage_six
) sub;

-- 2.2 出库源表数据完整性
SELECT 
    '📤 出库数据完整性' as check_category,
    metric,
    value,
    CASE 
        WHEN metric = '总记录数' AND value = '0' THEN '❌ 无数据'
        WHEN metric = '有效记录比例' AND value::numeric < 80 THEN '⚠️ 数据质量较低'
        WHEN metric = '有效记录比例' AND value::numeric >= 80 THEN '✅ 数据质量良好'
        ELSE '✅ 正常'
    END as status
FROM (
    SELECT '总记录数' as metric, COUNT(*)::text as value
    FROM mmis_temp_xinxike_outbound_six
    UNION ALL
    SELECT '有效记录数' as metric, 
           COUNT(CASE WHEN name IS NOT NULL AND TRIM(name) != '' 
                      AND out_org IS NOT NULL AND TRIM(out_org) != ''
                      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
                 THEN 1 END)::text as value
    FROM mmis_temp_xinxike_outbound_six
    UNION ALL
    SELECT '有效记录比例' as metric,
           CASE 
               WHEN COUNT(*) = 0 THEN '0'
               ELSE ROUND(
                   COUNT(CASE WHEN name IS NOT NULL AND TRIM(name) != '' 
                              AND out_org IS NOT NULL AND TRIM(out_org) != ''
                              AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
                         THEN 1 END) * 100.0 / COUNT(*), 2
               )::text
           END as value
    FROM mmis_temp_xinxike_outbound_six
) sub;

-- =================================================================================================
-- 第三部分：关联数据检查 🔗
-- =================================================================================================

-- 3.1 物资信息匹配度检查
SELECT 
    '🔗 物资信息匹配度' as check_category,
    source_table,
    total_materials,
    matched_materials,
    ROUND(matched_materials * 100.0 / NULLIF(total_materials, 0), 2) as match_percentage,
    CASE 
        WHEN total_materials = 0 THEN '❌ 无物资数据'
        WHEN matched_materials * 100.0 / total_materials >= 80 THEN '✅ 匹配度良好'
        WHEN matched_materials * 100.0 / total_materials >= 60 THEN '⚠️ 匹配度一般'
        ELSE '❌ 匹配度较低'
    END as status
FROM (
    SELECT 
        '入库源表' as source_table,
        COUNT(DISTINCT t.name) as total_materials,
        COUNT(DISTINCT CASE WHEN a.name IS NOT NULL THEN t.name END) as matched_materials
    FROM mmis_temp_inport_storage_six t
    LEFT JOIN mmis_aset_info_assist a ON t.name = a.name
    WHERE t.name IS NOT NULL AND TRIM(t.name) != ''
    UNION ALL
    SELECT 
        '出库源表' as source_table,
        COUNT(DISTINCT t.name) as total_materials,
        COUNT(DISTINCT CASE WHEN a.name IS NOT NULL THEN t.name END) as matched_materials
    FROM mmis_temp_xinxike_outbound_six t
    LEFT JOIN mmis_aset_info_assist a ON t.name = a.name
    WHERE t.name IS NOT NULL AND TRIM(t.name) != ''
) sub;

-- 3.2 员工信息匹配度检查（仅出库）
SELECT 
    '👥 员工信息匹配度' as check_category,
    COUNT(DISTINCT out_emp) as total_employees,
    COUNT(DISTINCT CASE WHEN e.emp_code IS NOT NULL THEN out_emp END) as matched_employees,
    ROUND(COUNT(DISTINCT CASE WHEN e.emp_code IS NOT NULL THEN out_emp END) * 100.0 / 
          NULLIF(COUNT(DISTINCT out_emp), 0), 2) as match_percentage,
    CASE 
        WHEN COUNT(DISTINCT out_emp) = 0 THEN '❌ 无员工数据'
        WHEN COUNT(DISTINCT CASE WHEN e.emp_code IS NOT NULL THEN out_emp END) * 100.0 / COUNT(DISTINCT out_emp) >= 80 
        THEN '✅ 匹配度良好'
        WHEN COUNT(DISTINCT CASE WHEN e.emp_code IS NOT NULL THEN out_emp END) * 100.0 / COUNT(DISTINCT out_emp) >= 60 
        THEN '⚠️ 匹配度一般'
        ELSE '❌ 匹配度较低'
    END as status
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_employee_info e ON t.out_emp = e.emp_code 
    AND e.hospital_id = 'zjxrmyy' AND e.is_deleted = 0
WHERE t.out_emp IS NOT NULL AND TRIM(t.out_emp) != '';

-- 3.3 科室信息匹配度检查（仅出库）
SELECT 
    '🏢 科室信息匹配度' as check_category,
    COUNT(DISTINCT out_org) as total_orgs,
    COUNT(DISTINCT CASE WHEN o.org_name IS NOT NULL THEN out_org END) as matched_orgs,
    ROUND(COUNT(DISTINCT CASE WHEN o.org_name IS NOT NULL THEN out_org END) * 100.0 / 
          NULLIF(COUNT(DISTINCT out_org), 0), 2) as match_percentage,
    CASE 
        WHEN COUNT(DISTINCT out_org) = 0 THEN '❌ 无科室数据'
        WHEN COUNT(DISTINCT CASE WHEN o.org_name IS NOT NULL THEN out_org END) * 100.0 / COUNT(DISTINCT out_org) >= 80 
        THEN '✅ 匹配度良好'
        WHEN COUNT(DISTINCT CASE WHEN o.org_name IS NOT NULL THEN out_org END) * 100.0 / COUNT(DISTINCT out_org) >= 60 
        THEN '⚠️ 匹配度一般'
        ELSE '❌ 匹配度较低'
    END as status
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_org o ON t.out_org = o.org_name 
    AND o.hospital_id = 'zjxrmyy' AND o.active_flag = '1'
WHERE t.out_org IS NOT NULL AND TRIM(t.out_org) != '';

-- =================================================================================================
-- 第四部分：导入风险评估 ⚠️
-- =================================================================================================

-- 4.1 综合风险评估
WITH risk_assessment AS (
    SELECT 
        CASE 
            WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_inport_storage_six')
              OR NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_xinxike_outbound_six')
            THEN '🚫 高风险'
            WHEN (SELECT COUNT(*) FROM mmis_temp_inport_storage_six) = 0 
             AND (SELECT COUNT(*) FROM mmis_temp_xinxike_outbound_six) = 0
            THEN '🚫 高风险'
            WHEN EXISTS (
                SELECT 1 FROM mmis_temp_inport_storage_six 
                WHERE name IS NOT NULL AND supplier_name IS NOT NULL
                LIMIT 1
            ) OR EXISTS (
                SELECT 1 FROM mmis_temp_xinxike_outbound_six 
                WHERE name IS NOT NULL AND out_org IS NOT NULL
                LIMIT 1
            )
            THEN '✅ 低风险'
            ELSE '⚠️ 中风险'
        END as risk_level,
        CASE 
            WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_inport_storage_six')
              OR NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_xinxike_outbound_six')
            THEN '源表不存在，无法执行导入'
            WHEN (SELECT COUNT(*) FROM mmis_temp_inport_storage_six) = 0 
             AND (SELECT COUNT(*) FROM mmis_temp_xinxike_outbound_six) = 0
            THEN '源表无数据，导入无意义'
            WHEN EXISTS (
                SELECT 1 FROM mmis_temp_inport_storage_six 
                WHERE name IS NOT NULL AND supplier_name IS NOT NULL
                LIMIT 1
            ) OR EXISTS (
                SELECT 1 FROM mmis_temp_xinxike_outbound_six 
                WHERE name IS NOT NULL AND out_org IS NOT NULL
                LIMIT 1
            )
            THEN '数据质量良好，可以安全导入'
            ELSE '数据质量需要进一步检查'
        END as recommendation
)
SELECT 
    '🎯 导入风险评估' as assessment_type,
    risk_level,
    recommendation
FROM risk_assessment;
