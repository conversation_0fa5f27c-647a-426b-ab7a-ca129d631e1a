package com.jp.med.ams.modules.amsPropertyInAndOut.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write.AmsOutStockAuditWriteMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.write.AmsOutStockAuditWriteService;
import com.jp.med.ams.modules.common.service.write.impl.AmsAuditServiceImpl;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.util.OSSUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email -
 * @date 2024-05-27 20:15:50
 */
@Service
@Transactional(readOnly = false)
public class AmsOutStockAuditWriteServiceImpl extends ServiceImpl<AmsOutStockAuditWriteMapper, AmsOutStockAuditDto> implements AmsOutStockAuditWriteService {

    private final OSSUtil oSSUtil;
    @Resource
    private AmsOutStockAuditWriteMapper writeMapper;
    @Resource
    private AmsAuditServiceImpl amsAuditService;

    public AmsOutStockAuditWriteServiceImpl(OSSUtil oSSUtil) {
        this.oSSUtil = oSSUtil;
    }

    /**
     * 批量审批
     *
     * @param dto
     * @return
     */
    @Override
    public Object batchApply(AmsOutStockAuditDto dto) {
        List<Long> batchAuditIds = dto.getBatchAuditIds();
        MultipartFile signFile = dto.getSignFile();
        String signUrl = OSSUtil.uploadFile(OSSConst.BUCKET_AMS, "audit/", signFile);
        dto.setSignFileUrl(signUrl);
        AmsOutStockAuditDto applyDto = new AmsOutStockAuditDto();
        applyDto.setType(Optional.of(dto.getType()).orElse("1"));
        applyDto.setSignFileUrl(signUrl);
        for (Long auditId : batchAuditIds) {

            AmsOutStockAuditDto query = writeMapper.queryFirstAuditRcdfmId(auditId);
            boolean lastNode = writeMapper.isLastNode(query);
            if (query.getId() != null) {
                applyDto.setId(query.getId());
                //更新审批记录+签名
                Long l = writeMapper.batchApply(applyDto);
                if (l == 1) {
                    if (lastNode) {
                        //如果是最后一条审批记录那么更新为通过并且触发状态更新
                        writeMapper.updateAuditStatus(query.getBchno(), "1");
                        AuditDetail auditDetail = new AuditDetail(query.getBchno(), AuditConst.STATE_COMPLETE);
                        amsAuditService.complete(auditDetail);
                    } else {
                        writeMapper.updateAuditStatus(query.getBchno(), "3");
                        AuditDetail auditDetail = new AuditDetail(query.getBchno(), AuditConst.STATE_SUCCESS);
                        amsAuditService.complete(auditDetail);
                    }

                }

            }
        }

        return null;
    }
}
