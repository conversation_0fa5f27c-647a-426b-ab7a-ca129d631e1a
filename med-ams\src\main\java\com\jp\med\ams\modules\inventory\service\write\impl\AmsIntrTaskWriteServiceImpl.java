package com.jp.med.ams.modules.inventory.service.write.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrDetailReadMapper;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrTaskReadMapper;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrTaskWriteMapper;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrTodoWriteMapper;
import com.jp.med.ams.modules.inventory.service.write.AmsIntrTaskWriteService;
import com.jp.med.ams.modules.inventory.vo.AmsIntrDetailVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import com.jp.med.common.util.BatchUtil;

/**
 * 资产盘点任务写服务实现类
 * <p>
 * 处理资产盘点任务及其关联待办事项的创建、删除和完成操作。
 * </p>
 *
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 10:12:34
 */
@Service
@Transactional(readOnly = false)
public class AmsIntrTaskWriteServiceImpl extends ServiceImpl<AmsIntrTaskWriteMapper, AmsIntrTaskDto>
        implements AmsIntrTaskWriteService {

    @Autowired
    private AmsIntrTaskWriteMapper amsIntrTaskWriteMapper;

    @Autowired
    private AmsIntrTodoWriteMapper amsIntrTodoWriteMapper;
    @Qualifier("amsIntrDetailReadMapper")
    @Autowired
    private AmsIntrDetailReadMapper amsIntrDetailReadMapper;
    @Qualifier("amsIntrTaskReadMapper")
    @Autowired
    private AmsIntrTaskReadMapper amsIntrTaskReadMapper;

    /**
     * 保存新的盘点任务及其待办事项。
     * <p>
     * 1. 设置创建人、激活标志和创建时间。
     * 2. 插入盘点任务记录 (`ams_intr_task`)。
     * 3. 获取插入后生成的任务 ID。
     * 4. 遍历待办事项列表 (`dto.list`)，设置每个待办事项关联的任务 ID。
     * 5. 批量插入待办事项记录 (`ams_intr_todo`)。
     * </p>
     *
     * @param dto 包含盘点任务信息和待办事项列表的 DTO。
     * @return 总是返回 `true`。
     */
    @Override
    public boolean save(AmsIntrTaskDto dto) {
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setFlag("1");
        dto.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        amsIntrTaskWriteMapper.insert(dto);
        List<AmsIntrTodoDto> list = dto.getList();
        list.forEach(item -> {
            item.setTaskId(dto.getId());
            item.setId(null);
        });
        BatchUtil.batch(list, AmsIntrTodoWriteMapper.class);
        return true;
    }

    /**
     * 根据 ID 删除盘点任务及其关联的待办事项。
     * <p>
     * 1. 根据 `dto.id` 删除盘点任务记录 (`ams_intr_task`)。
     * 2. 根据任务 ID (`dto.id`) 删除所有关联的待办事项记录 (`ams_intr_todo`)。
     * </p>
     *
     * @param dto 包含要删除的盘点任务 ID 的 DTO。
     * @return 总是返回 `true`。
     */
    @Override
    public boolean removeById(AmsIntrTaskDto dto) {
        amsIntrTaskWriteMapper.deleteById(dto);
        AmsIntrTodoDto amsIntrTodoDto = new AmsIntrTodoDto();
        amsIntrTodoDto.setTaskId(dto.getId());
        amsIntrTodoWriteMapper.deleteByTaskId(amsIntrTodoDto);
        return true;
    }

    /**
     * 完成指定的盘点任务，并更新相关资产状态。
     * <p>
     * 1. 遍历传入的任务 ID 列表 (`dto.ids`)。
     * 2. 对每个任务 ID：
     * a. 创建一个 `AmsIntrTaskDto` 对象，设置 ID 和状态为 '1' (已完成)。
     * b. 查询该任务关联的盘点明细中涉及的资产 (`amsIntrTaskReadMapper.queryIntr`)。
     * c. 将这些资产的减少方式 (`redcWay`) 设置为 '2002' (盘亏)。
     * d. 批量更新这些资产的信息 (`ams_property`)。
     * 3. 批量更新所有指定任务的状态为已完成 (`ams_intr_task`)。
     * </p>
     *
     * @param dto 包含要完成的任务 ID 列表 (`ids`) 的 DTO。
     */
    @Override
    public void compltetIntr(AmsIntrTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<Integer> ids = dto.getIds();
        List<AmsIntrTaskDto> list = new ArrayList<>();
        for (Integer id : ids) {
            AmsIntrTaskDto taskDto = new AmsIntrTaskDto();
            taskDto.setId(id);
            taskDto.setStatus("1");
            list.add(taskDto);

            /// 设置资产减少方式为盘亏
            List<AmsIntrDetailVo> amsIntrDetailVos = amsIntrTaskReadMapper.queryIntr(dto);
            List<AmsPropertyDto> propertyList = amsIntrDetailVos.stream().map(item -> {
                Integer assetId = item.getAssetId();
                AmsPropertyDto amsPropertyDto = new AmsPropertyDto();
                amsPropertyDto.setId(assetId);
                amsPropertyDto.setRedcWay("2002");// 盘亏

                return amsPropertyDto;
            }).collect(Collectors.toList());
            BatchUtil.batch("updateById", propertyList, AmsPropertyWriteMapper.class);
        }

        BatchUtil.batch("updateById", list, AmsIntrTaskWriteMapper.class);

    }
}
