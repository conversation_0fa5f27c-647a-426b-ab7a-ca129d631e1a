package com.jp.med.ams.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.config.dto.AmsCfgCcmdDto;
import com.jp.med.ams.modules.config.service.read.AmsCfgCcmdReadService;
import com.jp.med.ams.modules.config.service.write.AmsCfgCcmdWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 *
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 09:35:21
 */
@Api(value = "医疗器械分类目录", tags = "医疗器械分类目录")
@RestController
@RequestMapping("amsCfgCcmd")
public class AmsCfgCcmdController {

    @Autowired
    private AmsCfgCcmdReadService amsCfgCcmdReadService;

    @Autowired
    private AmsCfgCcmdWriteService amsCfgCcmdWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsCfgCcmdDto dto){
        return CommonResult.success(amsCfgCcmdReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsCfgCcmdDto dto){
        amsCfgCcmdWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsCfgCcmdDto dto){
        amsCfgCcmdWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsCfgCcmdDto dto){
        amsCfgCcmdWriteService.removeById(dto);
        return CommonResult.success();
    }

}
