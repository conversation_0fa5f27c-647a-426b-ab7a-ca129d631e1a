package com.jp.med.ams.modules.inventory.mapper.read;

import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.jp.med.ams.modules.inventory.vo.AmsIntrDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 盘点数据明细表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Mapper
public interface AmsIntrDetailReadMapper extends BaseMapper<AmsIntrDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsIntrDetailVo> queryList(AmsIntrDetailDto dto);

    /**
     * 查询待盘点的资产
     * @param dto
     * @return
     */
    List<AmsIntrDetailVo> queryToDoDetail(AmsIntrTodoDto dto);
}
