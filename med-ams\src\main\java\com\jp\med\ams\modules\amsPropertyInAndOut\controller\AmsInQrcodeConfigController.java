package com.jp.med.ams.modules.amsPropertyInAndOut.controller;

import cn.hutool.core.util.StrUtil;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInQrcodeConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsInQrcodeConfigReadMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.read.AmsInQrcodeConfigReadService;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.write.AmsInQrcodeConfigWriteService;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInQrcodeConfigVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 资产入库二维码配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
@Api(value = "资产入库二维码配置", tags = "资产入库二维码配置")
@RestController
@RequestMapping("amsInQrcodeConfig")
public class AmsInQrcodeConfigController {

    @Autowired
    private AmsInQrcodeConfigReadService amsInQrcodeConfigReadService;

    @Autowired
    private AmsInQrcodeConfigWriteService amsInQrcodeConfigWriteService;


    @Autowired
    private AmsInQrcodeConfigReadMapper amsInQrcodeConfigReadMapper;

    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    /**
     * 列表
     */
    @ApiOperation("查询资产入库二维码配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsInQrcodeConfigDto dto) {
        return CommonResult.paging(amsInQrcodeConfigReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产入库二维码配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsInQrcodeConfigDto dto) {
        amsInQrcodeConfigWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产入库二维码配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsInQrcodeConfigDto dto) {
        dateValid(dto);
        amsInQrcodeConfigWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产入库二维码配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsInQrcodeConfigDto dto) {
        amsInQrcodeConfigWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("生成第三方入库二维码")
    @PostMapping("/generateQrCode")
    public CommonResult<?> generateQrCode(@RequestBody AmsInQrcodeConfigDto dto) throws IOException {
        dateValid(dto);
        dto.setHospitalId("zjxrmyy");
        amsInQrcodeConfigWriteService.generateQrCode(dto);
        return CommonResult.success();
    }

    private void dateValid(@RequestBody AmsInQrcodeConfigDto dto) {
        if (dto.getValidTime() != null && dto.getValidTime().size() == 2) {
            dto.setStartDate(dto.getValidTime().get(0));
            dto.setEndDate(dto.getValidTime().get(1));
            if (dto.getStartDate().after(dto.getEndDate())) {
                throw new AppException("开始日期不能大于结束日期");
            }
        }
    }

    @ApiOperation("扫描二维码重定向入库页面+token登录")
    @PostMapping("/scanQrCode/")
    public CommonResult<?> scanQrCode(String qrId) {
//        ossUtil.uploadFile(Co)
        if (StringUtils.isEmpty(qrId)) {
            throw new AppException("二维码不存在");
        }
        AmsInQrcodeConfigVo amsInQrcodeConfigVo = amsInQrcodeConfigReadMapper.queryOneByQrIdAms(qrId);
        if (amsInQrcodeConfigVo == null) {
            throw new AppException("二维码不存在");
        }
        Date startDate = amsInQrcodeConfigVo.getStartDate();
        if (startDate != null) {
            Date now = new Date();
            if (now.before(startDate)) {
                throw new AppException("二维码未生效");
            }
        }
        if (amsInQrcodeConfigVo.getEndDate() != null) {
            Date now = new Date();
            if (now.after(amsInQrcodeConfigVo.getEndDate())) {
                throw new AppException("二维码已失效");
            }
        }
        return CommonResult.success(amsInQrcodeConfigVo);
    }

    @ApiOperation("查看使用二维码入库的资产")
    @PostMapping("/queryUserInQrCodePropertyByQrId")
    public CommonResult<?> queryInQrCodeAssets(@RequestBody AmsInQrcodeConfigDto dto) {
        if (StrUtil.isBlank(dto.getQrId())) {
            throw new AppException("二维码不存在");
        }
        List<AmsOutStockAuditDto> amsOutStockAuditDtos = amsInQrcodeConfigReadMapper.queryUserInQrCodePropertyByQrId(dto.getQrId());

        if (!amsOutStockAuditDtos.isEmpty()) {
            List<Integer> ids = amsOutStockAuditDtos.stream().map(AmsOutStockAuditDto::getPropertyId).collect(Collectors.toList());
            AmsPropertyDto amsPropertyDto = new AmsPropertyDto();
            amsPropertyDto.setIds(ids);
            List<AmsPropertyVo> amsPropertyVos = amsPropertyReadMapper.queryMainList(amsPropertyDto);
            return CommonResult.success(amsPropertyVos);
        }
        return CommonResult.success(List.of());
    }
}
