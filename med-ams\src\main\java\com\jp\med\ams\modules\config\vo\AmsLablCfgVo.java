package com.jp.med.ams.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产标签配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 17:36:48
 */
@Data
public class AmsLablCfgVo {

	/** ID */
	private Integer id;

	/** 资产类型 */
	private String assetCardName;

	/** 附件 */
	private String attachment;

	/** 创建时间 */
	private String createTime;

	/** 创建人 */
	private String crter;

	/** 创建人(姓名) */
	private String empName;

	/** 有效标志 */
	private String flag;

	/**医疗机构编号 */
	private String hospitalId;

	/**图片*/
	private String img;

	/** 图片路径*/
	private String imgPath;

	/** 附件路径 */
	private String attachmentPath;

}
