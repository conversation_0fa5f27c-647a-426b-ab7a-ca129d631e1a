package com.jp.med.ams.modules.inventory.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;

/**
 * 盘点数据明细表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
public interface AmsIntrDetailWriteService extends IService<AmsIntrDetailDto> {

    /**
     * 手工盘点
     * @param dto
     */
    void intr(AmsIntrDetailDto dto);

    void deleteProfit(AmsIntrDetailDto dto);

    void cleanIntr(AmsIntrDetailDto dto);
}

