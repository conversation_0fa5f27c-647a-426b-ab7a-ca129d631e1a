package com.jp.med.ams.modules.config.service.read;

import com.jp.med.ams.modules.config.dto.AmsStockCfgDto;
import com.jp.med.ams.modules.config.vo.AmsStockCfgVo;

import java.util.List;

public interface AmsStockCfgReadService {
    /**
     * 查询表格列
     * @return
     */
    List<AmsStockCfgVo> queryTableColumn();

    /**
     * 查询列表
     * @param dto
     * @return
     */
    List<AmsStockCfgVo> queryList(AmsStockCfgDto dto);

    /**
     * 查询卡片
     * @param dto
     * @return
     */
    List<AmsStockCfgVo> queryCard(AmsStockCfgDto dto);
}
