package com.jp.med.erp.modules.vcrGen.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 凭证信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Data
@TableName("erp_vcr_detail" )
public class ErpVcrDetailDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 凭证号id,u8生成 */
    @TableField("idpzh")
    private String idpzh;

    /** 会计期间 */
    @TableField("kjqj")
    private String kjqj;

    /** 凭证号 */
    @TableField("pzh")
    private String pzh;

    /** 财务凭证金额 */
    @TableField("pzje")
    private BigDecimal pzje;

    /** 预算凭证金额 */
    @TableField("yspzje")
    private BigDecimal yspzje;

    /** 凭证状态 1.仅保存 2.已生成 */
    @TableField("status")
    private String status;

    /** 凭证生成标志 */
    @TableField(exist = false)
    private String genFlag;

    /** 凭证明细 */
    @TableField(exist = false)
    private List<ErpVcrItemDetailDto> detail;


    @TableField(exist = false)
    private List<Integer> ids;

    /**
     * 生成凭证报销类型
     */
    @TableField(exist = false)
    private String type;

    /**
     * 有效标志
     */
    @TableField(exist = false)
    private String activeFlag;

    @TableField(exist = false)
    private String supType;

    /**
     * 支付方式  1：现金
     */
    @TableField(exist = false)
    private String payMethod;

}
