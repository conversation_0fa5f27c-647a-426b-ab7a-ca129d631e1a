package com.jp.med.ams.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.config.dto.AmsTypeCfgDto;
import com.jp.med.ams.modules.config.vo.AmsTypeCfgVo;

import java.util.List;

/**
 * 资产类型配表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 14:10:05
 */
public interface AmsTypeCfgReadService extends IService<AmsTypeCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsTypeCfgVo> queryList(AmsTypeCfgDto dto);
}

