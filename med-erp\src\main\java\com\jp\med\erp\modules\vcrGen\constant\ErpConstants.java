package com.jp.med.erp.modules.vcrGen.constant;

public interface ErpConstants {

    //--------------------start凭证来源------------------
    /** 报销 **/
    String VCR_SOURCE_REIM = "1";
    /** 药品报销 **/
    String VCR_SOURCE_DRUG_REIM = "2";

    /** 工资 **/
    String VCR_SOURCE_SALARY = "3";

    /** 折旧 **/
    String VCR_SOURCE_DEPR = "4";
    //--------------------end凭证来源--------------------

    //----------------应发工资start----------------
    //岗位工资
    String POST_SALARY = "postSalary";
    // 薪级工资
    String SAL_GRADE_SALARY = "salGradeSalary";
    // 护士 10%
    String NURSE_SALARY = "nurseSalary";
    // 地区附加津贴
    String AREA_SALARY = "areaSalary";
    // 护龄津贴
    String AGE_SALARY = "ageSalary";
    // 基础性绩效
    String BASIC_PERF = "basicPerf";
    // 通讯费补贴
    String COMMUNICATION_FEE_ALLOWANCE = "communicationFeeAllowance";
    // 生活补贴
    String LIFE_SALARY = "lifeSalary";
    // 人力临时增加
    String TEMPORARY_ADD_SALARY = "temporaryAddSalary";
    // 财务临时增加
    String TEMPORARY_ADD_SALARY2 = "temporaryAddSalary2";

    //岗位工资
    String POST_SALARY_CODE = "3010101";
    // 薪级工资
    String SAL_GRADE_SALARY_CODE = "3010102";
    // 护士 10%
    String NURSE_SALARY_CODE = "3010103";
    // 地区附加津贴
    String AREA_SALARY_CODE = "3010201";
    // 护龄津贴
    String AGE_SALARY_CODE = "3010202";
    // 基础性绩效
    String BASIC_PERF_CODE = "3010705";
    // 生活补贴
    String LIFE_SALARY_CODE = "30305";

    //----------------应发工资end------------------


    //-----------------代扣代缴start---------------

    // 养老保险
    String PENSION_INSURANCE = "pensionInsurance";
    // 医疗保险
    String MEDICAL_INSURANCE = "medicalInsurance";
    // 失业保险
    String UNEMPLOYMENT_INSURANCE = "unemploymentInsurance";
    // 住房基金
    String HOUSING_FUND = "housingFund";
    // 执业年金
    String OCCUPATION_ANNUITY = "occupationalAnnuity";
    //个人所得税
    String PERSON_TAX = "personalIncomeTaxDeduction";

    //房租费
    String RENT = "rent";
    //水费
    String WATER_CHARGE = "waterCharge";
    // 临时扣款
    String TEMPORARY_REDUCE_SALARY = "temporaryReduceSalary";
    // 临时扣款
    String TEMPORARY_REDUCE_SALARY2 = "temporaryReduceSalary2";

    //银行存款 不是工资明细中的项，但是工资凭证会生成此项贷方，所以此项用于salaryConfig的配置
    String BANK_DEPOSIT = "bankDeposit";

    /** 工资总额-应付职工薪酬-基本工资（含离退休费） 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SALARY_TOTAL_BASE = "salaryTotalBase";

    /** 工资总额-应付职工薪酬-国家统一规定的津贴补贴 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SALARY_TOTAL_ALLOWANCE = "salaryTotalAllowance";

    /** 工资总额-应付职工薪酬-规范津贴补贴（绩效工资） 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SALARY_TOTAL_PERFORM_SAL = "salaryTotalPerformSal";

    /** 工资总额-冲账 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String OFFSET_ACCOUNT = "offsetAccount";

    /** 工资总额-特殊往来账 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SPECIAL_CORRS_ACCOUNT = "specialCorrsAccount";

    /** 工资总额-特殊单采血浆站 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SPECIAL_PLASMA_STATION = "specialPlasmaStation";
    //-----------------代扣代缴end-----------------


    //------------------企业缴纳start--------------
    // 机关事业单位基本养老保险缴费
    String PENSION_INSURANCE_ENTP = "pensionInsuranceEntp";
    // 职工企业基本养老保险缴费
    String PENSION_INSURANCE_ENTP2 = "pensionInsuranceEntp2";
    // 职工基本医疗保险缴费
    String MEDICAL_INSURANCE_ENTP = "medicalInsuranceEntp";
    // 职工失业保险缴
    String UNEMPLOYMENT_INSURANCE_ENTP = "unemploymentInsuranceEntp";
    // 住房公积金
    String HOUSING_FUND_ENTP = "housingFundEntp";
    // 职业年金缴费
    String OCCUPATION_ANNUITY_ENTP = "occupationalAnnuityEntp";
    // 职工工伤保险缴费
    String INJR_INSU_ENTP = "injrInsuEntp";
    // 工费支出
    String PUB_FEE_ENTP = "pubFeeEntp";
    //------------------企业缴纳end----------------

    //--------------------工会会费代扣start-------------
    //工会会费
    String LABOR_UNION = "laborUnion";
    //--------------------工会会费代扣end-------------
    /**
     * 辅助信息-部门-信息代码
     **/
    String ACTIG_ASST_CFG_DEPT = "106";

    //----------------------payTypeCode分类-----------------
    //差旅
    String PAY_TYPECODE_TRAVEL = "101";

    //培训
    String PAY_TYPECODE_TRAINING = "102";

    //其他费用
    String PAY_TYPECODE_OTHER = "000";

    //分摊
    String PAY_TYPECODE_SHARE = "103";

    //工资
    String PAY_TYPECODE_SALARY = "104";

    //合同
    String PAY_TYPECODE_CONTRACT = "105";

    //折旧
    String PAY_TYPECODE_DEPR = "106";

    //零星采购
    String PAY_TYPECODE_PURC = "107";

    //药品
    String PAY_TYPECODE_DRUG = "109";

    //物资采购
    String PAY_TYPECODE_PURC_WZ = "110";


    //-----------------------付款回单信息start------------------------

    //---------------status---------------
    /** 识别成功 **/
    String RECOG_SUCCESS = "1";

    /**
     * 识别失败
     */
    String RECOG_FAILD = "2";

    /**
     * 手动修改
     */
    String MANUAL_MODIFY = "3";
    //识别信息后，可能作为map中key值的日期名称
    String RECOG_DATE = "日期";
    String RECOG_COMMISSION_DATE = "委托日期";
    //识别信息后，可能作为map中key值的金额名称
    String RECOG_AMT = "金额";
    //-----------------------付款回单信息end------------------------


    //-----------------------付款回单类型start------------------------
    String BUSINESS_AUTHORIZATION_LETTER = "业务委托书";

    String BUSINESS_SETTLEMENT_LETTER = "结算业务申请书";

    String NATION_PAYMENT_VOUCHER = "国库集中支付凭证";

    String NATION_PAYMETN_RECEIPT = "国内支付业务付款回单";
    //-----------------------付款回单类型end----------------------------

    //-----------------------工资凭证类型start--------------------------

    /** 工资计提 **/
    String SALARY = "1";

    /** 个人代扣 **/
    String INDIVDUAL_REDUCE = "2";

    /** 企业缴纳 **/
    String BUSINESS_PAYMENT = "3";

    /** 工会经费 **/
    String UNION_FUNDS = "4";
    //-----------------------工资凭证类型end--------------------------

    //-----------------------科室类型start--------------------------
    /**职能**/
    String FUN_DEPT = "1";

    /** 窗口 **/
    String WIN_DEPT = "2";

    /** 临床 **/
    String CLINI_DEPT = "3";

    /** 医技 **/
    String MED_TEC_DEPT = "4";

    /** 临床医技 **/
    String CLINIC_STR = "3,4";

    /** 医辅、行政  （暂时都归到职能类型） **/
    String FUN_STR = "1";
    //-----------------------科室类型end--------------------------



    //-----------------------工资凭证人员类型start--------------------------
    //在编
    String[] ESTAB_STR_ARR = new String[]{"在编","血防占编"};

    //招聘
    String[] HIRE_STR_ARR = new String[]{"编外-医技","编外-护理","编外-辅助岗位","编外-医技-见习","编外-护理-见习","编外-辅助岗位-见习"};

    //临聘
    String[] TEMP_HIRE_STR_ARR = new String[]{"编外-其他专技","编外-后勤","编外-其他专技-见习","编外-后勤-见习"};

    //借调
    String[] SECONDMENT_STR_ARR = new String[]{"借调"};

    //返聘
    String[] REHIRE_STR_ARR = new String[]{"返聘"};
    //-----------------------工资凭证人员类型end----------------------------

    //培训费目名称
    String TRAIN_ITEM_NAME = "培训费";

    //------------------------零星采购-经济科目 start----------------------------
    //总务低值易耗品
    String ZWDZYH = "3021801";
    //其他总务材料
    String QTZWCL = "3021802";
    //------------------------零星采购-经济科目 end------------------------------

    //------------------------药品库房类型-start---------------------------------
    //中药库
    String STOIN_TYPE_ZY = "1099";
    //西药库
    String STOIN_TYPE_XY = "1094";
    //消毒用品
    String STOIN_TYPE_XD = "1241";
    //------------------------药品库房类型-end-----------------------------------


    /** 附件类型-普通 **/
    String FILE_TYPE_GENERAL = "1";

    /** 附件类型-付款证明 **/
    String FILE_TYPE_PAY = "2";

    /** 附件类型-发票 **/
    String FILE_TYPE_INVO = "3";
}
