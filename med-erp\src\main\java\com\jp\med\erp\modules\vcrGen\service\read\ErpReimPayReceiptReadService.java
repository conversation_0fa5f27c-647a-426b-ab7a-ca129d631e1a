package com.jp.med.erp.modules.vcrGen.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimPayReceiptDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;

import java.util.List;

/**
 * 费用报销付款回单信息
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 16:02:12
 */
public interface ErpReimPayReceiptReadService extends IService<ErpReimPayReceiptDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpReimPayReceiptVo> queryList(ErpReimPayReceiptDto dto);

    /**
     * 分页查询列表
     * @param dto
     * @return
    */
    List<ErpReimPayReceiptVo> queryPageList(ErpReimPayReceiptDto dto);
}

