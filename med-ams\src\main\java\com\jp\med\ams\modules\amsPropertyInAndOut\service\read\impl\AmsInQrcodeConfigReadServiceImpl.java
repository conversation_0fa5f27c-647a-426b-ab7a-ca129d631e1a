package com.jp.med.ams.modules.amsPropertyInAndOut.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInQrcodeConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsInQrcodeConfigReadMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.read.AmsInQrcodeConfigReadService;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInQrcodeConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsInQrcodeConfigReadServiceImpl extends ServiceImpl<AmsInQrcodeConfigReadMapper, AmsInQrcodeConfigDto> implements AmsInQrcodeConfigReadService {

    @Autowired
    private AmsInQrcodeConfigReadMapper amsInQrcodeConfigReadMapper;

    @Override
    public List<AmsInQrcodeConfigVo> queryList(AmsInQrcodeConfigDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsInQrcodeConfigReadMapper.queryList(dto);
    }

}
