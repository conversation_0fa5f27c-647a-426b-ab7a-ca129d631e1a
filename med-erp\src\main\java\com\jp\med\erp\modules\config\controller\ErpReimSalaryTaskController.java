package com.jp.med.erp.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.config.service.read.ErpReimSalaryTaskReadService;
import com.jp.med.erp.modules.config.service.write.ErpReimSalaryTaskWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Api(value = "应发工资报销任务", tags = "应发工资报销任务")
@RestController
@RequestMapping("erpReimSalaryTask")
public class ErpReimSalaryTaskController {

    @Autowired
    private ErpReimSalaryTaskReadService erpReimSalaryTaskReadService;

    @Autowired
    private ErpReimSalaryTaskWriteService erpReimSalaryTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询应发工资报销任务")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody ErpReimSalaryTaskDto dto){
        return CommonResult.paging(erpReimSalaryTaskReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询应发工资报销任务")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody ErpReimSalaryTaskDto dto){
        return CommonResult.success(erpReimSalaryTaskReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增应发工资报销任务")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody ErpReimSalaryTaskDto dto){
        erpReimSalaryTaskWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改应发工资报销任务")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody ErpReimSalaryTaskDto dto){
        erpReimSalaryTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除应发工资报销任务")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody ErpReimSalaryTaskDto dto){
        erpReimSalaryTaskWriteService.removeById(dto);
        return CommonResult.success();
    }

}
