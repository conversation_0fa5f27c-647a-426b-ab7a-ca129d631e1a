package com.jp.med.ams.modules.depr.mapper.read;

import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.jp.med.ams.modules.depr.vo.AmsDeprAsgnVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 资产折旧分配
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Mapper
public interface AmsDeprAsgnReadMapper extends BaseMapper<AmsDeprAsgnDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsDeprAsgnVo> queryList(AmsDeprAsgnDto dto);

    /**
     * 查询分摊配置
     * @param dto
     * @return
     */
    List<AmsDeprAsgnVo> queryAsgn(AmsDeprAsgnDto dto);
}
