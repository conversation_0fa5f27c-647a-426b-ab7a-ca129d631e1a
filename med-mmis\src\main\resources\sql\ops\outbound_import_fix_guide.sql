-- =================================================================================================
-- 出库导入问题修复指南
-- 功能：解决出库数据导入过程中遇到的字段缺失问题
-- 问题：字段 t.out_emp 不存在
-- =================================================================================================

-- =================================================================================================
-- 问题分析 🔍
-- =================================================================================================

/*
错误信息：字段 t.out_emp 不存在
原因分析：
1. 源表 mmis_temp_xinxike_outbound_six 缺少 out_emp 字段
2. 源表 mmis_temp_xinxike_outbound_six 缺少 out_org 字段
3. 导入脚本假设这些字段存在，但实际源表结构不完整

解决方案：
1. 添加缺失的字段到源表
2. 检查源表的实际数据结构
3. 根据实际情况调整导入逻辑
*/

-- =================================================================================================
-- 修复步骤 🔧
-- =================================================================================================

-- 步骤1：回滚当前事务（如果需要）
-- ROLLBACK;

-- 步骤2：检查源表当前结构
SELECT 
    '当前源表字段' as info,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
ORDER BY ordinal_position;

-- 步骤3：添加缺失字段
ALTER TABLE mmis_temp_xinxike_outbound_six 
ADD COLUMN IF NOT EXISTS out_emp varchar(50),
ADD COLUMN IF NOT EXISTS out_org varchar(100);

-- 步骤4：验证字段添加结果
SELECT 
    '字段添加验证' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'mmis_temp_xinxike_outbound_six' 
              AND column_name = 'out_emp'
        ) THEN '✅ out_emp 字段已存在'
        ELSE '❌ out_emp 字段缺失'
    END as out_emp_status,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'mmis_temp_xinxike_outbound_six' 
              AND column_name = 'out_org'
        ) THEN '✅ out_org 字段已存在'
        ELSE '❌ out_org 字段缺失'
    END as out_org_status;

-- =================================================================================================
-- 数据映射检查 📊
-- =================================================================================================

-- 检查源表是否有可以映射到 out_emp 和 out_org 的字段
SELECT 
    '可能的员工字段' as field_type,
    column_name
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
  AND (
    column_name LIKE '%emp%' OR 
    column_name LIKE '%user%' OR 
    column_name LIKE '%staff%' OR
    column_name LIKE '%person%'
  )
ORDER BY column_name;

SELECT 
    '可能的科室字段' as field_type,
    column_name
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
  AND (
    column_name LIKE '%org%' OR 
    column_name LIKE '%dept%' OR 
    column_name LIKE '%department%' OR
    column_name LIKE '%division%'
  )
ORDER BY column_name;

-- =================================================================================================
-- 数据填充建议 💡
-- =================================================================================================

/*
如果源表中有其他字段包含员工或科室信息，可以使用以下方式填充：

-- 示例1：如果有 employee_code 字段，可以复制到 out_emp
UPDATE mmis_temp_xinxike_outbound_six 
SET out_emp = employee_code 
WHERE employee_code IS NOT NULL;

-- 示例2：如果有 department_name 字段，可以复制到 out_org
UPDATE mmis_temp_xinxike_outbound_six 
SET out_org = department_name 
WHERE department_name IS NOT NULL;

-- 示例3：如果没有相关数据，可以设置默认值
UPDATE mmis_temp_xinxike_outbound_six 
SET out_emp = 'UNKNOWN',
    out_org = '信息科'
WHERE out_emp IS NULL OR out_org IS NULL;
*/

-- =================================================================================================
-- 重新执行导入 🚀
-- =================================================================================================

/*
修复完成后，可以重新执行导入：

BEGIN;
\i med-mmis/src/main/resources/sql/ops/xinxike_new_outbound_import.sql
-- 检查结果
-- COMMIT; 或 ROLLBACK;
*/

-- =================================================================================================
-- 验证脚本 ✅
-- =================================================================================================

-- 最终验证：检查所有必要字段是否存在
SELECT 
    '最终字段检查' as final_check,
    COUNT(CASE WHEN column_name = 'out_emp' THEN 1 END) as has_out_emp,
    COUNT(CASE WHEN column_name = 'out_org' THEN 1 END) as has_out_org,
    COUNT(CASE WHEN column_name = 'name' THEN 1 END) as has_name,
    COUNT(CASE WHEN column_name = 'num' THEN 1 END) as has_num,
    COUNT(CASE WHEN column_name = 'price' THEN 1 END) as has_price,
    CASE 
        WHEN COUNT(CASE WHEN column_name IN ('out_emp', 'out_org', 'name', 'num', 'price') THEN 1 END) = 5
        THEN '✅ 核心字段完整，可以执行导入'
        ELSE '❌ 仍有字段缺失，需要进一步检查'
    END as status
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six';

-- 显示源表数据样例（用于确认数据结构）
SELECT 
    '源表数据样例' as sample_info,
    *
FROM mmis_temp_xinxike_outbound_six 
LIMIT 3;
