package com.jp.med.ams.modules.dashboard.vo;

import lombok.Data;

/**
 * 仪表盘指标数据VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class AmsDashboardMetricsVo {

    /**
     * 总盘点任务数
     */
    private Integer totalTasks;

    /**
     * 盘点完成率
     */
    private Double completionRate;

    /**
     * 盘盈资产数
     */
    private Integer profitAssets;

    /**
     * 盘亏资产数
     */
    private Integer lossAssets;

    /**
     * 已盘点资产数
     */
    private Integer inventoriedAssets;

    /**
     * 未盘点资产数
     */
    private Integer uninventoriedAssets;

    /**
     * 总资产数
     */
    private Integer totalAssets;

    /**
     * 任务趋势数据
     */
    private TrendData tasksTrend;

    /**
     * 完成率趋势数据
     */
    private TrendData completionTrend;

    /**
     * 盘盈趋势数据
     */
    private TrendData profitTrend;

    /**
     * 盘亏趋势数据
     */
    private TrendData lossTrend;

    /**
     * 趋势数据
     */
    @Data
    public static class TrendData {
        /**
         * 变化值
         */
        private Double value;

        /**
         * 趋势类型 (up/down/stable)
         */
        private String type;

        /**
         * 变化百分比
         */
        private Double percentage;
    }
} 