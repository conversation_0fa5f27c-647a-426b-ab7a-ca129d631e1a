package com.jp.med.ams.modules.inventory.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.jp.med.ams.modules.inventory.vo.AmsIntrVo;

import java.util.List;
import java.util.Map;

/**
 * 资产盘点表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
public interface AmsIntrReadService extends IService<AmsIntrDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsIntrVo> queryList(AmsIntrDto dto);

    /**
     * 获取最新盘点状态
     *
     * @param dto 盘点任务DTO
     * @return 包含任务ID、总资产数、已盘点数量、已盘点UID列表的状态信息
     */
    Map<String, Object> getLatestIntrStatus(AmsIntrTodoDto dto);
}

