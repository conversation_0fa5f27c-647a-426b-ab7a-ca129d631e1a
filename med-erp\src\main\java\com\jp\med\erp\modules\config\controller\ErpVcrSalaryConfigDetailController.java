package com.jp.med.erp.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.service.read.ErpVcrSalaryConfigDetailReadService;
import com.jp.med.erp.modules.config.service.write.ErpVcrSalaryConfigDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 财务核算-工资凭证科目映射配置明细
 * <AUTHOR>
 * @email -
 * @date 2024-08-22 09:54:23
 */
@Api(value = "财务核算-工资凭证科目映射配置明细", tags = "财务核算-工资凭证科目映射配置明细")
@RestController
@RequestMapping("erpVcrSalaryConfigDetail")
public class ErpVcrSalaryConfigDetailController {

    @Autowired
    private ErpVcrSalaryConfigDetailReadService erpVcrSalaryConfigDetailReadService;

    @Autowired
    private ErpVcrSalaryConfigDetailWriteService erpVcrSalaryConfigDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询财务核算-工资凭证科目映射配置明细")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody ErpVcrSalaryConfigDetailDto dto){
        return CommonResult.paging(erpVcrSalaryConfigDetailReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询财务核算-工资凭证科目映射配置明细")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody ErpVcrSalaryConfigDetailDto dto){
        return CommonResult.success(erpVcrSalaryConfigDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增财务核算-工资凭证科目映射配置明细")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody ErpVcrSalaryConfigDetailDto dto){
        erpVcrSalaryConfigDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改财务核算-工资凭证科目映射配置明细")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody ErpVcrSalaryConfigDetailDto dto){
        erpVcrSalaryConfigDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除财务核算-工资凭证科目映射配置明细")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody ErpVcrSalaryConfigDetailDto dto){
        erpVcrSalaryConfigDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
