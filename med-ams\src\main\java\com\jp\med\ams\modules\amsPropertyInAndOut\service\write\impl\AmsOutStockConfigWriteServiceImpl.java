package com.jp.med.ams.modules.amsPropertyInAndOut.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInStockConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsInStockConfigReadMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsOutStockAuditReadMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write.AmsInStockConfigWriteMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write.AmsOutStockAuditWriteMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.write.AmsOutStockConfigWriteService;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInStockConfigVo;
import com.jp.med.ams.modules.changes.feign.AmsMessageFeignService;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.ULIDUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Service
@Slf4j
@Transactional(readOnly = false)
public class AmsOutStockConfigWriteServiceImpl extends ServiceImpl<AmsInStockConfigWriteMapper, AmsInStockConfigDto>
        implements AmsOutStockConfigWriteService {

    @Resource
    private AmsInStockConfigReadMapper amsOutStockConfigReadMapper;

    @Resource
    private AmsInStockConfigWriteMapper amsOutStockConfigWriteMapper;

    @Resource
    private AmsOutStockAuditWriteMapper amsOutStockAuditWriteMapper;

    @Resource
    private AmsOutStockAuditReadMapper amsOutStockAuditReadMapper;
    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    public void saveConfig(AmsInStockConfigDto dto) {
        // 删除之前的配置
        QueryWrapper<AmsInStockConfigDto> queryWrapper = new QueryWrapper<>();
        amsOutStockConfigWriteMapper.delete(queryWrapper.eq("out_dept", dto.getOutDept()));
        // 保存新的配置
        ArrayList<AmsInStockConfigDto> AmsInStockConfigDtoArrayList = new ArrayList<>();
        List<String> flds = dto.getFlds();
        for (AmsInStockConfigVo amsOutStockConfigVo : dto.getList()) {
            if (flds.contains(amsOutStockConfigVo.getFld())) {
                AmsInStockConfigDto cfgDto = new AmsInStockConfigDto();
                cfgDto.setFld(amsOutStockConfigVo.getFld());
                cfgDto.setMustl(amsOutStockConfigVo.getMustl());
                cfgDto.setOutDept(dto.getOutDept());
                cfgDto.setType(dto.getType());
                AmsInStockConfigDtoArrayList.add(cfgDto);
            }
        }
        BatchUtil.batch(AmsInStockConfigDtoArrayList, AmsInStockConfigWriteMapper.class);
    }

    @Override
    public void removeConfig(AmsInStockConfigDto dto) {
        QueryWrapper<AmsInStockConfigDto> queryWrapper = new QueryWrapper<>();
        amsOutStockConfigWriteMapper.delete(queryWrapper.eq("out_dept", dto.getOutDept()));

    }

    @Resource
    private AmsMessageFeignService amsMessageFeignService;

    @Override
    @GlobalTransactional
    public void startOutProcess(AmsOutStockAuditDto dto) {
        // 1、生成审核批次号
        String ulid = ULIDUtil.generate();
        String bchno = AuditConst.AMS_OUT_APPLY + ulid;

        dto.setBchno(bchno);
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setOutTime(new Date());
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        // 申请中
        dto.setProsstas(MedConst.TYPE_1);

        // 设置出库人
        dto.setOutUser(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setOutDept(dto.getSysUser().getHrmUser().getHrmOrgId());
        // 转移申请校验
        dto.setSqlAutowiredHospitalCondition(true);
        if (amsOutStockAuditReadMapper.validateOutApply(dto)) {
            throw new AppException("当前资产正在出库中");
        }

        amsOutStockAuditWriteMapper.updateById(dto);

        // 2、构建审核app消息 TODO(未测试app端)
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            HrmUser hrmUser = dto.getSysUser().getHrmUser();
            String appyer = org.apache.commons.lang3.StringUtils.isNotEmpty(hrmUser.getEmpCode())
                    ? hrmUser.getEmpCode()
                    : dto.getSysUser().getUsername();
            String appyerName = org.apache.commons.lang3.StringUtils.isNotEmpty(hrmUser.getEmpName())
                    ? hrmUser.getEmpName()
                    : dto.getSysUser().getNickname();

            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle("资产转移申请");
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);

            appMsgSup.setContent(
                    "[" + dto.getOutDeptName() + "(" + dto.getOutDeptName() + ")" + "]发起出库流程到["
                            + dto.getReceiveDeptName() + "("
                            + dto.getReceiveUserName() + ")]" + "资产：" + dto.getAssetName());
            appMsgSup.setContent("您有新的出库流程待审核");
            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(bchno);

            // 进入详情页获取申请详细信息的请求地址 TODO
            auditPayload.setDetailUrl("");
            // 详情页的表格数据columns,需要在上面这个请求地址返回以下这些字段,用于详情页数据展示
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("assetName", "资产名称");
            map.put("outDeptName", "出库科室");
            map.put("receiveDeptName", "入库科室");
            map.put("outUserName", "出库人");
            map.put("receiveUserName", "接受人");
            auditPayload.setDisplayItem(map);

            // 系统消息
            SysMessageDto sysMessageDto = new SysMessageDto();
            List<String> ids = new ArrayList<>();
            ids.add(dto.getReceiveUser());
            String[] preInitializedArray = new String[ids.size()];
            sysMessageDto.setUsers(ids.toArray(preInitializedArray));
            sysMessageDto.setCreator(dto.getOutUserName());
            sysMessageDto.setTitle("资产出库申请");
            sysMessageDto.setPushText(appMsgSup.getContent());
            // FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(sysMessageDto));
            log.info("appMsgSup:{}", appMsgSup.getContent());
            // 远程调用 加入审核表

            var temp = new AuditDetail(bchno, dto.getAuditDetails(), appMsgSup, auditPayload,
                    OSSConst.BUCKET_AMS);
            temp.setRoutePath(dto.getRoutePath());
            // 远程调用 加入审核表
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(temp));

        }

    }

}
