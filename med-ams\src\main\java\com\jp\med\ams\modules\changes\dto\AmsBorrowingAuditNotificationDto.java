package com.jp.med.ams.modules.changes.dto;

import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;

/**
 * 资产借用审核通知DTO
 * 增强的通知结构，包含审核人信息和详细状态
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
public class AmsBorrowingAuditNotificationDto {

    /**
     * 借用记录信息
     */
    private AmsChngBrwgVo borrowingRecord;

    /**
     * 审核人工号
     */
    private String auditorCode;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 审核人科室
     */
    private String auditorDept;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 审核结果
     * APPROVED: 审核通过
     * REJECTED: 审核拒绝
     * RETURNED: 归还确认
     * EXTENDED: 续期申请
     */
    private String auditResult;

    /**
     * 审核备注/拒绝原因
     */
    private String auditRemarks;

    /**
     * 原流程状态
     */
    private String originalStatus;

    /**
     * 新流程状态
     */
    private String newStatus;

    /**
     * 通知类型
     * AUDIT_RESULT: 审核结果通知
     * EXTENSION_REQUEST: 续期申请通知
     * RETURN_REMINDER: 归还提醒
     * OVERDUE_WARNING: 逾期警告
     */
    private String notificationType;

    /**
     * 扩展信息
     */
    private String extendInfo;

    // 构造函数
    public AmsBorrowingAuditNotificationDto() {
    }

    public AmsBorrowingAuditNotificationDto(AmsChngBrwgVo borrowingRecord, String auditorName, String auditResult) {
        this.borrowingRecord = borrowingRecord;
        this.auditorName = auditorName;
        this.auditResult = auditResult;
    }

    /**
     * 构建审核通过通知
     */
    public static AmsBorrowingAuditNotificationDto buildApprovalNotification(
            AmsChngBrwgVo borrowingRecord, String auditorName, String auditorDept, String auditTime) {
        AmsBorrowingAuditNotificationDto dto = new AmsBorrowingAuditNotificationDto();
        dto.setBorrowingRecord(borrowingRecord);
        dto.setAuditorName(auditorName);
        dto.setAuditorDept(auditorDept);
        dto.setAuditTime(auditTime);
        dto.setAuditResult("APPROVED");
        dto.setNotificationType("AUDIT_RESULT");
        return dto;
    }

    /**
     * 构建审核拒绝通知
     */
    public static AmsBorrowingAuditNotificationDto buildRejectionNotification(
            AmsChngBrwgVo borrowingRecord, String auditorName, String auditorDept,
            String auditTime, String rejectReason) {
        AmsBorrowingAuditNotificationDto dto = new AmsBorrowingAuditNotificationDto();
        dto.setBorrowingRecord(borrowingRecord);
        dto.setAuditorName(auditorName);
        dto.setAuditorDept(auditorDept);
        dto.setAuditTime(auditTime);
        dto.setAuditResult("REJECTED");
        dto.setAuditRemarks(rejectReason);
        dto.setNotificationType("AUDIT_RESULT");
        return dto;
    }

    /**
     * 构建归还确认通知
     */
    public static AmsBorrowingAuditNotificationDto buildReturnConfirmationNotification(
            AmsChngBrwgVo borrowingRecord, String auditorName, String auditorDept, String auditTime) {
        AmsBorrowingAuditNotificationDto dto = new AmsBorrowingAuditNotificationDto();
        dto.setBorrowingRecord(borrowingRecord);
        dto.setAuditorName(auditorName);
        dto.setAuditorDept(auditorDept);
        dto.setAuditTime(auditTime);
        dto.setAuditResult("RETURNED");
        dto.setNotificationType("AUDIT_RESULT");
        return dto;
    }

    // Getter and Setter methods
    public AmsChngBrwgVo getBorrowingRecord() {
        return borrowingRecord;
    }

    public void setBorrowingRecord(AmsChngBrwgVo borrowingRecord) {
        this.borrowingRecord = borrowingRecord;
    }

    public String getAuditorCode() {
        return auditorCode;
    }

    public void setAuditorCode(String auditorCode) {
        this.auditorCode = auditorCode;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getAuditorDept() {
        return auditorDept;
    }

    public void setAuditorDept(String auditorDept) {
        this.auditorDept = auditorDept;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    public String getAuditRemarks() {
        return auditRemarks;
    }

    public void setAuditRemarks(String auditRemarks) {
        this.auditRemarks = auditRemarks;
    }

    public String getOriginalStatus() {
        return originalStatus;
    }

    public void setOriginalStatus(String originalStatus) {
        this.originalStatus = originalStatus;
    }

    public String getNewStatus() {
        return newStatus;
    }

    public void setNewStatus(String newStatus) {
        this.newStatus = newStatus;
    }

    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }
}
