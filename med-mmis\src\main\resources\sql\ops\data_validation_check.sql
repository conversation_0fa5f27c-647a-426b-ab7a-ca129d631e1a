-- =================================================================================================
-- 数据验证检查脚本：检查源表数据质量和导入可行性
-- 功能：验证 mmis_temp_inport_storage_six 和 mmis_temp_xinxike_outbound_six 表的数据
-- 作者：数据迁移验证
-- 创建时间：2025年1月
-- =================================================================================================

-- =================================================================================================
-- 第一部分：源表基础信息检查 📊
-- =================================================================================================

-- 1.1 检查入库源表是否存在及基本信息
SELECT 
    '入库源表基础信息' as check_type,
    'mmis_temp_inport_storage_six' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_inport_storage_six')
        THEN '✅ 表存在'
        ELSE '❌ 表不存在'
    END as table_status,
    COALESCE((SELECT COUNT(*) FROM mmis_temp_inport_storage_six), 0) as total_records;

-- 1.2 检查出库源表是否存在及基本信息
SELECT 
    '出库源表基础信息' as check_type,
    'mmis_temp_xinxike_outbound_six' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_xinxike_outbound_six')
        THEN '✅ 表存在'
        ELSE '❌ 表不存在'
    END as table_status,
    COALESCE((SELECT COUNT(*) FROM mmis_temp_xinxike_outbound_six), 0) as total_records;

-- =================================================================================================
-- 第二部分：入库源表数据质量检查 🔍
-- =================================================================================================

-- 2.1 入库源表字段完整性检查
SELECT 
    '入库源表字段检查' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_inport_storage_six'
ORDER BY ordinal_position;

-- 2.2 入库源表核心字段数据质量检查
SELECT 
    '入库源表数据质量' as check_type,
    '总记录数' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_inport_storage_six
UNION ALL
SELECT 
    '入库源表数据质量' as check_type,
    '物资名称非空记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_inport_storage_six 
WHERE name IS NOT NULL AND TRIM(name) != ''
UNION ALL
SELECT 
    '入库源表数据质量' as check_type,
    '供应商非空记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_inport_storage_six 
WHERE supplier_name IS NOT NULL AND TRIM(supplier_name) != ''
UNION ALL
SELECT 
    '入库源表数据质量' as check_type,
    '数量有效记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_inport_storage_six 
WHERE num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
UNION ALL
SELECT 
    '入库源表数据质量' as check_type,
    '价格有效记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_inport_storage_six 
WHERE price IS NOT NULL AND price ~ '^[0-9]+\.?[0-9]*$' AND CAST(price AS numeric) > 0;

-- 2.3 入库源表数据样例展示
SELECT 
    '入库源表数据样例' as check_type,
    name,
    modspec,
    supplier_name,
    num,
    price,
    remark
FROM mmis_temp_inport_storage_six 
WHERE name IS NOT NULL AND TRIM(name) != ''
LIMIT 5;

-- =================================================================================================
-- 第三部分：出库源表数据质量检查 🔍
-- =================================================================================================

-- 3.1 出库源表字段完整性检查
SELECT 
    '出库源表字段检查' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
ORDER BY ordinal_position;

-- 3.2 出库源表核心字段数据质量检查
SELECT 
    '出库源表数据质量' as check_type,
    '总记录数' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_xinxike_outbound_six
UNION ALL
SELECT 
    '出库源表数据质量' as check_type,
    '物资名称非空记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_xinxike_outbound_six 
WHERE name IS NOT NULL AND TRIM(name) != ''
UNION ALL
SELECT 
    '出库源表数据质量' as check_type,
    '出库科室非空记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_xinxike_outbound_six 
WHERE out_org IS NOT NULL AND TRIM(out_org) != ''
UNION ALL
SELECT 
    '出库源表数据质量' as check_type,
    '数量有效记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_xinxike_outbound_six 
WHERE num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
UNION ALL
SELECT 
    '出库源表数据质量' as check_type,
    '价格有效记录' as metric,
    COUNT(*) as value,
    '条' as unit
FROM mmis_temp_xinxike_outbound_six 
WHERE price IS NOT NULL AND price ~ '^[0-9]+\.?[0-9]*$' AND CAST(price AS numeric) > 0;

-- 3.3 出库源表数据样例展示
SELECT 
    '出库源表数据样例' as check_type,
    name,
    modspec,
    out_org,
    out_emp,
    num,
    price,
    remark
FROM mmis_temp_xinxike_outbound_six 
WHERE name IS NOT NULL AND TRIM(name) != ''
LIMIT 5;

-- =================================================================================================
-- 第四部分：关联表数据检查 🔗
-- =================================================================================================

-- 4.1 检查物资信息辅助表
SELECT 
    '物资信息辅助表检查' as check_type,
    '总物资数量' as metric,
    COUNT(*) as value,
    '种' as unit
FROM mmis_aset_info_assist
UNION ALL
SELECT 
    '物资信息辅助表检查' as check_type,
    '有参考价格的物资' as metric,
    COUNT(*) as value,
    '种' as unit
FROM mmis_aset_info_assist 
WHERE ref_price IS NOT NULL AND ref_price > 0;

-- 4.2 检查员工信息表
SELECT 
    '员工信息表检查' as check_type,
    '总员工数量' as metric,
    COUNT(*) as value,
    '人' as unit
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy' AND is_deleted = 0
UNION ALL
SELECT 
    '员工信息表检查' as check_type,
    '有效员工编号数量' as metric,
    COUNT(DISTINCT emp_code) as value,
    '个' as unit
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy' AND is_deleted = 0 AND emp_code IS NOT NULL;

-- 4.3 检查组织信息表
SELECT 
    '组织信息表检查' as check_type,
    '总科室数量' as metric,
    COUNT(*) as value,
    '个' as unit
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' AND active_flag = '1'
UNION ALL
SELECT 
    '组织信息表检查' as check_type,
    '有效科室名称数量' as metric,
    COUNT(DISTINCT org_name) as value,
    '个' as unit
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' AND active_flag = '1' AND org_name IS NOT NULL;

-- =================================================================================================
-- 第五部分：数据匹配度分析 🎯
-- =================================================================================================

-- 5.1 入库数据物资匹配度分析
SELECT 
    '入库物资匹配度分析' as check_type,
    '源表物资种类' as metric,
    COUNT(DISTINCT name) as value,
    '种' as unit
FROM mmis_temp_inport_storage_six 
WHERE name IS NOT NULL AND TRIM(name) != ''
UNION ALL
SELECT 
    '入库物资匹配度分析' as check_type,
    '可匹配物资种类' as metric,
    COUNT(DISTINCT t.name) as value,
    '种' as unit
FROM mmis_temp_inport_storage_six t
INNER JOIN mmis_aset_info_assist a ON t.name = a.name
WHERE t.name IS NOT NULL AND TRIM(t.name) != '';

-- 5.2 出库数据员工匹配度分析
SELECT 
    '出库员工匹配度分析' as check_type,
    '源表员工编号种类' as metric,
    COUNT(DISTINCT out_emp) as value,
    '个' as unit
FROM mmis_temp_xinxike_outbound_six 
WHERE out_emp IS NOT NULL AND TRIM(out_emp) != ''
UNION ALL
SELECT 
    '出库员工匹配度分析' as check_type,
    '可匹配员工编号' as metric,
    COUNT(DISTINCT t.out_emp) as value,
    '个' as unit
FROM mmis_temp_xinxike_outbound_six t
INNER JOIN hrm_employee_info e ON t.out_emp = e.emp_code
WHERE t.out_emp IS NOT NULL AND TRIM(t.out_emp) != ''
  AND e.hospital_id = 'zjxrmyy' AND e.is_deleted = 0;

-- 5.3 出库数据科室匹配度分析
SELECT 
    '出库科室匹配度分析' as check_type,
    '源表科室种类' as metric,
    COUNT(DISTINCT out_org) as value,
    '个' as unit
FROM mmis_temp_xinxike_outbound_six 
WHERE out_org IS NOT NULL AND TRIM(out_org) != ''
UNION ALL
SELECT 
    '出库科室匹配度分析' as check_type,
    '可匹配科室' as metric,
    COUNT(DISTINCT t.out_org) as value,
    '个' as unit
FROM mmis_temp_xinxike_outbound_six t
INNER JOIN hrm_org o ON t.out_org = o.org_name
WHERE t.out_org IS NOT NULL AND TRIM(t.out_org) != ''
  AND o.hospital_id = 'zjxrmyy' AND o.active_flag = '1';

-- =================================================================================================
-- 第六部分：导入可行性评估 ✅
-- =================================================================================================

-- 6.1 入库数据导入可行性评估
WITH storage_analysis AS (
    SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN name IS NOT NULL AND TRIM(name) != '' THEN 1 END) as valid_name,
        COUNT(CASE WHEN supplier_name IS NOT NULL AND TRIM(supplier_name) != '' THEN 1 END) as valid_supplier,
        COUNT(CASE WHEN num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0 THEN 1 END) as valid_quantity,
        COUNT(CASE WHEN price IS NOT NULL AND price ~ '^[0-9]+\.?[0-9]*$' AND CAST(price AS numeric) > 0 THEN 1 END) as valid_price
    FROM mmis_temp_inport_storage_six
)
SELECT 
    '入库数据导入可行性' as assessment_type,
    total_records,
    valid_name,
    valid_supplier,
    valid_quantity,
    valid_price,
    CASE 
        WHEN total_records = 0 THEN '❌ 无数据'
        WHEN valid_name::float / total_records < 0.8 THEN '⚠️ 物资名称完整度不足80%'
        WHEN valid_supplier::float / total_records < 0.8 THEN '⚠️ 供应商信息完整度不足80%'
        WHEN valid_quantity::float / total_records < 0.8 THEN '⚠️ 数量信息完整度不足80%'
        ELSE '✅ 数据质量良好，可以导入'
    END as assessment_result
FROM storage_analysis;

-- 6.2 出库数据导入可行性评估
WITH outbound_analysis AS (
    SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN name IS NOT NULL AND TRIM(name) != '' THEN 1 END) as valid_name,
        COUNT(CASE WHEN out_org IS NOT NULL AND TRIM(out_org) != '' THEN 1 END) as valid_org,
        COUNT(CASE WHEN num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0 THEN 1 END) as valid_quantity,
        COUNT(CASE WHEN price IS NOT NULL AND price ~ '^[0-9]+\.?[0-9]*$' AND CAST(price AS numeric) > 0 THEN 1 END) as valid_price
    FROM mmis_temp_xinxike_outbound_six
)
SELECT 
    '出库数据导入可行性' as assessment_type,
    total_records,
    valid_name,
    valid_org,
    valid_quantity,
    valid_price,
    CASE 
        WHEN total_records = 0 THEN '❌ 无数据'
        WHEN valid_name::float / total_records < 0.8 THEN '⚠️ 物资名称完整度不足80%'
        WHEN valid_org::float / total_records < 0.8 THEN '⚠️ 科室信息完整度不足80%'
        WHEN valid_quantity::float / total_records < 0.8 THEN '⚠️ 数量信息完整度不足80%'
        ELSE '✅ 数据质量良好，可以导入'
    END as assessment_result
FROM outbound_analysis;
