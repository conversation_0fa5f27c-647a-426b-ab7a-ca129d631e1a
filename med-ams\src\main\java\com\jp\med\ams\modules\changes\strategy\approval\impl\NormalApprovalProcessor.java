package com.jp.med.ams.modules.changes.strategy.approval.impl;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.strategy.approval.AbstractApprovalProcessor;
import com.jp.med.ams.modules.changes.strategy.approval.ApprovalContext;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 普通变更审批处理器
 * 处理普通变更类型的审批业务逻辑
 * 优先级：999（最低，作为默认处理器）
 */
@Component
@Order(999)
public class NormalApprovalProcessor extends AbstractApprovalProcessor {
    
    @Override
    public boolean supports(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto) {
        // 支持所有非拆分的普通变更审批
        return !"8".equals(changeRecord.getRedcWay());
    }
    
    @Override
    protected AmsPropertyDto createPropertyUpdate(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto, ApprovalContext context) {
        AmsPropertyDto propertyUpdate = new AmsPropertyDto();
        propertyUpdate.setFaCode(changeRecord.getFaCode());
        
        if (isApprovalPassed(approvalDto)) {
            // 审批通过：设置为已注销，减少方式为变更类型
            propertyUpdate.setIsCanc("1");
            propertyUpdate.setRedcWay(changeRecord.getChgType());
        } else if (isApprovalCancelled(approvalDto)) {
            // 取消审批：恢复为未注销状态，清除减少方式
            propertyUpdate.setIsCanc("0");
            propertyUpdate.setRedcWay(null);
        }
        
        return propertyUpdate;
    }
}
