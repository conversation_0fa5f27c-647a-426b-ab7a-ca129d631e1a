package com.jp.med.erp.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.erp.modules.config.mapper.write.ErpReimSalaryTaskWriteMapper;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.config.service.write.ErpReimSalaryTaskWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Service
@Transactional(readOnly = false)
public class ErpReimSalaryTaskWriteServiceImpl extends ServiceImpl<ErpReimSalaryTaskWriteMapper, ErpReimSalaryTaskDto> implements ErpReimSalaryTaskWriteService {
}
