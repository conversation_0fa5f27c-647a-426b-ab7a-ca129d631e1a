package com.jp.med.ams.modules.changes.mapper.read;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.vo.AmsChgRcdVo;

/**
 * 资产变更记录表
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
@Mapper
public interface AmsChgRcdReadMapper extends BaseMapper<AmsChgRcdDto> {

    /**
     * 查询列表
     * 
     * @param dto
     * @return
     */
    List<AmsChgRcdVo> queryList(AmsChgRcdDto dto);

    /**
     * 变动记录+资金变动聚合
     * 
     * @param dto
     * @return
     */
    List<AmsChgRcdVo> queryList2Polymerization(AmsChgRcdDto dto);

}
