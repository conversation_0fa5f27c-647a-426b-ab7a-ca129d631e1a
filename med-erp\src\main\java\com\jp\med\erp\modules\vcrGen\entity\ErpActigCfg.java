package com.jp.med.erp.modules.vcrGen.entity;

import lombok.Data;

/**
 * 会计科目
 */
@Data
public class ErpActigCfg {
    /** id */
    private Integer id;

    /** 科目代码 */
    private String subCode;

    /** 科目名称 */
    private String subName;

    /** 科目类型 */
    private String subType;

    /** 备注 */
    private String remarks;

    /** 拼音助记码 */
    private String pinyin;

    /** 辅助信息 */
    private String asstInfo;

    /** 会计要素 */
    private String actigElem;

    /** 会计体系 */
    private String actigSys;

    /** 余额方向 */
    private String balcDirc;

    /** 单位类型 */
    private String empType;

    /** 创建人 */
    private String crter;

    /** 创建时间 */
    private String createTime;

    /** 修改时间 */
    private String modiTime;

    /** 医疗机构id */
    private String hospitalId;

    /** 启用标志 */
    private String activeFlag;

    /** 状态 */
    private String status;

    /** 科目代码或名称 */
    private String sub;
}
