package com.jp.med.ams.modules.inventory.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 盘点统一响应格式
 * 标准化所有盘点相关API的响应格式，便于前端处理
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryResponse<T> {

    /**
     * 响应是否成功
     */
    private boolean success;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 盘点会话ID（如果适用）
     */
    private String sessionId;

    /**
     * 错误代码（如果有错误）
     */
    private String errorCode;

    /**
     * 创建成功响应
     */
    public static <T> InventoryResponse<T> success(T data) {
        return InventoryResponse.<T>builder()
                .success(true)
                .data(data)
                .message("操作成功")
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应（带消息）
     */
    public static <T> InventoryResponse<T> success(T data, String message) {
        return InventoryResponse.<T>builder()
                .success(true)
                .data(data)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应（带会话ID）
     */
    public static <T> InventoryResponse<T> success(T data, String message, String sessionId) {
        return InventoryResponse.<T>builder()
                .success(true)
                .data(data)
                .message(message)
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应
     */
    public static <T> InventoryResponse<T> error(String message) {
        return InventoryResponse.<T>builder()
                .success(false)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应（带错误代码）
     */
    public static <T> InventoryResponse<T> error(String message, String errorCode) {
        return InventoryResponse.<T>builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .timestamp(LocalDateTime.now())
                .build();
    }
}

/**
 * 同步结果响应数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class SyncResultData {

    /**
     * 总盘点数量
     */
    private Integer totalCount;

    /**
     * 本次新增数量
     */
    private Integer newCount;

    /**
     * 重复数量
     */
    private Integer duplicateCount;

    /**
     * 本次新增的UID列表
     */
    private List<String> newUids;

    /**
     * 所有已盘点的UID列表
     */
    private List<String> allInventoriedUids;
}

/**
 * 盘点状态响应数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class InventoryStatusData {

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 总资产数量
     */
    private Integer totalAssets;

    /**
     * 已盘点数量
     */
    private Integer inventoriedCount;

    /**
     * 已盘点的UID列表
     */
    private List<String> inventoriedUids;

    /**
     * 盘点进度百分比
     */
    private Double progressPercentage;

    /**
     * 是否完成
     */
    private Boolean isCompleted;
}

/**
 * 盘点会话响应数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class InventorySessionData {

    /**
     * 会话ID
     */
    private Integer sessionId;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 会话状态
     */
    private String status;
}

/**
 * 资产列表响应数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class AssetListData {

    /**
     * 资产列表
     */
    private List<AssetInfo> assets;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 已盘点数量
     */
    private Integer inventoriedCount;

    /**
     * 未盘点数量
     */
    private Integer notInventoriedCount;
}

/**
 * 资产信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class AssetInfo {

    /**
     * 资产UID
     */
    private String uid;

    /**
     * 资产编码
     */
    private String faCode;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 存放地点
     */
    private String storageArea;

    /**
     * 是否已盘点
     */
    private String isIntr;

    /**
     * 同步状态
     */
    private String sync;

    /**
     * 是否手工盘点
     */
    private Integer isManual;
}
