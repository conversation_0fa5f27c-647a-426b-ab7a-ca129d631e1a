package com.jp.med.erp.modules.vcrGen.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpReimTravelApprVo {

    /** id */
    private Long id;

    /** 申请时间 */
    private String appyerTime;

    /** 申请人 */
    private String appyer;

    /** 申请人 */
    private String appyerName;

    /** 出差事由 */
    private String evectionRea;

    /** 出差开始时间 */
    private String evectionBegnTime;

    /** 出差结束时间 */
    private String evectionEndTime;

    /** 出差时间 */
    private String evectionTime;;

    /** 出差地点 */
    private Integer evectionAddr;

    /** 出差地点详情 */
    private String evectionDetlAddr;

    /** 是否绕道 */
    private String detourOrNot;

    /** 公里数 */
    private BigDecimal kil;

    /** 交通方式 */
    private String trnp;

    /** 交通方式号码（如汽车为车牌号） */
    private String trnpNum;

    /** 是否安排伙食 */
    private String food;

    /** 是否安排住宿 */
    private String stay;

    /** 承诺 */
    private String prse;

    /** 预计出差金额 */
    private BigDecimal planAmt;

    /** 医疗机构id */
    private String hospitalId;

    /** 类型 */
    private String type;

    /** 文件名称 */
    private String attName;

    /** 文件 */
    private String att;

    /** 审核批次号 */
    private String auditBchno;

    /** 审核批次号 */
    private String pageImage;

    /** 报销标志 */
    private String reimFlag;

    /** 出差性质 */
    private String busMet;

    /** 1:职能科室 2:临床、医技 **/
    private String apprDeptType;

    /** 审核流程id **/
    private Integer chkerFlow;

    /** 审核标志,1:标识当前记录审核，0：标识当前记录不审核 */
    private String auditFlag;

    /** 申请科室 **/
    private String apprDept;

    /**
     * 对应的流程编号
     * <p>
     * 关联 ProcessInstance 的 id 属性
     */
    private String processInstanceId;
}
