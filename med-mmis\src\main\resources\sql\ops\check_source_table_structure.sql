-- =================================================================================================
-- 检查源表结构脚本：查看实际的源表字段结构
-- 功能：检查 mmis_temp_inport_storage_six 和 mmis_temp_xinxike_outbound_six 的实际字段
-- =================================================================================================

-- 检查入库源表字段结构
SELECT 
    '入库源表字段结构' as table_info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_inport_storage_six'
ORDER BY ordinal_position;

-- 检查出库源表字段结构
SELECT 
    '出库源表字段结构' as table_info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
ORDER BY ordinal_position;

-- 查看入库源表数据样例（前5条）
SELECT 
    '入库源表数据样例' as sample_info,
    *
FROM mmis_temp_inport_storage_six 
LIMIT 5;

-- 查看出库源表数据样例（前5条）
SELECT 
    '出库源表数据样例' as sample_info,
    *
FROM mmis_temp_xinxike_outbound_six 
LIMIT 5;
