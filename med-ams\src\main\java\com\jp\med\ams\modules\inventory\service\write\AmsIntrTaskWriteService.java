package com.jp.med.ams.modules.inventory.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;

/**
 * 资产盘点任务
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 10:12:34
 */
public interface AmsIntrTaskWriteService extends IService<AmsIntrTaskDto> {
    /**
     * 完成盘点
     * @param dto
     */
    void compltetIntr(AmsIntrTaskDto dto);
}

