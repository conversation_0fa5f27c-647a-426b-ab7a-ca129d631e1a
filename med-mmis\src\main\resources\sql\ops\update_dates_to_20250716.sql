-- =================================================================================================
-- 日期统一修改脚本：将入库和出库数据的日期统一调整为2025年7月16日
-- 功能：修改源表中的日期字段，确保导入的数据日期一致
-- 执行时机：在运行导入脚本之前执行
-- =================================================================================================

-- =================================================================================================
-- 第一部分：修改入库源表日期 📦
-- =================================================================================================

-- 检查入库源表是否存在必要字段
SELECT 
    '入库源表日期字段检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'mmis_temp_inport_storage_six' AND column_name = 'create_time')
        THEN '✅ create_time 字段存在'
        ELSE '❌ create_time 字段不存在'
    END as create_time_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'mmis_temp_inport_storage_six' AND column_name = 'bill_date')
        THEN '✅ bill_date 字段存在'
        ELSE '❌ bill_date 字段不存在'
    END as bill_date_status;

-- 为入库源表添加日期字段（如果不存在）
ALTER TABLE mmis_temp_inport_storage_six 
ADD COLUMN IF NOT EXISTS create_time varchar(50),
ADD COLUMN IF NOT EXISTS bill_date date;

-- 统一设置入库日期为2025年7月16日
UPDATE mmis_temp_inport_storage_six 
SET 
    create_time = '2025-07-16 12:00:00',
    bill_date = '2025-07-16'::date;

-- 验证入库日期修改结果
SELECT 
    '入库日期修改验证' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN create_time = '2025-07-16 12:00:00' THEN 1 END) as correct_create_time,
    COUNT(CASE WHEN bill_date = '2025-07-16'::date THEN 1 END) as correct_bill_date,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN create_time = '2025-07-16 12:00:00' THEN 1 END)
         AND COUNT(*) = COUNT(CASE WHEN bill_date = '2025-07-16'::date THEN 1 END)
        THEN '✅ 所有记录日期已统一'
        ELSE '⚠️ 部分记录日期未正确设置'
    END as status
FROM mmis_temp_inport_storage_six;

-- =================================================================================================
-- 第二部分：修改出库源表日期 📤
-- =================================================================================================

-- 检查出库源表是否存在必要字段
SELECT 
    '出库源表日期字段检查' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'mmis_temp_xinxike_outbound_six' AND column_name = 'create_time')
        THEN '✅ create_time 字段存在'
        ELSE '❌ create_time 字段不存在'
    END as create_time_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'mmis_temp_xinxike_outbound_six' AND column_name = 'bill_date')
        THEN '✅ bill_date 字段存在'
        ELSE '❌ bill_date 字段不存在'
    END as bill_date_status;

-- 为出库源表添加日期字段（如果不存在）
ALTER TABLE mmis_temp_xinxike_outbound_six 
ADD COLUMN IF NOT EXISTS create_time varchar(50),
ADD COLUMN IF NOT EXISTS bill_date date;

-- 统一设置出库日期为2025年7月16日
UPDATE mmis_temp_xinxike_outbound_six 
SET 
    create_time = '2025-07-16 12:00:00',
    bill_date = '2025-07-16'::date;

-- 验证出库日期修改结果
SELECT 
    '出库日期修改验证' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN create_time = '2025-07-16 12:00:00' THEN 1 END) as correct_create_time,
    COUNT(CASE WHEN bill_date = '2025-07-16'::date THEN 1 END) as correct_bill_date,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN create_time = '2025-07-16 12:00:00' THEN 1 END)
         AND COUNT(*) = COUNT(CASE WHEN bill_date = '2025-07-16'::date THEN 1 END)
        THEN '✅ 所有记录日期已统一'
        ELSE '⚠️ 部分记录日期未正确设置'
    END as status
FROM mmis_temp_xinxike_outbound_six;

-- =================================================================================================
-- 第三部分：日期修改总结 📊
-- =================================================================================================

-- 显示修改后的日期分布
SELECT 
    '入库数据日期分布' as data_type,
    bill_date,
    COUNT(*) as record_count
FROM mmis_temp_inport_storage_six
GROUP BY bill_date
ORDER BY bill_date;

SELECT 
    '出库数据日期分布' as data_type,
    bill_date,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six
GROUP BY bill_date
ORDER BY bill_date;

-- 最终验证
SELECT 
    '日期统一修改总结' as summary_type,
    '入库记录' as record_type,
    COUNT(*) as total_count,
    MIN(bill_date) as min_date,
    MAX(bill_date) as max_date,
    CASE 
        WHEN MIN(bill_date) = MAX(bill_date) AND MIN(bill_date) = '2025-07-16'::date
        THEN '✅ 日期已统一为2025-07-16'
        ELSE '❌ 日期未统一'
    END as status
FROM mmis_temp_inport_storage_six
UNION ALL
SELECT 
    '日期统一修改总结' as summary_type,
    '出库记录' as record_type,
    COUNT(*) as total_count,
    MIN(bill_date) as min_date,
    MAX(bill_date) as max_date,
    CASE 
        WHEN MIN(bill_date) = MAX(bill_date) AND MIN(bill_date) = '2025-07-16'::date
        THEN '✅ 日期已统一为2025-07-16'
        ELSE '❌ 日期未统一'
    END as status
FROM mmis_temp_xinxike_outbound_six;

-- =================================================================================================
-- 注意事项 📝
-- =================================================================================================

/*
日期修改说明：
1. 所有入库和出库记录的开单日期统一设置为：2025-07-16
2. 所有记录的创建时间统一设置为：2025-07-16 12:00:00
3. 这样可以确保导入的数据具有一致的时间戳
4. 单据号生成将基于统一的日期：CKD20250716001, RKD20250716001 等

影响范围：
- 入库单据号格式：RKD20250716XXX
- 出库单据号格式：CKD20250716XXX
- 所有业务数据的时间戳将显示为2025年7月16日

后续操作：
1. 执行完此脚本后，可以正常运行导入脚本
2. 导入的数据将具有统一的日期标识
3. 便于后续的数据管理和查询
*/
