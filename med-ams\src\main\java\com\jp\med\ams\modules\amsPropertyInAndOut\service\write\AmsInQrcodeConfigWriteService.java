package com.jp.med.ams.modules.amsPropertyInAndOut.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInQrcodeConfigDto;

import java.io.IOException;

/**
 * 资产入库二维码配置
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
public interface AmsInQrcodeConfigWriteService extends IService<AmsInQrcodeConfigDto> {
    void generateQrCode(AmsInQrcodeConfigDto dto) throws IOException;
}

