package com.jp.med.erp.modules.vcrGen.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrDetailDto;
import com.jp.med.erp.modules.vcrGen.entity.Certificate;

/**
 * 凭证信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
public interface ErpVcrDetailWriteService extends IService<ErpVcrDetailDto> {

    /**
     * 凭证生成
     * @param dto
     */
    void generateVcr(Certificate dto);

    void deleteVcr(ErpVcrDetailDto dto);

    String saveVcrAssts(Certificate dto);

    String saveErpAsstsWithFile(Certificate dto);

    /**
     * 更新凭证辅助项目金额
     * */
    void updateVcrAsstAmt(Certificate dto);

    void updVcrAssts(Certificate dto);

    void delVcrAssts(Certificate dto);

    void saveSalaryVcrAssts(ErpReimSalaryTaskDto dto);

    void saveDeprVcrAssts(EcsReimDeprTaskDto dto);

    void updErpVcrDetailPzh(ErpVcrDetailDto dto);

    String saveDrugVcrAssts(ErpVcrDetailDto dto);

    void delDrugVpzhMsg(ErpVcrDetailDto dto);
}

