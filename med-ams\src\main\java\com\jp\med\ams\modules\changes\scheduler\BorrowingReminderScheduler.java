package com.jp.med.ams.modules.changes.scheduler;

import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 借用提醒定时任务调度器
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
@Slf4j
@Component
public class BorrowingReminderScheduler {

    @Autowired
    private AmsBorrowingNotificationService notificationService;

    /**
     * 每天上午9点执行到期提醒任务
     * 检查即将到期的借用记录并发送提醒
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendDailyExpiryReminders() {
        log.info("🕘 开始执行每日到期提醒任务");

        try {
            notificationService.sendBatchExpiryReminders();
            log.info("✅ 每日到期提醒任务执行完成");
        } catch (Exception e) {
            log.error("❌ 每日到期提醒任务执行失败", e);
        }
    }

    /**
     * 每天下午2点执行逾期警告任务
     * 检查逾期的借用记录并发送警告
     */
    @Scheduled(cron = "0 0 14 * * ?")
    public void sendDailyOverdueWarnings() {
        log.info("🕑 开始执行每日逾期警告任务");

        try {
            notificationService.sendBatchOverdueWarnings();
            log.info("✅ 每日逾期警告任务执行完成");
        } catch (Exception e) {
            log.error("❌ 每日逾期警告任务执行失败", e);
        }
    }

    /**
     * 每小时执行一次紧急提醒任务
     * 检查当天到期和已逾期的借用记录
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void sendHourlyUrgentReminders() {
        log.info("🕐 开始执行每小时紧急提醒任务");

        try {
            // 这里可以添加更频繁的紧急提醒逻辑
            // 比如只检查当天到期和已逾期的记录
            notificationService.sendBatchExpiryReminders();
            log.info("✅ 每小时紧急提醒任务执行完成");
        } catch (Exception e) {
            log.error("❌ 每小时紧急提醒任务执行失败", e);
        }
    }

    /**
     * 每周一上午8点执行周报提醒
     * 发送本周即将到期的借用记录汇总
     */
    @Scheduled(cron = "0 0 8 * * MON")
    public void sendWeeklyReport() {
        log.info("📊 开始执行每周报告任务");

        try {
            // 这里可以添加周报逻辑
            // 比如统计本周即将到期的借用记录并发送汇总报告
            log.info("✅ 每周报告任务执行完成");
        } catch (Exception e) {
            log.error("❌ 每周报告任务执行失败", e);
        }
    }

    /**
     * 手动触发到期提醒任务
     * 用于测试或紧急情况下的手动执行
     */
    public void manualTriggerExpiryReminders() {
        log.info("🔧 手动触发到期提醒任务");

        try {
            notificationService.sendBatchExpiryReminders();
            log.info("✅ 手动到期提醒任务执行完成");
        } catch (Exception e) {
            log.error("❌ 手动到期提醒任务执行失败", e);
        }
    }

    /**
     * 手动触发逾期警告任务
     * 用于测试或紧急情况下的手动执行
     */
    public void manualTriggerOverdueWarnings() {
        log.info("🔧 手动触发逾期警告任务");

        try {
            notificationService.sendBatchOverdueWarnings();
            log.info("✅ 手动逾期警告任务执行完成");
        } catch (Exception e) {
            log.error("❌ 手动逾期警告任务执行失败", e);
        }
    }
}
