package com.jp.med.erp.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigDetailVo;

import java.util.List;

/**
 * 财务核算-工资凭证科目映射配置明细
 * <AUTHOR>
 * @email -
 * @date 2024-08-22 09:54:23
 */
public interface ErpVcrSalaryConfigDetailReadService extends IService<ErpVcrSalaryConfigDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrSalaryConfigDetailVo> queryList(ErpVcrSalaryConfigDetailDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<ErpVcrSalaryConfigDetailVo> queryPageList(ErpVcrSalaryConfigDetailDto dto);
}

