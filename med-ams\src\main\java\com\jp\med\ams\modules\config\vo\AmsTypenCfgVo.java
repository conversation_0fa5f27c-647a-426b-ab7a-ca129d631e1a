package com.jp.med.ams.modules.config.vo;


import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 新资产分类
 *
 * <AUTHOR>
 * @email -
 * @date 2023-12-06 11:20:53
 */
@Data
public class AmsTypenCfgVo implements BaseTree<String, AmsTypenCfgVo> {

    /**
     * ID
     */
    private Integer id;

    /**
     * 类型编码
     */
    private String assetTypeCode;

    /**
     * 唯一编码
     */
    private String key;

    /**
     * 类型名称
     */
    private String assetTypeName;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 建议使用年限
     */
    private String years;

    /**
     * 折旧方式编码
     */
    private String deprCode;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 残值率
     */
    private BigDecimal resr;

    /**
     * 说明
     */
    private String dscr;

    /**
     * 卡片配置
     */
    private String stockCfg;

    /**
     * 有效标志
     */
    private String flag;

    List<AmsTypenCfgVo> children;
    private String cardName;


    @Override
    public void setCode(String id) {
        this.assetTypeCode = id;
    }

    @Override
    public String getCode() {
        return this.assetTypeCode;
    }

    @Override
    public String getPid() {
        return this.parentCode;
    }

    @Override
    public void setPid(String pid) {
        this.parentCode = pid;
    }

    @Override
    public void addChild(AmsTypenCfgVo node) {
        if (Objects.isNull(children)) {
            this.children = new ArrayList<>();
        }
        this.children.add(node);
    }
}
