<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.config.mapper.read.ErpVcrSalaryConfigReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo" id="vcrSalaryConfigMap">
        <result property="id" column="id"/>
        <result property="salaryType" column="salary_type"/>
        <result property="reimName" column="reim_name"/>
        <result property="deptCode" column="dept_code"/>
        <result property="empCode" column="emp_code"/>
        <result property="abst" column="abst"/>
        <result property="empType" column="emp_type"/>
        <result property="deptType" column="dept_type"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo">
        SELECT
            a.id AS id,
            a.salary_type AS salaryType,
            a.reim_name AS reimName,
            a.dept_code AS deptCode,
            a.emp_code AS empCode,
            b.emp_name as empName,
            a.abst AS abst,
            a.emp_type AS empType,
            a.dept_type AS deptType,
            CASE
                WHEN (SELECT STRING_AGG(b.id::text, ',')
                      FROM erp_vcr_salary_config_detail b
                      WHERE b.salary_config_id = a.id) IS NOT NULL
                    THEN '1'
                ELSE '0'
                END AS hasAsst
        FROM erp_vcr_salary_config a
        LEFT JOIN hrm_employee_info b on a.emp_code = b.emp_code
        <where>
            <if test="salaryType != null and salaryType != ''">
                and a.salary_type = #{salaryType,jdbcType=VARCHAR}
            </if>
            <if test="reimName != null and reimName != ''">
                and a.reim_name like concat('%',#{reimName,jdbcType=VARCHAR},'%')
            </if>
            <if test="empType != null and empType != ''">
                and a.emp_type = #{empType,jdbcType=VARCHAR}
            </if>
            <if test="deptType != null and deptType != ''">
                and a.dept_type = #{deptType,jdbcType=VARCHAR}
            </if>
            <if test="empCode != null and empCode != ''">
                and a.emp_code = #{empCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.salary_type,a.id desc
    </select>

</mapper>
