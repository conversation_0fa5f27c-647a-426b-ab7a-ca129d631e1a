-- =================================================================================================
-- 出库申请表组织数据迁移脚本
-- 功能：将mmis_outbound_apply表中out_target_org_id的数据迁移到out_taget_org字段
-- 作者：数据迁移
-- 创建时间：2025年7月16日
-- =================================================================================================

BEGIN;

-- =================================================================================================
-- 第一步：数据检查 🔍
-- =================================================================================================

-- 检查迁移前的数据状态
SELECT 
    '迁移前数据检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_target_org_id,
    COUNT(CASE WHEN out_taget_org IS NOT NULL AND TRIM(out_taget_org) != '' THEN 1 END) as has_taget_org,
    COUNT(DISTINCT out_target_org_id) as unique_target_org_ids,
    COUNT(DISTINCT out_taget_org) as unique_taget_orgs
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy';

-- 显示当前数据样例
SELECT 
    '迁移前数据样例' as sample_type,
    docment_num,
    out_target_org_id,
    out_taget_org,
    bill_date
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' 
  AND (out_target_org_id IS NOT NULL OR out_taget_org IS NOT NULL)
ORDER BY docment_num
LIMIT 10;

-- =================================================================================================
-- 第二步：数据备份 💾
-- =================================================================================================

-- 添加备份字段（如果不存在）
ALTER TABLE mmis_outbound_apply 
ADD COLUMN IF NOT EXISTS out_target_org_id_backup varchar(255),
ADD COLUMN IF NOT EXISTS out_taget_org_backup varchar(255);

-- 备份原始数据
UPDATE mmis_outbound_apply 
SET 
    out_target_org_id_backup = out_target_org_id,
    out_taget_org_backup = out_taget_org
WHERE hospital_id = 'zjxrmyy';

-- =================================================================================================
-- 第三步：数据迁移 🔄
-- =================================================================================================

-- 将out_target_org_id的数据迁移到out_taget_org字段
-- 只有当out_taget_org为空且out_target_org_id有值时才迁移
UPDATE mmis_outbound_apply 
SET out_taget_org = out_target_org_id
WHERE hospital_id = 'zjxrmyy'
  AND out_target_org_id IS NOT NULL 
  AND TRIM(out_target_org_id) != ''
  AND (out_taget_org IS NULL OR TRIM(out_taget_org) = '');

-- =================================================================================================
-- 第四步：验证迁移结果 ✅
-- =================================================================================================

-- 检查迁移后的数据状态
SELECT 
    '迁移后数据检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND TRIM(out_target_org_id) != '' THEN 1 END) as has_target_org_id,
    COUNT(CASE WHEN out_taget_org IS NOT NULL AND TRIM(out_taget_org) != '' THEN 1 END) as has_taget_org,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND out_taget_org IS NOT NULL THEN 1 END) as both_fields_filled
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy';

-- 显示迁移后的数据样例
SELECT 
    '迁移后数据样例' as sample_type,
    docment_num,
    out_target_org_id,
    out_taget_org,
    bill_date,
    CASE 
        WHEN out_target_org_id = out_taget_org THEN '✅ 迁移成功'
        WHEN out_target_org_id IS NOT NULL AND out_taget_org IS NULL THEN '⚠️ 未迁移'
        WHEN out_target_org_id IS NULL AND out_taget_org IS NOT NULL THEN '📝 已有数据'
        ELSE '❓ 其他状态'
    END as migration_status
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' 
  AND (out_target_org_id IS NOT NULL OR out_taget_org IS NOT NULL)
ORDER BY docment_num
LIMIT 10;

-- 统计迁移结果
SELECT 
    '迁移结果统计' as result_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id = out_taget_org THEN 1 END) as successfully_migrated,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL AND out_taget_org IS NULL THEN 1 END) as failed_migration,
    COUNT(CASE WHEN out_target_org_id IS NULL AND out_taget_org IS NOT NULL THEN 1 END) as already_had_data,
    ROUND(
        COUNT(CASE WHEN out_target_org_id = out_taget_org THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END), 0), 2
    ) as migration_success_rate
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy';

-- =================================================================================================
-- 第五步：数据清理（可选） 🧹
-- =================================================================================================

-- 如果需要清空out_target_org_id字段（迁移完成后），取消下面的注释
-- UPDATE mmis_outbound_apply 
-- SET out_target_org_id = NULL
-- WHERE hospital_id = 'zjxrmyy'
--   AND out_taget_org IS NOT NULL 
--   AND out_target_org_id = out_taget_org;

-- =================================================================================================
-- 第六步：最终验证 📊
-- =================================================================================================

-- 最终数据分布
SELECT 
    '最终数据分布' as distribution_type,
    out_taget_org,
    COUNT(*) as record_count
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy' 
  AND out_taget_org IS NOT NULL
GROUP BY out_taget_org
ORDER BY record_count DESC;

-- 最终总结
SELECT 
    '🎯 迁移总结' as summary_type,
    '数据迁移完成' as operation,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_taget_org IS NOT NULL THEN 1 END) as records_with_taget_org,
    CASE 
        WHEN COUNT(CASE WHEN out_taget_org IS NOT NULL THEN 1 END) > 0
        THEN '✅ 迁移成功'
        ELSE '❌ 迁移失败'
    END as migration_status,
    ROUND(
        COUNT(CASE WHEN out_taget_org IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
    ) as completion_percentage
FROM mmis_outbound_apply 
WHERE hospital_id = 'zjxrmyy';

-- =================================================================================================
-- 事务控制 ⚠️
-- =================================================================================================

-- 提交事务（如果一切正常）
COMMIT;

-- 如果需要回滚，使用以下命令：
-- ROLLBACK;

-- =================================================================================================
-- 使用说明 📖
-- =================================================================================================

/*
脚本功能说明：
1. 将 mmis_outbound_apply 表中 out_target_org_id 字段的数据迁移到 out_taget_org 字段
2. 只迁移 out_taget_org 为空的记录，避免覆盖已有数据
3. 提供完整的数据备份和验证机制
4. 包含详细的迁移结果统计

执行步骤：
1. 数据检查：查看迁移前的数据状态
2. 数据备份：备份原始数据到 *_backup 字段
3. 数据迁移：将 out_target_org_id 数据复制到 out_taget_org
4. 结果验证：检查迁移是否成功
5. 数据清理：可选择清空源字段
6. 最终验证：提供完整的迁移报告

注意事项：
- 脚本在事务中执行，可以安全回滚
- 只迁移目标字段为空的记录
- 保留原始数据备份，便于问题排查
- 提供详细的迁移统计和验证
*/
