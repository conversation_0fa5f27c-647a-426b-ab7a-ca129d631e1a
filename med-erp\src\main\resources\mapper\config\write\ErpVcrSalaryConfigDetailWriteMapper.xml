<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.config.mapper.write.ErpVcrSalaryConfigDetailWriteMapper">

    <insert id="saveDetail" parameterType="com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto">
        INSERT INTO erp_vcr_salary_config_detail (
            salary_config_id,
            pay_type_code,
            pay_type_name,
            actig_sub_code,
            actig_sub_name,
            actig_sys,
            dept_code,
            dept_name,
            rel_co_code,
            rel_co_name,
            fun_sub_code,
            fun_sub_name,
            econ_sub_code,
            econ_sub_name,
            proj_code,
            proj_name,
            cash_flow_code,
            cash_flow_name,
            actig_amt_type,
            abst
        )
        VALUES (
            #{salaryConfigId},
            #{payTypeCode,jdbcType=VARCHAR},
            #{payTypeName,jdbcType=VARCHAR},
            #{actigSubCode},
            #{actigSubName},
            #{actigSys},
            #{deptCode},
            #{deptName},
            #{relCoCode},
            #{relCoName},
            #{funSubCode},
            #{funSubName},
            #{econSubCode},
            #{econSubName},
            #{projCode},
            #{projName},
            #{cashFlowCode},
            #{cashFlowName},
            #{actigAmtType},
            #{abst}
        )
    </insert>
</mapper>
