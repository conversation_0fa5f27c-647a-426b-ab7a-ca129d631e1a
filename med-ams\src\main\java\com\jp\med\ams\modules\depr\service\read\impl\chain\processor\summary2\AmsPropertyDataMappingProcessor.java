package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary2;

import com.jp.med.ams.modules.depr.service.read.impl.AmsPropertyDeprReadServiceImpl;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据映射处理器
 * 负责获取本月和上月的折旧数据并创建映射
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyDataMappingProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> {

    @Autowired
    private AmsPropertyDeprReadServiceImpl amsPropertyDeprReadService;

    @Override
    protected void doProcess(AmsPropertyDeprSummary2Context context) {
        log.debug("开始处理数据映射");

        // 获取本月和上月折旧汇总数据
        context.setCurrentMonthPropertyDepr2Vos(amsPropertyDeprReadService.queryDeprSummary(context.getQueryDto()));

        String currentYm = context.getQueryDto().getYm();
        context.getQueryDto().setYm(context.getLastMonthYm());
        context.setLastMonthPropertyDepr2Vos(amsPropertyDeprReadService.queryDeprSummary(context.getQueryDto()));
        context.getQueryDto().setYm(currentYm); // 恢复原始 ym 值

        // 创建 Map 用于快速查找，Key 为合并 Key
        Map<String, AmsPropertyDepr2Vo> currentMonthMap = createDeprVoMap(
                context.getCurrentMonthPropertyDepr2Vos(), context.isUseTypeNCode());
        Map<String, AmsPropertyDepr2Vo> lastMonthMap = createDeprVoMap(
                context.getLastMonthPropertyDepr2Vos(), context.isUseTypeNCode());

        context.setCurrentMonthMap(currentMonthMap);
        context.setLastMonthMap(lastMonthMap);

        log.debug("数据映射处理完成，本月数据 {} 条，上月数据 {} 条",
                currentMonthMap.size(), lastMonthMap.size());
    }

    /**
     * 创建折旧VO映射
     * 
     * @param deprVos      折旧VO列表
     * @param useTypeNCode 是否使用新类型代码
     * @return 映射表
     */
    private Map<String, AmsPropertyDepr2Vo> createDeprVoMap(java.util.List<AmsPropertyDepr2Vo> deprVos,
            boolean useTypeNCode) {
        Map<String, AmsPropertyDepr2Vo> map = new HashMap<>();

        for (AmsPropertyDepr2Vo vo : deprVos) {
            String key = createMergeKey(vo, useTypeNCode);
            map.put(key, vo);
        }

        return map;
    }

    /**
     * 创建合并键
     * 
     * @param vo           折旧VO对象
     * @param useTypeNCode 是否使用新类型代码
     * @return 合并键
     */
    private String createMergeKey(AmsPropertyDepr2Vo vo, boolean useTypeNCode) {
        String typeKey = useTypeNCode ? vo.getAssetTypeCode() : vo.getAssetTypeName();
        return vo.getDeptUseCode() + "-" + typeKey + "-" + vo.getSourceCode();
    }
}
