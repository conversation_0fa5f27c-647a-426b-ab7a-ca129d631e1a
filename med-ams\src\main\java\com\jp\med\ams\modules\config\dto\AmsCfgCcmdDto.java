package com.jp.med.ams.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 09:35:21
 */
@Data
@TableName("ams_cfg_ccmd" )
public class AmsCfgCcmdDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 医疗器械分类编码 */
    @TableField("ccm_code")
    private String ccmCode;

    /** 医疗器械分类名称 */
    @TableField("ccm_name")
    private String ccmName;

    /** 医疗器械分类父类编码 */
    @TableField("ccm_parent_code")
    private String ccmParentCode;

    /** 描述 */
    @TableField("\"desc\"")
    private String desc;

    /** 预期用途 */
    @TableField("intd_use")
    private String intdUse;

    /** 品名举例 */
    @TableField("exm_prod_name")
    private String exmProdName;

    /** 有效标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 名称或编码 */
    @TableField(exist = false)
    private String codeOrName;

    /** 分类产品描述或预期用途或品名举例 */
    @TableField(exist = false)
    private String detail;
}
