package com.jp.med.ams.modules.inventory.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 盘点分布式锁服务
 * 使用Redis实现分布式锁，防止重复同步和并发冲突
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Service
public class InventoryLockService {

    /**
     * 锁前缀
     */
    private static final String LOCK_PREFIX = "inventory:lock:";
    /**
     * 默认锁超时时间（秒）
     */
    private static final long DEFAULT_TIMEOUT = 30;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取同步锁
     *
     * @param taskId 任务ID
     * @return 是否获取成功
     */
    public boolean acquireSyncLock(Integer taskId) {
        return acquireLock("sync:" + taskId, DEFAULT_TIMEOUT);
    }

    /**
     * 释放同步锁
     *
     * @param taskId 任务ID
     */
    public void releaseSyncLock(Integer taskId) {
        releaseLock("sync:" + taskId);
    }

    /**
     * 获取盘点会话锁
     *
     * @param taskId 任务ID
     * @return 是否获取成功
     */
    public boolean acquireSessionLock(Integer taskId) {
        return acquireLock("session:" + taskId, DEFAULT_TIMEOUT);
    }

    /**
     * 释放盘点会话锁
     *
     * @param taskId 任务ID
     */
    public void releaseSessionLock(Integer taskId) {
        releaseLock("session:" + taskId);
    }

    /**
     * 获取分布式锁
     *
     * @param key     锁键
     * @param timeout 超时时间（秒）
     * @return 是否获取成功
     */
    private boolean acquireLock(String key, long timeout) {
        String lockKey = LOCK_PREFIX + key;
        String lockValue = String.valueOf(System.currentTimeMillis());

        try {
            Boolean success = redisTemplate.opsForValue()
                    .setIfAbsent(lockKey, lockValue, timeout, TimeUnit.SECONDS);
            return Boolean.TRUE.equals(success);
        } catch (Exception e) {
            // Redis异常时降级处理，允许操作继续
            return true;
        }
    }

    /**
     * 释放分布式锁
     *
     * @param key 锁键
     */
    private void releaseLock(String key) {
        String lockKey = LOCK_PREFIX + key;

        try {
            redisTemplate.delete(lockKey);
        } catch (Exception e) {
            // 忽略释放锁时的异常
        }
    }

    /**
     * 检查锁是否存在
     *
     * @param key 锁键
     * @return 是否存在
     */
    public boolean isLocked(String key) {
        String lockKey = LOCK_PREFIX + key;

        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(lockKey));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 使用锁执行操作
     *
     * @param key       锁键
     * @param timeout   超时时间（秒）
     * @param operation 要执行的操作
     * @param <T>       返回类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败
     */
    public <T> T executeWithLock(String key, long timeout, LockOperation<T> operation) {
        if (!acquireLock(key, timeout)) {
            throw new RuntimeException("获取锁失败，操作正在进行中");
        }

        try {
            return operation.execute();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            releaseLock(key);
        }
    }

    /**
     * 使用默认超时时间执行操作
     *
     * @param key       锁键
     * @param operation 要执行的操作
     * @param <T>       返回类型
     * @return 操作结果
     */
    public <T> T executeWithLock(String key, LockOperation<T> operation) {
        return executeWithLock(key, DEFAULT_TIMEOUT, operation);
    }

    /**
     * 锁操作接口
     */
    @FunctionalInterface
    public interface LockOperation<T> {
        T execute() throws Exception;
    }
}
