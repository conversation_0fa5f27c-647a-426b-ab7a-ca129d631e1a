package com.jp.med.erp.modules.config.mapper.read;

import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 工资凭证配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 00:52:39
 */
@Mapper
public interface ErpVcrSalaryConfigReadMapper extends BaseMapper<ErpVcrSalaryConfigDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrSalaryConfigVo> queryList(ErpVcrSalaryConfigDto dto);
}
