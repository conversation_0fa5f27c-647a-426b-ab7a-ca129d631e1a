package com.jp.med.erp.modules.vcrGen.vo;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 凭证信息明细
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Data
public class ErpVcrItemDetailVo {


	private Integer id;

	/** 分录号 */
	private Integer flh;

	/** 凭证号id,u8生成 */
	private String idpzh;

	/** 凭证内容id,u8生成 */
	private String idpznr;

	/** 借贷标志 */
	private String jdbz;

	/** 明细金额 */
	private BigDecimal je;

	/** 凭证号 */
	private String pzh;

	/** 凭证id,此id为hrp定下的唯一id */
	private String pzid;

}
