package com.jp.med.ams.modules.it.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.it.mapper.read.AmsItInvtApplyReadMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtApplyDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtApplyVo;
import com.jp.med.ams.modules.it.service.read.AmsItInvtApplyReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsItInvtApplyReadServiceImpl extends ServiceImpl<AmsItInvtApplyReadMapper, AmsItInvtApplyDto> implements AmsItInvtApplyReadService {

    @Autowired
    private AmsItInvtApplyReadMapper amsItInvtApplyReadMapper;

    /**
    *查询当前用户耗材申请
    *
    */
    @Override
    public List<AmsItInvtApplyVo> queryList(AmsItInvtApplyDto dto) {
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtApplyReadMapper.queryList(dto);
    }
    /**
    * 审核人查询所有耗材申请
    *
    */

    @Override
    public List<AmsItInvtApplyVo> adminQueryList(AmsItInvtApplyDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtApplyReadMapper.adminQueryList(dto);
    }

    /**
    *查询耗材出库信息
    *
    */
    @Override
    public List<AmsItInvtApplyVo> productOutInvt(AmsItInvtApplyDto dto) {
        //审核通过
        dto.setStatus("1");
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtApplyReadMapper.queryList(dto);
    }

}
