package com.jp.med.ams.modules.depr.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.depr.mapper.write.AmsDeprDeptWriteMapper;
import com.jp.med.ams.modules.depr.dto.AmsDeprDeptDto;
import com.jp.med.ams.modules.depr.service.write.AmsDeprDeptWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 存储部门信息，包括折旧额和其他相关数据
 * <AUTHOR>
 * @email -
 * @date 2024-07-19 16:16:26
 */
@Service
@Transactional(readOnly = false)
public class AmsDeprDeptWriteServiceImpl extends ServiceImpl<AmsDeprDeptWriteMapper, AmsDeprDeptDto> implements AmsDeprDeptWriteService {
}
