package com.jp.med.erp.modules.vcrGen.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimPayReceiptDto;
import com.jp.med.erp.modules.vcrGen.entity.Certificate;
import com.jp.med.erp.modules.vcrGen.entity.FileRecordEntity;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpReimPayReceiptReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.write.ErpReimPayReceiptWriteMapper;
import com.jp.med.erp.modules.vcrGen.service.write.ErpReimPayReceiptWriteService;
import com.jp.med.erp.modules.vcrGen.utils.RecogPayReceiptUtil;
import com.jp.med.erp.modules.vcrGen.vo.ErpDrugReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 费用报销付款回单信息
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 16:02:12
 */
@Service
@Transactional(readOnly = false)
public class ErpReimPayReceiptWriteServiceImpl extends ServiceImpl<ErpReimPayReceiptWriteMapper, ErpReimPayReceiptDto> implements ErpReimPayReceiptWriteService {

    @Autowired
    private ErpReimPayReceiptReadMapper erpReimPayReceiptReadMapper;

    @Autowired
    private ErpReimPayReceiptWriteMapper erpReimPayReceiptWriteMapper;

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    @Value("${urls.mid.recogn}")
    private String recogUrl;

    @Override
    public List<ErpReimPayReceiptVo> recogReceipt(ErpReimPayReceiptDto dto) {

        /*List<Integer> ids = dto.getIds();

        List<ErpReimPayReceiptVo> rtVos = new ArrayList<>();
        List<ErpReimPayReceiptDto> insertRecords = new ArrayList<>();

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (Integer id : ids) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 查询付款证明识别数据
                List<ErpReimPayReceiptVo> erpReimPayReceiptVos = erpReimPayReceiptReadMapper.queryListById(dto.getSupType(),id);
                // 查询报销付款证明文件
                List<FileRecordEntity> fileRecordEntities = new ArrayList<>();
                if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_1)) {
                    //费用报销
                    fileRecordEntities = erpVcrDetailReadMapper.queryFileRecord(Arrays.asList(id));
                } else if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_2)){
                    //药品报销
                    List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(Arrays.asList(id));
                    List<String> attCodes = drugVos.stream().map(ErpDrugReimDetailVo::getAttCode).collect(Collectors.toList());
                    fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
                } else if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_3)) {
                    //通过任务id查询当前报销
                    Certificate sParam = new Certificate();
                    sParam.setIds(Arrays.asList(id));
                    List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailWithSalaryId(sParam);
                    List<String> attCodes = erpReimDetailVos.stream().map(ErpReimDetailVo::getAttCode).collect(Collectors.toList());
                    fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
                }

                List<FileRecordEntity> collect = fileRecordEntities
                        .stream()
                        .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2))
                        .collect(Collectors.toList());

                if (erpReimPayReceiptVos.size() == collect.size()) {
                    // 数量一致，则已生成数据
                    synchronized (rtVos) {
                        rtVos.addAll(erpReimPayReceiptVos);
                    }
                    return;
                }

                // 删除当前reimId对应的receipt识别数据
                LambdaQueryWrapper<ErpReimPayReceiptDto> receiptWrapper = Wrappers.lambdaQuery();
                receiptWrapper.eq(ErpReimPayReceiptDto::getReimDetailId, id)
                        .eq(ErpReimPayReceiptDto::getSupType, dto.getSupType());
                remove(receiptWrapper);

                // 识别生成票据数据
                List<ErpReimPayReceiptDto> batchInsertRecords = new ArrayList<>();
                for (FileRecordEntity item : collect) {
                    ErpReimPayReceiptDto receiptDto = new ErpReimPayReceiptDto();
                    receiptDto.setSupType(dto.getSupType());
                    receiptDto.setReimDetailId(id);
                    receiptDto.setAtt(item.getAtt());
                    receiptDto.setAttName(item.getAttName());
                    receiptDto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
                    receiptDto.setCrterTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                    String recogJson = RecogPayReceiptUtil.alyRecognGnr(recogUrl, item.getAtt(), item.getAttName());
                    receiptDto.setStatus(ErpConstants.RECOG_SUCCESS);

                    // 在工具类中，将金额和日期识别后放入引用中
                    RecogPayReceiptUtil.recogStrToPayInfo(recogJson, receiptDto);

                    try {
                        if (StringUtils.equals(ErpConstants.BUSINESS_AUTHORIZATION_LETTER, receiptDto.getType())) {
                            // 业务委托书
                            receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr().trim()
                                    .replace("￥", "")
                                    .replace("，", "")));
                        } else if (StringUtils.equals(ErpConstants.NATION_PAYMENT_VOUCHER, receiptDto.getType())) {
                            // 国库集中支付凭证， 日期示例2024年12月09日  金额 ￥145,434.74
                            // 日期不用处理，金额替换
                            receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr().trim()
                                    .replace("￥", "")
                                    .replace("，", "")));
                        } else if (StringUtils.equals(ErpConstants.NATION_PAYMETN_RECEIPT, receiptDto.getType())) {
                            // 国内支付业务付款回单 日期示例: 日期：2024年12月09日   金额示例：金额：CNY 1，412.35
                            receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr()
                                    .split("CNY")[1].trim().replace("，", "")));
                            receiptDto.setPayDate(receiptDto.getPayDate().trim()
                                    .replace("日期", "")
                                    .replace("：", "")
                            );
                        } else if (StringUtils.equals(ErpConstants.BUSINESS_SETTLEMENT_LETTER, receiptDto.getType())) {
                            // 结算业务申请书 日期示例：2024年12月09  金额示例：人民币￥98，874.25
                            receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr().trim()
                                    .replace("人民币", "")
                                    .replace("￥", "")
                                    .replace("，", "")));
                        }
                    } catch (Exception e) {
                        receiptDto.setStatus(ErpConstants.RECOG_FAILD);
                        log.error(String.format("文件：{}解析异常", item.getAttName()), e);
                    }

                    batchInsertRecords.add(receiptDto);
                }

                synchronized (insertRecords) {
                    insertRecords.addAll(batchInsertRecords);
                }
            });

            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 保存
        saveBatch(insertRecords);

        // 返回数据
        insertRecords.stream().forEach(itemDto -> {
            ErpReimPayReceiptVo vo = new ErpReimPayReceiptVo();
            BeanUtils.copyProperties(itemDto, vo);
            rtVos.add(vo);
        });

        return rtVos;*/

        //所选药品报销id
        List<Integer> ids = dto.getIds();

        List<ErpReimPayReceiptVo> rtVos = new ArrayList<>();
        List<ErpReimPayReceiptDto> insertRecords = new ArrayList<>();

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_2)) {
            //药品报销因为多个报销单关联一个付款单，不通过报销id去找已识别付款单数据，而是drug_pay_id关联
            List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(ids);
            //获取付款单drug_pay_id
            Set<Integer> drugPayIds = drugVos.stream().map(ErpDrugReimDetailVo::getDrugPayId).collect(Collectors.toSet());
            //查询药品报销文件中间表
            EcsDrugPayDetailDto param = new EcsDrugPayDetailDto();
            param.setIds(new ArrayList<>(drugPayIds));
            List<EcsDrugPayDetailVo> ecsDrugPayDetailDtos = erpVcrDetailReadMapper.queryDrugPayDetail(param);
            //查询已识别付款单数据
            ErpReimPayReceiptDto rParam = new ErpReimPayReceiptDto();
            rParam.setSupType(dto.getSupType());
            rParam.setReimDetailIds(new ArrayList<>(drugPayIds));
            List<ErpReimPayReceiptVo> erpReimPayReceiptVos = erpReimPayReceiptReadMapper.queryList(rParam);

            //获取已识别付款单数据
            Set<String> attCodes = ecsDrugPayDetailDtos.stream().map(EcsDrugPayDetailVo::getAttCode).collect(Collectors.toSet());
            List<FileRecordEntity> fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(new ArrayList<>(attCodes));
            List<FileRecordEntity> payFileRecords = fileRecordEntities.stream()
                    .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2)).collect(Collectors.toList());
            if (erpReimPayReceiptVos.size() == payFileRecords.size()){
                return erpReimPayReceiptVos;
            }

            // 删除当前药品对应的receipt识别数据
            LambdaQueryWrapper<ErpReimPayReceiptDto> receiptWrapper = Wrappers.lambdaQuery();
            receiptWrapper.in(ErpReimPayReceiptDto::getReimDetailId, drugPayIds)
                    .eq(ErpReimPayReceiptDto::getSupType, dto.getSupType());
            remove(receiptWrapper);

            for (int i = 0; i < ecsDrugPayDetailDtos.size(); i++) {
                EcsDrugPayDetailVo ecsDrugPayDetailVo = ecsDrugPayDetailDtos.get(i);
                List<FileRecordEntity> fileRecords = payFileRecords.stream().filter(item -> StringUtils.equals(item.getAttCode(), ecsDrugPayDetailVo.getAttCode())).collect(Collectors.toList());
                List<ErpReimPayReceiptDto> payRecognRecords = getPayRecognRecords(fileRecords, dto, ecsDrugPayDetailVo.getId());
                insertRecords.addAll(payRecognRecords);
            }
        } else if(StringUtils.equals(dto.getSupType(),MedConst.TYPE_1)
                && (StringUtils.equals(dto.getType(),MedConst.TYPE_8)
                    || StringUtils.equals(dto.getType(),MedConst.TYPE_10))) {  //如果是零星采购或者物资采购报销
            //零星采购和物资采购现多个报销单关联一个付款单，不通过报销id去找已识别付款单数据，而是pay_rcpt_id关联
            List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailList(ids);
            //获取付款方式
            Set<String> payMethods = erpReimDetailVos.stream().map(ErpReimDetailVo::getPayMethod).collect(Collectors.toSet());
            if (payMethods.size() > 1) {
                log.error("选择的报销单付款方式不一致");
                throw new AppException("只能选择相同付款方式的报销生成凭证");
            }
            //如果都是现金支付或者复明工程，则没有付款文件，直接返回
            if (payMethods.size() == 1 && !StringUtils.equals(new ArrayList<>(payMethods).get(0),MedConst.TYPE_0)) {
                return rtVos;
            }
            //获取付款单pay_rcpt_id
            Set<Integer> payRcptIds = erpReimDetailVos.stream().map(ErpReimDetailVo::getPayRcptId).collect(Collectors.toSet());
            //查询报销文件中间表 ecs_drug_pay_detail
            EcsDrugPayDetailDto param = new EcsDrugPayDetailDto();
            param.setIds(new ArrayList<>(payRcptIds));
            List<EcsDrugPayDetailVo> ecsDrugPayDetailDtos = erpVcrDetailReadMapper.queryDrugPayDetail(param);
            //查询已识别付款单数据
            ErpReimPayReceiptDto rParam = new ErpReimPayReceiptDto();
            rParam.setSupType(dto.getSupType());
            rParam.setReimDetailIds(new ArrayList<>(payRcptIds));
            List<ErpReimPayReceiptVo> erpReimPayReceiptVos = erpReimPayReceiptReadMapper.queryList(rParam);
            //获取已识别付款单数据
            Set<String> attCodes = ecsDrugPayDetailDtos.stream().map(EcsDrugPayDetailVo::getAttCode).collect(Collectors.toSet());
            List<FileRecordEntity> fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(new ArrayList<>(attCodes));
            List<FileRecordEntity> payFileRecords = fileRecordEntities.stream()
                    .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2)).collect(Collectors.toList());
            if (erpReimPayReceiptVos.size() == payFileRecords.size()){
                return erpReimPayReceiptVos;
            }
            //删除当前报销对应的receipt识别数据
            LambdaQueryWrapper<ErpReimPayReceiptDto> receiptWrapper = Wrappers.lambdaQuery();
            receiptWrapper.in(ErpReimPayReceiptDto::getReimDetailId, payRcptIds)
                    .eq(ErpReimPayReceiptDto::getSupType, dto.getSupType());
            remove(receiptWrapper);
            for (int i = 0; i < ecsDrugPayDetailDtos.size(); i++) {
                EcsDrugPayDetailVo ecsDrugPayDetailVo = ecsDrugPayDetailDtos.get(i);
                List<FileRecordEntity> fileRecords = payFileRecords.stream().filter(item -> StringUtils.equals(item.getAttCode(), ecsDrugPayDetailVo.getAttCode())).collect(Collectors.toList());
                List<ErpReimPayReceiptDto> payRecognRecords = getPayRecognRecords(fileRecords, dto, ecsDrugPayDetailVo.getId());
                insertRecords.addAll(payRecognRecords);
            }
        } else {
            for (Integer id : ids) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    // 查询付款证明识别数据
                    List<ErpReimPayReceiptVo> erpReimPayReceiptVos = erpReimPayReceiptReadMapper.queryListById(dto.getSupType(),id);
                    // 查询报销付款证明文件
                    List<FileRecordEntity> fileRecordEntities = new ArrayList<>();
                    if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_1)) {
                        //费用报销
                        fileRecordEntities = erpVcrDetailReadMapper.queryFileRecord(Arrays.asList(id));
                    } else if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_2)){
                        //药品报销
                        List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(Arrays.asList(id));
                        List<String> attCodes = drugVos.stream().map(ErpDrugReimDetailVo::getAttCode).collect(Collectors.toList());
                        fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
                    } else if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_3)) {
                        //通过任务id查询当前报销
                        Certificate sParam = new Certificate();
                        sParam.setIds(Arrays.asList(id));
                        List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailWithSalaryId(sParam);
                        List<String> attCodes = erpReimDetailVos.stream().map(ErpReimDetailVo::getAttCode).collect(Collectors.toList());
                        fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
                    }

                    List<FileRecordEntity> collect = fileRecordEntities
                            .stream()
                            .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2))
                            .collect(Collectors.toList());

                    if (erpReimPayReceiptVos.size() == collect.size()) {
                        // 数量一致，则已生成数据
                        synchronized (rtVos) {
                            rtVos.addAll(erpReimPayReceiptVos);
                        }
                        return;
                    }

                    // 删除当前reimId对应的receipt识别数据
                    LambdaQueryWrapper<ErpReimPayReceiptDto> receiptWrapper = Wrappers.lambdaQuery();
                    receiptWrapper.eq(ErpReimPayReceiptDto::getReimDetailId, id)
                            .eq(ErpReimPayReceiptDto::getSupType, dto.getSupType());
                    remove(receiptWrapper);

                    // 识别生成票据数据
                    List<ErpReimPayReceiptDto> batchInsertRecords = getPayRecognRecords(collect, dto, id);

                    synchronized (insertRecords) {
                        insertRecords.addAll(batchInsertRecords);
                    }
                });

                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }

        // 保存
        saveBatch(insertRecords);

        // 返回数据
        insertRecords.stream().forEach(itemDto -> {
            ErpReimPayReceiptVo vo = new ErpReimPayReceiptVo();
            BeanUtils.copyProperties(itemDto, vo);
            rtVos.add(vo);
        });

        return rtVos;
    }


    //识别付款单数据
    private List<ErpReimPayReceiptDto> getPayRecognRecords(List<FileRecordEntity> payFiles,
                                                           ErpReimPayReceiptDto dto,
                                                           Integer id) {
        List<ErpReimPayReceiptDto> batchInsertRecords = new ArrayList<>();
        for (FileRecordEntity item : payFiles) {
            ErpReimPayReceiptDto receiptDto = new ErpReimPayReceiptDto();
            receiptDto.setSupType(dto.getSupType());
            receiptDto.setReimDetailId(id);
            receiptDto.setAtt(item.getAtt());
            receiptDto.setAttName(item.getAttName());
            receiptDto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
            receiptDto.setCrterTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            String recogJson = RecogPayReceiptUtil.alyRecognGnr(recogUrl, item.getAtt(), item.getAttName());
            receiptDto.setStatus(ErpConstants.RECOG_SUCCESS);

            // 在工具类中，将金额和日期识别后放入引用中
            RecogPayReceiptUtil.recogStrToPayInfo(recogJson, receiptDto);

            try {
                if (StringUtils.equals(ErpConstants.BUSINESS_AUTHORIZATION_LETTER, receiptDto.getType())) {
                    // 业务委托书
                    receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr().trim()
                            .replace("￥", "")
                            .replace("，", "")));
                } else if (StringUtils.equals(ErpConstants.NATION_PAYMENT_VOUCHER, receiptDto.getType())) {
                    // 国库集中支付凭证， 日期示例2024年12月09日  金额 ￥145,434.74
                    // 日期不用处理，金额替换
                    receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr().trim()
                            .replace("￥", "")
                            .replace("，", "")));
                } else if (StringUtils.equals(ErpConstants.NATION_PAYMETN_RECEIPT, receiptDto.getType())) {
                    // 国内支付业务付款回单 日期示例: 日期：2024年12月09日   金额示例：金额：CNY 1，412.35
                    receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr()
                            .split("CNY")[1].trim().replace("，", "")));
                    receiptDto.setPayDate(receiptDto.getPayDate().trim()
                            .replace("日期", "")
                            .replace("：", "")
                    );
                } else if (StringUtils.equals(ErpConstants.BUSINESS_SETTLEMENT_LETTER, receiptDto.getType())) {
                    // 结算业务申请书 日期示例：2024年12月09  金额示例：人民币￥98，874.25
                    receiptDto.setPayAmt(new BigDecimal(receiptDto.getPayAmtStr().trim()
                            .replace("人民币", "")
                            .replace("￥", "")
                            .replace("，", "")));
                }
            } catch (Exception e) {
                receiptDto.setStatus(ErpConstants.RECOG_FAILD);
                log.error(String.format("文件：{}解析异常", item.getAttName()), e);
            }

            batchInsertRecords.add(receiptDto);
        }
        return batchInsertRecords;
    }
}
