package com.jp.med.erp.modules.vcrGen.mapper.read;

import com.jp.med.erp.modules.vcrGen.dto.ErpVcrItemDetailDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpVcrItemDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 凭证信息明细
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Mapper
public interface ErpVcrItemDetailReadMapper extends BaseMapper<ErpVcrItemDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrItemDetailVo> queryList(ErpVcrItemDetailDto dto);
}
