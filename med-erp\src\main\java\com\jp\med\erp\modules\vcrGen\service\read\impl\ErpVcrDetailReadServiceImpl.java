package com.jp.med.erp.modules.vcrGen.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.ecs.EcsReimFeignService;
import com.jp.med.common.vo.EcsReimDeprTaskVo;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrDetailDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrItemDetailDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrPreviewDto;
import com.jp.med.erp.modules.vcrGen.entity.Certificate;
import com.jp.med.erp.modules.vcrGen.entity.ErpReimItemDetail;
import com.jp.med.erp.modules.vcrGen.entity.ErpReimSubsItemDetail;
import com.jp.med.erp.modules.vcrGen.entity.FileRecordEntity;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrItemDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrPreviewReadMapper;
import com.jp.med.erp.modules.vcrGen.service.read.ErpVcrDetailReadService;
import com.jp.med.erp.modules.vcrGen.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
@Slf4j
public class ErpVcrDetailReadServiceImpl extends ServiceImpl<ErpVcrDetailReadMapper, ErpVcrDetailDto> implements ErpVcrDetailReadService {

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;
    
    @Autowired
    private ErpVcrItemDetailReadMapper erpVcrItemDetailReadMapper;

    @Autowired
    private EcsReimFeignService ecsReimFeignService;

    @Autowired
    private ErpVcrPreviewReadMapper erpVcrPreviewReadMapper;

    @Override
    public List<ErpVcrDetailVo> queryList(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpVcrDetailReadMapper.queryList(dto);
    }

    @Override
    public List<ErpReimDetailVo> queryVcrGenList(ErpVcrDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
        List<ErpReimDetailVo> erpReimDetailVos = new ArrayList<>();
        //零星采购和物资采购报销查询方式不同
        if (StringUtils.equals(dto.getType(),MedConst.TYPE_8) || StringUtils.equals(dto.getType(),MedConst.TYPE_10)) {
            erpReimDetailVos = erpVcrDetailReadMapper.queryPurcVcrGenList(dto);
        } else {
            erpReimDetailVos = erpVcrDetailReadMapper.queryVcrGenList(dto);
            if (CollectionUtil.isNotEmpty(erpReimDetailVos)){
                erpReimDetailVos.forEach(erpReimDetailVo -> {
                    List<String> time = new ArrayList<>();
                    time.add(erpReimDetailVo.getEvectionBegnTime());
                    time.add(erpReimDetailVo.getEvectionEndTime());
                    erpReimDetailVo.setEvectionTime(time);
                });
            }
        }

        return erpReimDetailVos;
    }

    @Override
    public List<ErpReimDetailVo> drugToVcrList(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
        return erpVcrDetailReadMapper.drugToVcrList(dto);
    }

    @Override
    public List<ErpReimSalaryTaskVo> salaryToVcrList(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
        return erpVcrDetailReadMapper.salaryToVcrList(dto);
    }

    @Override
    public List<EcsReimDeprTaskVo> deprToVcrList(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
        return erpVcrDetailReadMapper.deprToVcrList(dto);
    }

    @Override
    public List<ErpReimTravelApprVo> queryApprInfo(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return erpVcrDetailReadMapper.queryApprInfo(dto);
    }

    @Override
    public List<ErpReimDetailVo> queryReimInfo(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return erpVcrDetailReadMapper.queryReimInfo(dto);
    }

    @Override
    public Map<String, Object> queryVcr(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        //查询凭证申请信息
        List<Certificate> certificates = erpVcrDetailReadMapper.queryVcrApplyMsg(dto);
        //查询凭证
        List<ErpVcrDetailVo> vcrList = erpVcrDetailReadMapper.queryList(dto);
        //查询辅助项信息
        List<ErpReimAsstVo> vcrAsstList = erpVcrDetailReadMapper.queryVcrAsst(dto);


        List<ErpReimItemDetail> vcrItemList = new ArrayList<>();
        List<ErpReimSubsItemDetail> vcrSubItemList = new ArrayList<>();
        List<FileRecordEntity> fileRecords = new ArrayList<>();
        List<ErpStoinVo> drugStoins = new ArrayList<>();

        LambdaQueryWrapper<ErpVcrItemDetailDto> pzidWrapper = Wrappers.lambdaQuery();
        pzidWrapper.eq(ErpVcrItemDetailDto::getIdpzh,dto.getIdpzh());
        List<ErpVcrItemDetailDto> erpVcrItemDetailDtos = erpVcrItemDetailReadMapper.selectList(pzidWrapper);
        //pzid
        String pzid = erpVcrItemDetailDtos.get(0).getPzid();
        //查询附件信息，根据不同大类的报销查询不同信息
        if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_1)) {     //费用报销
            Set<Integer> reimIds = new HashSet<>();
            //如果是零星、物资采购
            if (StringUtils.equals(dto.getType(),MedConst.TYPE_8) || StringUtils.equals(dto.getType(),MedConst.TYPE_10)) {
                //兼容老数据查询

                List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailVoByIdPzh(dto.getIdpzh());
                Set<String> attCodes = erpReimDetailVos.stream().map(ErpReimDetailVo::getAttCode2).collect(Collectors.toSet());
                if (CollectionUtil.isEmpty(attCodes)) {
                    if (CollectionUtil.isNotEmpty(vcrAsstList)){
                        vcrAsstList.stream().forEach(item -> reimIds.add(item.getReimDetailId()));
                    }
                    vcrItemList = erpVcrDetailReadMapper.queryItemDetail(new ArrayList<>(reimIds));
                    vcrSubItemList = erpVcrDetailReadMapper.querySubItemDetail(new ArrayList<>(reimIds));
                    fileRecords = erpVcrDetailReadMapper.queryFileRecord(new ArrayList<>(reimIds));
                } else {
                    erpReimDetailVos.stream().forEach(item -> reimIds.add(item.getId()));
                    vcrItemList = erpVcrDetailReadMapper.queryItemDetail(new ArrayList<>(reimIds));
                    vcrSubItemList = erpVcrDetailReadMapper.querySubItemDetail(new ArrayList<>(reimIds));
                    fileRecords = erpVcrDetailReadMapper.queryFileRecord(new ArrayList<>(reimIds));

                    //付款文件信息
                    fileRecords.addAll(erpVcrDetailReadMapper.queryFileRecordByCode(new ArrayList<>(attCodes)));
                    vcrAsstList = erpVcrDetailReadMapper.queryVcrAsstByvpzh(dto);
                }

            } else {
                if (CollectionUtil.isNotEmpty(vcrAsstList)){
                    vcrAsstList.stream().forEach(item -> reimIds.add(item.getReimDetailId()));
                }
                vcrItemList = erpVcrDetailReadMapper.queryItemDetail(new ArrayList<>(reimIds));
                vcrSubItemList = erpVcrDetailReadMapper.querySubItemDetail(new ArrayList<>(reimIds));
                fileRecords = erpVcrDetailReadMapper.queryFileRecord(new ArrayList<>(reimIds));
            }

        } else if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_2)){                    //药品报销
            List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVoByIdPzh(dto.getIdpzh());
            Set<String> attCodes = drugVos.stream().map(ErpDrugReimDetailVo::getAttCode).collect(Collectors.toSet());
            //文件信息 主要是付款单
            fileRecords = erpVcrDetailReadMapper.queryFileRecordByCode(new ArrayList<>(attCodes));
            //查询入库单信息 发票文件
            drugStoins = erpVcrDetailReadMapper.queryDrugStoinVoByReimIds(drugVos.stream().map(e -> e.getId().intValue()).collect(Collectors.toList()));
            vcrAsstList = erpVcrDetailReadMapper.queryVcrAsstByvpzh(dto);
        } else if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_3)) {                          //工资
            //通过任务id查询当前报销
            Certificate cc = new Certificate();
            cc.setIds(Arrays.asList(Integer.valueOf(pzid)));
            List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailWithSalaryId(cc);
            //获取文件code
            List<String> attCodes = erpReimDetailVos.stream().map(ErpReimDetailVo::getAttCode).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(attCodes)) {
                fileRecords = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
            }
        } else if (StringUtils.equals(dto.getSupType(),MedConst.TYPE_4)) {              //折旧
            //折旧任务
            EcsReimDeprTaskDto dd = new EcsReimDeprTaskDto();
            dd.setId(Integer.valueOf(pzid));
            CommonResult<List<EcsReimDeprTaskVo>> taskRes = ecsReimFeignService.list(dd);
            if (Objects.isNull(taskRes) || CollectionUtil.isEmpty(taskRes.getData())) {
                log.error(String.format("查询折旧任务失败，折旧任务id:{}", pzid));
                throw new AppException(String.format("查询折旧任务失败，折旧任务id%s", pzid));
            }
            List<EcsReimDeprTaskVo> deprTasks = taskRes.getData();

            //获取文件code
            List<String> attCodes = deprTasks.stream().map(EcsReimDeprTaskVo::getAttCode).collect(Collectors.toList());
            fileRecords = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
        }

        Map<String,Object> result = new HashMap<>();
        result.put("certificate",certificates.get(0));
        result.put("vcr",vcrList.get(0));
        result.put("vcrAsstList",vcrAsstList);
        result.put("vcrItemList",vcrItemList);
        result.put("vcrSubItemList",vcrSubItemList);
        result.put("fileRecords",fileRecords);
        result.put("drugStoins",drugStoins);
        return result;
    }

    @Override
    public Map<String, Object> queryAsst(Certificate dto) {
        List<ErpReimItemDetail> vcrItemList = new ArrayList<>();
        List<ErpReimSubsItemDetail> vcrSubItemList = new ArrayList<>();
        List<FileRecordEntity> fileRecords = new ArrayList<>();
        List<ErpReimAsstVo> erpReimAsstVos = new ArrayList<>();
        List<ErpStoinVo> drugStoins = new ArrayList<>();
        //根据不同报销类型查询不同数据
        if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_1)) {
            //费用报销
            vcrItemList = erpVcrDetailReadMapper.queryItemDetail(dto.getIds());
            vcrSubItemList = erpVcrDetailReadMapper.querySubItemDetail(dto.getIds());
            //如果是零星采购或者物资采购
            if (StringUtils.equals(dto.getType(), MedConst.TYPE_8) || StringUtils.equals(dto.getType(), MedConst.TYPE_10)) {
                //查询报销信息
                List<ErpReimDetailVo> purcVos = erpVcrDetailReadMapper.queryPurcReimDetailVo(dto.getIds());
                Set<String> attCodes = purcVos.stream().map(ErpReimDetailVo::getAttCode2).collect(Collectors.toSet());
                fileRecords = erpVcrDetailReadMapper.queryFileRecord(dto.getIds());
                fileRecords.addAll(erpVcrDetailReadMapper.queryFileRecordByCode(new ArrayList<>(attCodes)));
                //辅助项目
                erpReimAsstVos = erpVcrDetailReadMapper.queryAsstByPreviewVcr(dto);
            } else {
                fileRecords = erpVcrDetailReadMapper.queryFileRecord(dto.getIds());
                erpReimAsstVos = erpVcrDetailReadMapper.queryAsst(dto);
            }

        } else if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_2)){
            //药品报销
            //1.查询药品报销信息
            List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(dto.getIds());
            List<String> attCodes = drugVos.stream().map(ErpDrugReimDetailVo::getAttCode).collect(Collectors.toList());
            //文件信息 主要是付款单
            fileRecords = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
            //查询入库单信息 发票文件
            drugStoins = erpVcrDetailReadMapper.queryDrugStoinVoByReimIds(dto.getIds());
            //辅助项目
            erpReimAsstVos = erpVcrDetailReadMapper.queryAsstByPreviewVcr(dto);
        } else if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_3)) { //工资
            //通过任务id查询当前报销
            List<ErpReimDetailVo> erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailWithSalaryId(dto);
            //获取文件code
            List<String> attCodes = erpReimDetailVos.stream().map(ErpReimDetailVo::getAttCode).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(attCodes)) {
                fileRecords = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
            }
            //辅助项目
            erpReimAsstVos = erpVcrDetailReadMapper.queryAsst(dto);
        } else if (StringUtils.equals(dto.getSupType(), MedConst.TYPE_4)) {     //折旧
            EcsReimDeprTaskDto dd = new EcsReimDeprTaskDto();
            Integer deprId = dto.getIds().get(0);
            dd.setId(deprId);
            CommonResult<List<EcsReimDeprTaskVo>> taskRes = ecsReimFeignService.list(dd);
            if (Objects.isNull(taskRes) || CollectionUtil.isEmpty(taskRes.getData())) {
                log.error(String.format("查询折旧任务失败，折旧任务id:{}", deprId));
                throw new AppException(String.format("查询折旧任务失败，折旧任务id%s", deprId));
            }
            List<EcsReimDeprTaskVo> deprTasks = taskRes.getData();
            //获取文件code
            List<String> attCodes = deprTasks.stream().map(EcsReimDeprTaskVo::getAttCode).collect(Collectors.toList());
            fileRecords = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
            erpReimAsstVos = erpVcrDetailReadMapper.queryAsst(dto);
        }

        Map<String,Object> result = new HashMap<>();
        result.put("vcrItemList",vcrItemList);
        result.put("vcrSubItemList",vcrSubItemList);
        result.put("vcrAsstList",erpReimAsstVos);
        result.put("fileRecords",fileRecords);
        result.put("drugStoins",drugStoins);
        return result;
    }

    /**
     * 查询工资个人扣减-临时扣款人员没有对应配置的员工
     * @param dto
     * @return
     */
    @Override
    public List<ErpVcrSalaryConfigVo> queryToConfigSalary(ErpVcrDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return erpVcrDetailReadMapper.queryToConfigSalary(dto);
    }

    /**
     * 查询药品当前的vpzh
     * @param dto
     * @return
     */
    @Override
    public String queryDrugVpzh(ErpVcrDetailDto dto) {
        LambdaQueryWrapper<ErpVcrPreviewDto> preWrapper = Wrappers.lambdaQuery(ErpVcrPreviewDto.class);
        preWrapper.in(ErpVcrPreviewDto::getModuleId,dto.getIds())
                .eq(ErpVcrPreviewDto::getSupType,dto.getSupType())
                .eq(ErpVcrPreviewDto::getPayType,dto.getType());
        List<ErpVcrPreviewDto> erpVcrPreviewDtos = erpVcrPreviewReadMapper.selectList(preWrapper);
        //去重
        Set<String> vpzhList = erpVcrPreviewDtos.stream().map(ErpVcrPreviewDto::getVpzh).collect(Collectors.toSet());
        if (vpzhList.size() == 0) {
            return MedConst.TYPE_0;
        }

        if (vpzhList.size() > 1 ) {
            log.error("当前选择的报销属于多个凭证,报销id:{}",dto.getIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
            //不抛出异常，需要前端用户选择处理
            return "-1";
        }
        //判断当前选择报销是否为凭证的所有报销
        LambdaQueryWrapper<ErpVcrPreviewDto> vpzWrapper = Wrappers.lambdaQuery(ErpVcrPreviewDto.class);
        vpzWrapper.in(ErpVcrPreviewDto::getVpzh,vpzhList);
        List<ErpVcrPreviewDto> erpVcrPreviewDtos1 = erpVcrPreviewReadMapper.selectList(vpzWrapper);
        //去重
        Set<Integer> collect = erpVcrPreviewDtos1.stream().map(ErpVcrPreviewDto::getModuleId).collect(Collectors.toSet());
        if (collect.size() != dto.getIds().size()) {
            log.error("当前选择的报销不属于凭证的所有报销,报销id:{}",dto.getIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
            //不抛出异常，需要前端用户选择处理
            return "-2";
        }

        return vpzhList.stream().findFirst().get();
    }
}
