package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import cn.hutool.core.text.StrFormatter;
import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.jp.med.ams.modules.depr.mapper.read.AmsDeprAsgnReadMapper;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsDeprAsgnVo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.common.exception.AppException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 多科室分摊处理器
 * 负责处理一个资产有多个使用科室的情况
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyMultiDeptProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Autowired
    private AmsDeprAsgnReadMapper amsDeprAsgnReadMapper;

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理多科室分摊");

        // 处理普通资产的多科室分摊
        processMultiDeptUse(context.getNormalAssets());

        log.debug("多科室分摊处理完成");
    }

    /**
     * 处理多个使用科室的资产
     *
     * @param normalAssets 普通资产列表
     */
    private void processMultiDeptUse(java.util.List<AmsPropertyVo> normalAssets) {
        // 查询资产使用科室分配比例
        var splitAssetList = new ArrayList<AmsPropertyVo>();
        var waitDelFaCodeMap = new HashMap<String, AmsPropertyVo>();

        normalAssets
                .stream()
                .filter(asset -> asset.getDeptUse().contains(","))
                .forEach(asset -> {
                    AmsDeprAsgnDto query = new AmsDeprAsgnDto();
                    query.setFaCode(asset.getFaCode());
                    Collection<AmsDeprAsgnVo> deprAsgnVos = amsDeprAsgnReadMapper.queryAsgn(query);
                    if (!deprAsgnVos.isEmpty()) {
                        waitDelFaCodeMap.put(asset.getFaCode(), asset);
                        // 检测分摊比例之和是否为 1
                        BigDecimal propSum = deprAsgnVos.stream().map(AmsDeprAsgnVo::getProp).reduce(BigDecimal.ZERO,
                                BigDecimal::add);
                        if (propSum.compareTo(BigDecimal.ONE) != 0) {
                            log.error("资产 {} 的分摊比例之和为 {}，请检查数据", asset.getFaCode(), propSum);
                            var msg = StrFormatter.format("资产 {}:{} 的分摊比例之和为 {}，请检查数据", asset.getFaCode(),
                                    asset.getAssetName(), propSum);
                            throw new AppException(
                                    "资产 " + asset.getFaCode() + " 的分摊比例之和为 " + propSum + "，请联系资产管理员配置数据");
                        }
                        deprAsgnVos.forEach(deprAsgnVo -> {
                            AmsPropertyVo newAsset = new AmsPropertyVo();
                            BeanUtils.copyProperties(asset, newAsset);
                            newAsset.setDeptUse(deprAsgnVo.getOrgId());
                            // 设置原值，月折旧额，累计折旧额，净值 x 分摊比例
                            // 原值
                            newAsset.setAssetNav(asset.getAssetNav().multiply(deprAsgnVo.getProp()).setScale(2,
                                    RoundingMode.HALF_UP));
                            // 月折旧额
                            newAsset.setDeprMon(asset.getDeprMon().multiply(deprAsgnVo.getProp()).setScale(2,
                                    RoundingMode.HALF_UP));
                            // 净值
                            newAsset.setNbv(
                                    asset.getNbv().multiply(deprAsgnVo.getProp()).setScale(2, RoundingMode.HALF_UP));
                            // 累计折旧额
                            newAsset.setDep(
                                    asset.getDep().multiply(deprAsgnVo.getProp()).setScale(2, RoundingMode.HALF_UP));
                            splitAssetList.add(newAsset);
                        });
                    }
                });

        // 循环等待删除资产 计算补齐因为x分摊比例 导致的新资产金额之和与原资产的差值
        BigDecimal totalOriginalValue = BigDecimal.ZERO; // 所有待删除资产的原值之和
        BigDecimal totalNetValue = BigDecimal.ZERO; // 所有待删除资产的净值之和
        BigDecimal totalAccumDepr = BigDecimal.ZERO; // 所有待删除资产的累计折旧之和
        BigDecimal totalDeprMon = BigDecimal.ZERO; // 所有待删除资产的月折旧额之和

        for (Map.Entry<String, AmsPropertyVo> entry : waitDelFaCodeMap.entrySet()) {
            AmsPropertyVo assetValue = entry.getValue();

            // 计算原值之和
            if (assetValue.getAssetNav() != null) {
                totalOriginalValue = totalOriginalValue.add(assetValue.getAssetNav());
            }

            // 计算净值之和
            if (assetValue.getNbv() != null) {
                totalNetValue = totalNetValue.add(assetValue.getNbv());
            }

            // 计算累计折旧之和
            if (assetValue.getDep() != null) {
                totalAccumDepr = totalAccumDepr.add(assetValue.getDep());
            }

            // 计算月折旧额之和
            if (assetValue.getDeprMon() != null) {
                totalDeprMon = totalDeprMon.add(assetValue.getDeprMon());
            }
        }

        // 计算新拆分资产之和
        BigDecimal splitOriginalValue = BigDecimal.ZERO;
        BigDecimal splitNetValue = BigDecimal.ZERO;
        BigDecimal splitAccumDepr = BigDecimal.ZERO;
        BigDecimal splitDeprMon = BigDecimal.ZERO;

        for (AmsPropertyVo newAsset : splitAssetList) {
            if (newAsset.getAssetNav() != null) {
                splitOriginalValue = splitOriginalValue.add(newAsset.getAssetNav());
            }
            if (newAsset.getNbv() != null) {
                splitNetValue = splitNetValue.add(newAsset.getNbv());
            }
            if (newAsset.getDep() != null) {
                splitAccumDepr = splitAccumDepr.add(newAsset.getDep());
            }
            if (newAsset.getDeprMon() != null) {
                splitDeprMon = splitDeprMon.add(newAsset.getDeprMon());
            }
        }

        // 计算差值
        BigDecimal originalValueDiff = totalOriginalValue.subtract(splitOriginalValue);
        BigDecimal netValueDiff = totalNetValue.subtract(splitNetValue);
        BigDecimal accumDeprDiff = totalAccumDepr.subtract(splitAccumDepr);
        BigDecimal deprMonDiff = totalDeprMon.subtract(splitDeprMon);

        // 如果有差值，需要在新资产中进行调整
        if (originalValueDiff.compareTo(BigDecimal.ZERO) != 0 ||
                netValueDiff.compareTo(BigDecimal.ZERO) != 0 ||
                accumDeprDiff.compareTo(BigDecimal.ZERO) != 0 ||
                deprMonDiff.compareTo(BigDecimal.ZERO) != 0) {

            // 找到一个资产进行调整，通常是最大的那个资产
            AmsPropertyVo largestAsset = null;
            BigDecimal maxValue = BigDecimal.ZERO;

            for (AmsPropertyVo newAsset : splitAssetList) {
                if (newAsset.getAssetNav() != null &&
                        newAsset.getAssetNav().compareTo(maxValue) > 0) {
                    maxValue = newAsset.getAssetNav();
                    largestAsset = newAsset;
                }
            }

            // 对最大资产进行差值调整
            if (largestAsset != null) {
                if (largestAsset.getAssetNav() != null) {
                    largestAsset.setAssetNav(largestAsset.getAssetNav().add(originalValueDiff));
                }
                if (largestAsset.getNbv() != null) {
                    largestAsset.setNbv(largestAsset.getNbv().add(netValueDiff));
                }
                if (largestAsset.getDep() != null) {
                    largestAsset.setDep(largestAsset.getDep().add(accumDeprDiff));
                }
                if (largestAsset.getDeprMon() != null) {
                    largestAsset.setDeprMon(largestAsset.getDeprMon().add(deprMonDiff));
                }
            }
        }

        // 从原列表中移除待删除的资产
        waitDelFaCodeMap.keySet().forEach(faCode -> normalAssets.removeIf(asset -> asset.getFaCode().equals(faCode)));

        // 添加拆分后的资产
        normalAssets.addAll(splitAssetList);
    }
}
