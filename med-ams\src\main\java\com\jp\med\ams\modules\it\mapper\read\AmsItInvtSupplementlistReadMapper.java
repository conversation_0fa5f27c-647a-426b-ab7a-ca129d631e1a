package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItInvtSupplementlistDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtSupplementlistVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * <AUTHOR>
 * @email -
 * @date 2023-12-14 17:42:02
 */
@Mapper
public interface AmsItInvtSupplementlistReadMapper extends BaseMapper<AmsItInvtSupplementlistDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItInvtSupplementlistVo> queryList(AmsItInvtSupplementlistDto dto);

    /**
     * 查询最大id
     * @return
     */
    Integer queryMaxId();

    List<AmsItInvtSupplementlistVo> querySupplement(AmsItInvtSupplementlistDto dto);

    List<AmsItInvtSupplementlistVo> queryDetialsList(AmsItInvtSupplementlistDto dto);
}
