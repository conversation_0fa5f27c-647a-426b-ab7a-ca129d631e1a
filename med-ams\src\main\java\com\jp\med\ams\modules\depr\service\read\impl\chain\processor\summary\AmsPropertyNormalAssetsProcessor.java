package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 普通资产处理器
 * 负责处理普通资产的折旧计算
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyNormalAssetsProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理普通资产");

        // 处理普通资产分组数据
        processNormalAssets(
                context.getGroupedNormalAssets(),
                context.getResult(),
                context.getUsedFacodeSet(),
                context.getLastDayOfMonth(),
                context.getNormalAssets(),
                context.isUseNewType());

        log.debug("普通资产处理完成，共处理 {} 条记录", context.getResult().size());
    }

    /**
     * 处理普通资产
     */
    private void processNormalAssets(
            Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedAssets,
            List<AmsPropertyDepr2Vo> result,
            java.util.Set<String> usedFacodeSet,
            java.time.LocalDate lastDayOfMonth,
            List<AmsPropertyVo> allAssets,
            Boolean useNewType) {

        for (Map.Entry<String, Map<String, Map<String, List<AmsPropertyVo>>>> deptEntry : groupedAssets.entrySet()) {
            for (Map.Entry<String, Map<String, List<AmsPropertyVo>>> typeEntry : deptEntry.getValue().entrySet()) {
                for (Map.Entry<String, List<AmsPropertyVo>> sourceEntry : typeEntry.getValue().entrySet()) {
                    AmsPropertyDepr2Vo vo = createDeprVo(
                            deptEntry.getKey(),
                            typeEntry.getKey(),
                            sourceEntry.getKey(),
                            sourceEntry.getValue(),
                            usedFacodeSet,
                            lastDayOfMonth,
                            allAssets, useNewType);
                    vo.setIsAmortizedAssets(false);

                    result.add(vo);
                }
            }
        }
    }

    /**
     * 创建折旧VO对象
     */
    private AmsPropertyDepr2Vo createDeprVo(
            String deptCode,
            String assetTypeCode,
            String sourceCode,
            List<AmsPropertyVo> assets,
            java.util.Set<String> usedFacodeSet,
            java.time.LocalDate lastDayOfMonth,
            List<AmsPropertyVo> allAssets,
            Boolean useNewType) {

        AmsPropertyDepr2Vo vo = new AmsPropertyDepr2Vo();
        vo.setFaCodes(assets.stream()
                .map(AmsPropertyVo::getFaCode)
                .peek(usedFacodeSet::add)
                .collect(Collectors.joining(",")));
        vo.setDeptUseCode(deptCode);
        vo.setAssetTypeCode(assetTypeCode);
        vo.setSourceCode(sourceCode);

        // 设置名称
        allAssets.stream()
                .filter(item -> item.getDeptUse().equals(deptCode))
                .findFirst()
                .ifPresent(item -> vo.setDeptUseName(item.getDeptUseName()));

        allAssets.stream()
                .filter(item -> useNewType.equals(Boolean.TRUE) ? item.getAssetTypeN().equals(assetTypeCode)
                        : item.getAssetType().equals(assetTypeCode))
                .findFirst()
                .ifPresent(item -> {
                    if (useNewType) {
                        vo.setAssetTypeName(item.getAssetTypeNName());
                    } else {
                        vo.setAssetTypeName(item.getAssetTypeName());
                    }
                });

        // 计算本月原值总额
        BigDecimal totalOriginalValue = calculateTotalOriginalValue(assets);
        vo.setMonthDeprAmt(totalOriginalValue);

        // 计算月折旧额和累计折旧额
        BigDecimal totalMonthlyDeprAmount = BigDecimal.ZERO;
        BigDecimal totalDeprAmtSum = BigDecimal.ZERO;
        BigDecimal weightedDeprRate = BigDecimal.ZERO;
        BigDecimal totalWeight = BigDecimal.ZERO;

        // 对每个资产分别汇总
        for (AmsPropertyVo asset : assets) {

            BigDecimal monthlyDeprRate = asset.getDeprratMon();
            if (monthlyDeprRate == null) {
                monthlyDeprRate = BigDecimal.ONE.divide(
                        asset.getUl().multiply(BigDecimal.valueOf(12)),
                        10,
                        RoundingMode.HALF_UP);
            }

            if (asset.getDeprMon() != null) {
                totalMonthlyDeprAmount = totalMonthlyDeprAmount.add(asset.getDeprMon());
            }
            if (asset.getDep() != null) {
                totalDeprAmtSum = totalDeprAmtSum.add((asset.getDep()));
            }

            // 计算加权平均折旧率
            weightedDeprRate = weightedDeprRate.add(monthlyDeprRate.multiply(asset.getAssetNav()));
            totalWeight = totalWeight.add(asset.getAssetNav());
        }

        // 设置月折旧额
        vo.setDeprAmt(totalMonthlyDeprAmount);

        // 设置累计折旧额
        vo.setDeprAmtSum(totalDeprAmtSum);

        // 设置加权平均折旧率（百分比），这一步可以进行四舍五入，因为是个比率
        if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
            vo.setDeprRate(weightedDeprRate.divide(totalWeight, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)));
        } else {
            vo.setDeprRate(BigDecimal.ZERO);
        }

        vo.setIsSummary(false);
        return vo;
    }

    /**
     * 计算原值总和
     */
    private BigDecimal calculateTotalOriginalValue(List<AmsPropertyVo> assets) {
        return assets.stream()
                .map(AmsPropertyVo::getAssetNav)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
