package com.jp.med.ams.modules.changes.controller;

import com.jp.med.ams.modules.changes.scheduler.BorrowingReminderScheduler;
import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 资产借用消息通知控制器
 * 提供手动触发通知和管理通知的接口
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
@Slf4j
@Api(value = "资产借用消息通知", tags = "资产借用消息通知")
@RestController
@RequestMapping("amsBorrowingNotification")
public class AmsBorrowingNotificationController {

    @Autowired
    private AmsBorrowingNotificationService notificationService;

    @Autowired
    private BorrowingReminderScheduler reminderScheduler;

    /**
     * 手动触发到期提醒任务
     * 用于测试或紧急情况下的手动执行
     */
    @ApiOperation("手动触发到期提醒任务")
    @PostMapping("/trigger/expiryReminders")
    public CommonResult<String> triggerExpiryReminders() {
        log.info("🔧 手动触发到期提醒任务");

        try {
            reminderScheduler.manualTriggerExpiryReminders();
            return CommonResult.success("到期提醒任务执行成功");
        } catch (Exception e) {
            log.error("❌ 手动触发到期提醒任务失败", e);
            return CommonResult.error("到期提醒任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发逾期警告任务
     * 用于测试或紧急情况下的手动执行
     */
    @ApiOperation("手动触发逾期警告任务")
    @PostMapping("/trigger/overdueWarnings")
    public CommonResult<String> triggerOverdueWarnings() {
        log.info("🔧 手动触发逾期警告任务");

        try {
            reminderScheduler.manualTriggerOverdueWarnings();
            return CommonResult.success("逾期警告任务执行成功");
        } catch (Exception e) {
            log.error("❌ 手动触发逾期警告任务失败", e);
            return CommonResult.error("逾期警告任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 发送系统维护通知
     * 给所有相关用户发送系统维护通知
     */
    @ApiOperation("发送系统维护通知")
    @PostMapping("/send/maintenanceNotification")
    public CommonResult<String> sendMaintenanceNotification(
            @RequestParam String maintenanceMessage,
            @RequestParam String startTime,
            @RequestParam String endTime) {
        log.info("🔧 发送系统维护通知: 开始时间={}, 结束时间={}", startTime, endTime);

        try {
            notificationService.sendSystemMaintenanceNotification(maintenanceMessage, startTime, endTime);
            return CommonResult.success("系统维护通知发送成功");
        } catch (Exception e) {
            log.error("❌ 发送系统维护通知失败", e);
            return CommonResult.error("系统维护通知发送失败: " + e.getMessage());
        }
    }

    /**
     * 测试通知功能
     * 发送测试通知以验证通知系统是否正常工作
     */
    @ApiOperation("测试通知功能")
    @PostMapping("/test")
    public CommonResult<String> testNotification(@RequestParam String testMessage) {
        log.info("🧪 测试通知功能: {}", testMessage);

        try {
            // 这里可以发送一个测试通知
            notificationService.sendSystemMaintenanceNotification(
                    "这是一条测试消息: " + testMessage,
                    "立即",
                    "立即"
            );
            return CommonResult.success("测试通知发送成功");
        } catch (Exception e) {
            log.error("❌ 测试通知发送失败", e);
            return CommonResult.error("测试通知发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取通知系统状态
     * 返回通知系统的运行状态信息
     */
    @ApiOperation("获取通知系统状态")
    @GetMapping("/status")
    public CommonResult<Object> getNotificationStatus() {
        log.info("📊 获取通知系统状态");

        try {
            // 这里可以返回通知系统的状态信息
            return CommonResult.success("通知系统运行正常");
        } catch (Exception e) {
            log.error("❌ 获取通知系统状态失败", e);
            return CommonResult.failed("获取通知系统状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量发送到期提醒
     * 立即执行批量到期提醒任务
     */
    @ApiOperation("批量发送到期提醒")
    @PostMapping("/batch/expiryReminders")
    public CommonResult<String> sendBatchExpiryReminders() {
        log.info("📬 批量发送到期提醒");

        try {
            notificationService.sendBatchExpiryReminders();
            return CommonResult.success("批量到期提醒发送成功");
        } catch (Exception e) {
            log.error("❌ 批量发送到期提醒失败", e);
            return CommonResult.error("批量到期提醒发送失败: " + e.getMessage());
        }
    }

    /**
     * 批量发送逾期警告
     * 立即执行批量逾期警告任务
     */
    @ApiOperation("批量发送逾期警告")
    @PostMapping("/batch/overdueWarnings")
    public CommonResult<String> sendBatchOverdueWarnings() {
        log.info("📬 批量发送逾期警告");

        try {
            notificationService.sendBatchOverdueWarnings();
            return CommonResult.success("批量逾期警告发送成功");
        } catch (Exception e) {
            log.error("❌ 批量发送逾期警告失败", e);
            return CommonResult.error("批量逾期警告发送失败: " + e.getMessage());
        }
    }
}
