package com.jp.med.erp.modules.vcrGen.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimPayReceiptDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

@Slf4j
public class RecogPayReceiptUtil {

    /*public static List<Map<String,String>> alyPayInfoMaps(String url,List<FileRecordEntity> fileRecords) {
        List<Map<String,String>> maps = new ArrayList<>();
        for (FileRecordEntity fileRecord : fileRecords) {
            Map<String, String> stringStringMap = alyRecognGnr(url,fileRecord.getAtt(), fileRecord.getAttName());
            if (!stringStringMap.isEmpty()) {
                maps.add(stringStringMap);
            }
        }
        return maps;
    }*/


    public static String alyRecognGnr(String recognUrl,String attPath,String attName) {
        String result = "";
        try {
            String boundary = "---------------------------1234567890";   //定义boundary
            URL url = new URL(recognUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            OutputStream outputStream = connection.getOutputStream();
            PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true);

            // 添加文件数据
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + attName + "\"").append("\r\n");
            writer.append("Content-Type: " + HttpURLConnection.guessContentTypeFromName(attName)).append("\r\n");
            writer.append("\r\n");
            writer.flush();

            InputStream fileInputStream = OSSUtil.getObject(OSSConst.BUCKET_ECS,attPath);
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();
            fileInputStream.close();

            writer.append("\r\n");
            writer.append("--" + boundary + "--").append("\r\n");
            writer.close();

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                response.append(line);
            }

            reader.close();
            outputStream.close();
            connection.disconnect();

            //处理JSON响应
            String post = response.toString();
            log.error("当前通用文字识别返回结果string-------------" + post);
            if (StringUtils.isNotEmpty(post)) {
                JSONObject jsonObject = JSON.parseObject(post);
                Integer code = jsonObject.getInteger("code");
                String msg = jsonObject.getString("message");
                if (code.equals(200)) {
                    log.debug("--------------prism_wordsInfo----------"+jsonObject.getString("data"));
                    return jsonObject.getString("data");
                }
            }
        } catch (Exception e) {
            //识别失败不抛出异常
            log.error("阿里云通用文字识别失败",e);
        }
        return result;
    }

    public static void recogStrToPayInfo(String payStr, ErpReimPayReceiptDto dto) {
        String amt = "";
        String date = "";
        String receiptType = "";
        boolean getAmt = false;
        boolean getDate = false;
        boolean getReceiptType = false;
        if (StringUtils.isNotEmpty(payStr)) {
            JSONArray infos = JSON.parseArray(payStr);
            for (int i = 0; i < infos.size(); i++) {
                JSONObject info = infos.getJSONObject(i);
                String word = info.getString("word");
                //判断单据类型
                if (!getReceiptType && word.contains(ErpConstants.NATION_PAYMETN_RECEIPT)) {
                    receiptType = ErpConstants.NATION_PAYMETN_RECEIPT;
                    getReceiptType = true;
                }
                if (!getReceiptType && word.contains(ErpConstants.BUSINESS_AUTHORIZATION_LETTER)) {
                    receiptType = ErpConstants.BUSINESS_AUTHORIZATION_LETTER;
                    getReceiptType = true;
                }
                if (!getReceiptType && word.contains(ErpConstants.NATION_PAYMENT_VOUCHER)) {
                    receiptType = ErpConstants.NATION_PAYMENT_VOUCHER;
                    getReceiptType = true;
                }
                if (!getReceiptType && word.contains(ErpConstants.BUSINESS_SETTLEMENT_LETTER)) {
                    receiptType = ErpConstants.BUSINESS_SETTLEMENT_LETTER;
                    getReceiptType = true;
                }
                //是否是金额词语，找到就不再判断
                if (!getAmt && (word.contains("CNY") || word.contains("￥"))) {
                    amt = word;
                    getAmt = true;
                }
                if (!getDate && (word.contains("年"))) {
                    date = word;
                    getDate = true;
                }
            }
        }

        dto.setPayAmtStr(amt);
        dto.setPayDate(date);
        dto.setType(receiptType);
    }
}
