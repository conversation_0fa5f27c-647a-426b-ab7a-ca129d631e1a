-- =================================================================================================
-- 信息科新入库数据导入脚本：mmis_temp_inport_storage_six → mmis_aset_storage & mmis_aset_storage_detail
-- 功能：将信息科新的入库数据按业务逻辑分组导入到正式入库表中
-- 作者：信息科数据迁移
-- 创建时间：2025年6月
-- 数据来源：mmis_temp_inport_storage_six.sql
-- =================================================================================================

-- 开始事务处理，确保数据一致性
BEGIN;

-- =================================================================================================
-- 第一步：数据预处理和清洗 🔍
-- =================================================================================================

-- 首先添加必要的字段到临时表（如果不存在）
ALTER TABLE mmis_temp_inport_storage_six
ADD COLUMN IF NOT EXISTS mat_unique_code varchar(100),
ADD COLUMN IF NOT EXISTS item_num varchar(50),
ADD COLUMN IF NOT EXISTS meter_code varchar(50),
ADD COLUMN IF NOT EXISTS create_time varchar(50),
ADD COLUMN IF NOT EXISTS bill_date date,
ADD COLUMN IF NOT EXISTS hospital_id varchar(50),
ADD COLUMN IF NOT EXISTS amt numeric(20,6),
ADD COLUMN IF NOT EXISTS is_deleted int,
ADD COLUMN IF NOT EXISTS supplier_id int,
ADD COLUMN IF NOT EXISTS modspec varchar(200);

-- 清理空行数据
DELETE FROM mmis_temp_inport_storage_six
WHERE name IS NULL OR TRIM(name) = '';

-- 统计源表记录数
SELECT
    '源表总记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_inport_storage_six;

-- =================================================================================================
-- 第二步：物资信息补充 📦
-- =================================================================================================

-- 根据物资名称匹配物资唯一编码、物品编号、规格型号、参考价格
UPDATE mmis_temp_inport_storage_six t
SET
    mat_unique_code = a.mat_unique_code,
    item_num = a.code,
    modspec = COALESCE(t.modspec, a.modspec),
    meter_code = COALESCE(a.mtr_type, '022')
FROM mmis_aset_info_assist a
WHERE t.name = a.name;

-- 查看物资信息匹配情况
SELECT
    '通过物资名称匹配成功的记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_inport_storage_six
WHERE name IS NOT NULL AND name != '' AND mat_unique_code IS NOT NULL;

SELECT
    '物资名称匹配失败的记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_inport_storage_six
WHERE name IS NOT NULL AND name != '' AND mat_unique_code IS NULL;

-- 显示匹配失败的物资名称（用于排查）
SELECT DISTINCT
    '匹配失败的物资名称' as desc_info,
    name as material_name
FROM mmis_temp_inport_storage_six
WHERE name IS NOT NULL AND name != '' AND mat_unique_code IS NULL
ORDER BY name;

-- =================================================================================================
-- 第三步：日期处理 📅
-- =================================================================================================

-- 根据备注字段解析日期（假设remark包含月份信息）
UPDATE mmis_temp_inport_storage_six t
SET
    create_time = CASE
        WHEN t.remark = '12月' THEN '2024-12-25 12:00:00'
        WHEN t.remark = '1月' THEN '2025-01-01 12:00:00'
        WHEN t.remark = '2月' THEN '2025-02-01 12:00:00'
        WHEN t.remark = '3月' THEN '2025-03-01 12:00:00'
        WHEN t.remark = '4月' THEN '2025-04-01 12:00:00'
        WHEN t.remark = '5月' THEN '2025-05-01 12:00:00'
        ELSE '2025-01-01 12:00:00'
    END,
    bill_date = CASE
        WHEN t.remark = '12月' THEN '2024-12-25'::date
        WHEN t.remark = '1月' THEN '2025-01-01'::date
        WHEN t.remark = '2月' THEN '2025-02-01'::date
        WHEN t.remark = '3月' THEN '2025-03-01'::date
        WHEN t.remark = '4月' THEN '2025-04-01'::date
        WHEN t.remark = '5月' THEN '2025-05-01'::date
        ELSE '2025-01-01'::date
    END;

-- 更新其他固定字段
UPDATE mmis_temp_inport_storage_six
SET
    hospital_id = 'zjxrmyy',
    is_deleted = 0,
    amt = CASE
        WHEN num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND price IS NOT NULL AND price ~ '^[0-9]+\.?[0-9]*$'
        THEN CAST(num AS numeric) * CAST(price AS numeric)
        ELSE 0
    END;

-- =================================================================================================
-- 第四步：数据验证统计 📊
-- =================================================================================================

-- 统计有效记录数量
SELECT
    '有效记录数（基本信息完整）' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_inport_storage_six
WHERE supplier_name IS NOT NULL AND supplier_name != ''
  AND name IS NOT NULL AND name != ''
  AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0;

-- 预览分组统计：查看将要生成多少个入库单
SELECT
    '预计生成的入库单数量' as desc_info,
    COUNT(*) as count_val
FROM (
    SELECT DISTINCT
        supplier_name,
        bill_date,
        hospital_id,
        remark
    FROM mmis_temp_inport_storage_six
    WHERE supplier_name IS NOT NULL AND supplier_name != ''
      AND name IS NOT NULL AND name != ''
      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
) grouped_preview;

-- =================================================================================================
-- 第五步：生成单据号序列 📋
-- =================================================================================================

-- 创建临时序列表，用于生成入库单据号
CREATE TEMP TABLE temp_storage_doc_sequence AS
WITH source_groups AS (
    SELECT DISTINCT
        supplier_name,
        bill_date,
        hospital_id,
        remark
    FROM mmis_temp_inport_storage_six
    WHERE supplier_name IS NOT NULL AND supplier_name != ''
      AND name IS NOT NULL AND name != ''
      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
),
grouped_data_with_min_create_time AS (
    SELECT
        sg.supplier_name,
        sg.bill_date,
        sg.hospital_id,
        sg.remark,
        MIN(ti.create_time) as master_create_time
    FROM source_groups sg
    JOIN mmis_temp_inport_storage_six ti ON
        sg.supplier_name = ti.supplier_name AND
        sg.bill_date = ti.bill_date AND
        sg.hospital_id = ti.hospital_id AND
        sg.remark = ti.remark
    WHERE ti.supplier_name IS NOT NULL AND ti.supplier_name != ''
      AND ti.name IS NOT NULL AND ti.name != ''
      AND ti.num IS NOT NULL AND ti.num ~ '^[0-9]+\.?[0-9]*$' AND CAST(ti.num AS numeric) > 0
    GROUP BY sg.supplier_name, sg.bill_date, sg.hospital_id, sg.remark
),
numbered_storages AS (
  SELECT
      gds.*,
      ROW_NUMBER() OVER (ORDER BY gds.bill_date, gds.supplier_name, gds.remark) as seq_num
  FROM grouped_data_with_min_create_time gds
)
SELECT
    nr.*,
    'RKD' || TO_CHAR(nr.bill_date, 'YYYYMMDD') || LPAD(nr.seq_num::text, 3, '0') as docment_num
FROM numbered_storages nr;

-- 验证单据号唯一性
SELECT 
    '生成的单据号数量' as desc_info,
    COUNT(*) as count_val
FROM temp_storage_doc_sequence;

-- =================================================================================================
-- 第六步：插入入库主表 (mmis_aset_storage) 🏗️
-- =================================================================================================

INSERT INTO mmis_aset_storage (
    bill_date,            -- 开单日期
    supplier_name,        -- 供应商
    docment_num,          -- 单据号
    manual_doc_num,       -- 手工单号
    remark,               -- 备注
    appy_org_id,          -- 业务部门
    appyer,               -- 业务员
    wrhs_code,            -- 仓库代码
    type_code,            -- 业务类别
    is_deleted,           -- 逻辑删除标志
    hospital_id,          -- 医院ID
    chk_state,            -- 审核状态
    crter,                -- 创建人
    create_time,          -- 创建时间
    in_status,            -- 入库状态
    in_emp,               -- 入库操作人
    in_time,              -- 入库时间
    in_org_id             -- 入库执行人部门
)
SELECT 
    tds.bill_date::varchar,                                -- 开单日期
    tds.supplier_name,                                     -- 供应商
    tds.docment_num,                                       -- 单据号
    tds.docment_num,                                       -- 手工单号（同单据号）
    '信息科历史数据导入批次：' || tds.remark,              -- 备注
    '521001',                                              -- 业务部门（信息科）
    'SYS_IMPORT',                                          -- 业务员（系统导入）
    '0003',                                                -- 仓库代码（信息科仓库）
    'R1',                                                  -- 业务类别（采购入库）
    0,                                                     -- 逻辑删除标志
    tds.hospital_id,                                       -- 医院ID
    '1',                                                   -- 审核状态（已审核）
    'SYS_IMPORT',                                          -- 创建人
    tds.master_create_time,                                -- 创建时间
    '1',                                                   -- 入库状态（已入库）
    'SYS_IMPORT',                                          -- 入库操作人
    tds.master_create_time,                                -- 入库时间
    '521001'                                               -- 入库执行人部门
FROM temp_storage_doc_sequence tds;

-- 验证主表插入结果
SELECT 
    '主表插入记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_aset_storage
WHERE crter = 'SYS_IMPORT' AND type_code = 'R1';

-- =================================================================================================
-- 第七步：插入入库明细表 (mmis_aset_storage_detail) 📦
-- =================================================================================================

INSERT INTO mmis_aset_storage_detail (
    apply_id,             -- 申请ID（关联主表）
    item_num,             -- 货号/物品编号
    name,                 -- 品名
    modspec,              -- 规格型号
    wrhs_addr,            -- 库位代码
    meter_code,           -- 计量方式
    price,                -- 单价
    amt,                  -- 金额
    num,                  -- 数量
    remark,               -- 物资备注
    mat_unique_code,      -- 物资唯一编码
    crter,                -- 创建人
    crter_time,           -- 创建时间
    hospital_id,          -- 组织ID
    is_deleted,           -- 逻辑删除标志
    freight_cost,         -- 运输成本
    aset_code,            -- 物资编码
    item_count            -- 每件细数
)
SELECT 
    sa.id,                                                                    -- 关联主表ID
    COALESCE(temp.item_num, 'UNKNOWN'),                                       -- 物品编号
    temp.name,                                                                -- 品名
    temp.modspec,                                                             -- 规格型号
    '0003',                                                                   -- 库位代码（默认库位）
    COALESCE(temp.meter_code, '个'),                                          -- 计量方式（默认'个'）
    CASE 
        WHEN temp.price IS NOT NULL AND temp.price ~ '^[0-9]+\.?[0-9]*$' 
        THEN CAST(temp.price AS numeric(20,6))
        ELSE 0 
    END,                                                                      -- 单价
    temp.amt,                                                                 -- 金额
    CASE 
        WHEN temp.num IS NOT NULL AND temp.num ~ '^[0-9]+\.?[0-9]*$' 
        THEN CAST(temp.num AS numeric(20,6))
        ELSE 0 
    END,                                                                      -- 数量
    '导入批次：' || COALESCE(temp.remark, ''),                                -- 物资备注
    temp.mat_unique_code,                                                     -- 物资唯一编码
    'SYS_IMPORT',                                                             -- 创建人
    temp.create_time,                                                         -- 创建时间
    temp.hospital_id,                                                         -- 组织ID
    0,                                                                        -- 逻辑删除标志
    0.00,                                                                     -- 运输成本（默认0）
    COALESCE(temp.item_num, 'UNKNOWN'),                                       -- 物资编码
    1                                                                         -- 每件细数（默认1）
FROM mmis_temp_inport_storage_six temp
INNER JOIN temp_storage_doc_sequence tds_join ON 
    tds_join.supplier_name = temp.supplier_name AND
    tds_join.bill_date = temp.bill_date AND
    tds_join.hospital_id = temp.hospital_id AND
    tds_join.remark = temp.remark
INNER JOIN mmis_aset_storage sa ON sa.docment_num = tds_join.docment_num 
    AND sa.crter = 'SYS_IMPORT' AND sa.type_code = 'R1'
WHERE temp.supplier_name IS NOT NULL AND temp.supplier_name != ''
  AND temp.name IS NOT NULL AND temp.name != ''
  AND temp.num IS NOT NULL AND temp.num ~ '^[0-9]+\.?[0-9]*$' AND CAST(temp.num AS numeric) > 0;

-- 验证明细表插入结果
SELECT 
    '明细表插入记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_aset_storage_detail
WHERE crter = 'SYS_IMPORT';

-- =================================================================================================
-- 第八步：数据完整性验证 ✅
-- =================================================================================================

-- 验证主表与明细表关联完整性
SELECT 
    '主表记录数 (本次导入)' as desc_info,
    COUNT(*) as count_val
FROM mmis_aset_storage
WHERE crter = 'SYS_IMPORT' AND type_code = 'R1';

SELECT 
    '明细表记录数 (本次导入)' as desc_info,
    COUNT(*) as count_val
FROM mmis_aset_storage_detail
WHERE crter = 'SYS_IMPORT';

-- 检查是否有明细记录缺失主表关联
SELECT 
    '孤立明细记录数（无主表关联, 本次导入）' as desc_info,
    COUNT(*) as count_val
FROM mmis_aset_storage_detail d
WHERE d.crter = 'SYS_IMPORT'
  AND NOT EXISTS (
      SELECT 1 FROM mmis_aset_storage a 
      WHERE a.id = d.apply_id AND a.crter = 'SYS_IMPORT' AND a.type_code = 'R1'
  );

-- 检查是否有主表记录缺失明细
SELECT 
    '空入库单数（无明细记录, 本次导入）' as desc_info,
    COUNT(*) as count_val
FROM mmis_aset_storage a
WHERE a.crter = 'SYS_IMPORT' AND a.type_code = 'R1'
  AND NOT EXISTS (
      SELECT 1 FROM mmis_aset_storage_detail d 
      WHERE d.apply_id = a.id AND d.crter = 'SYS_IMPORT'
  );

-- 金额汇总验证
SELECT 
    '明细表总金额 (本次导入)' as desc_info,
    ROUND(SUM(d.amt), 2) as total_amount
FROM mmis_aset_storage_detail d
INNER JOIN mmis_aset_storage a ON a.id = d.apply_id
WHERE a.crter = 'SYS_IMPORT' AND d.crter = 'SYS_IMPORT' AND a.type_code = 'R1';

-- =================================================================================================
-- 第九步：抽样验证结果 🔍
-- =================================================================================================

-- 显示导入成功的入库单示例
SELECT 
    '导入成功的入库单示例' as desc_info,
    a.id,
    a.docment_num,
    a.supplier_name,
    a.bill_date,
    COUNT(d.id) as detail_count,
    ROUND(SUM(d.amt), 2) as total_amount
FROM mmis_aset_storage a
LEFT JOIN mmis_aset_storage_detail d ON a.id = d.apply_id AND d.crter = 'SYS_IMPORT'
WHERE a.crter = 'SYS_IMPORT' AND a.type_code = 'R1'
GROUP BY a.id, a.docment_num, a.supplier_name, a.bill_date
ORDER BY a.id
LIMIT 10;

-- 显示明细记录示例
SELECT 
    '明细记录示例' as desc_info,
    d.apply_id,
    d.item_num,
    d.name,
    d.num,
    d.price,
    d.amt,
    d.meter_code,
    a.docment_num
FROM mmis_aset_storage_detail d
INNER JOIN mmis_aset_storage a ON a.id = d.apply_id
WHERE d.crter = 'SYS_IMPORT' AND a.crter = 'SYS_IMPORT' AND a.type_code = 'R1'
ORDER BY d.apply_id, d.id
LIMIT 10;

-- =================================================================================================
-- 第十步：清理和完成 🧹
-- =================================================================================================

-- 删除临时序列表
DROP TABLE IF EXISTS temp_storage_doc_sequence;

-- 显示最终统计
SELECT 
    '=== 入库数据导入完成统计 ===' as desc_info,
    '' as value;

SELECT 
    '成功导入入库单数量' as desc_info,
    COUNT(*)::text as value
FROM mmis_aset_storage
WHERE crter = 'SYS_IMPORT' AND type_code = 'R1';

SELECT 
    '成功导入入库明细数量' as desc_info,
    COUNT(*)::text as value
FROM mmis_aset_storage_detail
WHERE crter = 'SYS_IMPORT';

SELECT 
    '总入库金额 (基于明细)' as desc_info,
    ROUND(SUM(d.amt), 2)::text as value
FROM mmis_aset_storage_detail d
INNER JOIN mmis_aset_storage a ON a.id = d.apply_id
WHERE a.crter = 'SYS_IMPORT' AND d.crter = 'SYS_IMPORT' AND a.type_code = 'R1';

-- 提交事务
COMMIT;

-- =================================================================================================
-- 脚本执行完成 ✅
-- 说明：
-- 1. 本脚本通过物资名称匹配mmis_aset_info_assist获取物资信息，避免硬编码
-- 2. 按照业务逻辑(供应商、日期、备注)进行了分组，生成了标准的入库单据
-- 3. 所有导入记录的crter字段标记为'SYS_IMPORT'，便于识别和管理
-- 4. 已完成数据完整性验证，确保主表与明细表正确关联
-- 5. 如需回滚，可通过crter='SYS_IMPORT' AND type_code='R1' 条件删除所有导入记录
-- ================================================================================================= 