package com.jp.med.ams.modules.amsPropertyInAndOut.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInStockConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInStockConfigVo;

import java.util.List;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
public interface AmsInStockConfigReadService extends IService<AmsInStockConfigDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsInStockConfigVo> queryList(AmsInStockConfigDto dto);
}

