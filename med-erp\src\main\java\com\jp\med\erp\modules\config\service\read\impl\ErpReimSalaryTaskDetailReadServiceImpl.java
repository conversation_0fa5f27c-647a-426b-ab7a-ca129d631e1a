package com.jp.med.erp.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.erp.modules.config.mapper.read.ErpReimSalaryTaskDetailReadMapper;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskDetailVo;
import com.jp.med.erp.modules.config.service.read.ErpReimSalaryTaskDetailReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class ErpReimSalaryTaskDetailReadServiceImpl extends ServiceImpl<ErpReimSalaryTaskDetailReadMapper, ErpReimSalaryTaskDetailDto> implements ErpReimSalaryTaskDetailReadService {

    @Autowired
    private ErpReimSalaryTaskDetailReadMapper erpReimSalaryTaskDetailReadMapper;

    @Override
    public List<ErpReimSalaryTaskDetailVo> queryList(ErpReimSalaryTaskDetailDto dto) {
        return erpReimSalaryTaskDetailReadMapper.queryList(dto);
    }

    @Override
    public List<ErpReimSalaryTaskDetailVo> queryPageList(ErpReimSalaryTaskDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpReimSalaryTaskDetailReadMapper.queryList(dto);
    }

}
