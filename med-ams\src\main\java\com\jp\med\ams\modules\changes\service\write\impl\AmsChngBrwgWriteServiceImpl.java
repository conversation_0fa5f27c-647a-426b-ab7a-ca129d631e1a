package com.jp.med.ams.modules.changes.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.feign.AmsMessageFeignService;
import com.jp.med.ams.modules.changes.mapper.read.AmsChngBrwgReadMapper;
import com.jp.med.ams.modules.changes.mapper.write.AmsChngBrwgWriteMapper;
import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import com.jp.med.ams.modules.changes.service.write.AmsChngBrwgWriteService;
import com.jp.med.ams.modules.changes.strategy.borrowing.AmsBorrowingAuditStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.context.AmsBorrowingAuditContext;
import com.jp.med.ams.modules.changes.strategy.borrowing.factory.AmsBorrowingAuditStrategyFactory;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.ULIDUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 资产借用信息表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 16:01:14
 */
@Slf4j
@Service
@Transactional(readOnly = false)
public class AmsChngBrwgWriteServiceImpl extends ServiceImpl<AmsChngBrwgWriteMapper, AmsChngBrwgDto>
        implements AmsChngBrwgWriteService {

    @Resource
    private AmsChngBrwgWriteMapper amsChngBrwgWriteMapper;

    @Resource
    private AmsChngBrwgReadMapper amsChngBrwgReadMapper;

    @Resource
    private AmsPropertyWriteMapper amsPropertyWriteMapper;

    @Autowired
    public AmsMessageFeignService amsMessageFeignService;

    @Autowired
    private AmsBorrowingNotificationService notificationService;
    @Autowired
    private AmsBorrowingAuditStrategyFactory auditStrategyFactory;

    /**
     * 根据ID更新资产借用信息，并同步更新关联资产的状态。
     * <p>
     * 主要逻辑：
     * 1. 如果传入的 `dto` 中 `id` 为空，但提供了 `faCode`，则尝试根据 `faCode` 和当前借用状态（prosstas='4',
     * busstas='1'）查找对应的借用单据ID。
     * 2. 如果找不到唯一的借用单据，则抛出 AppException。
     * 3. 使用获取到的或传入的 `id` 更新 `ams_chng_brwg` 表中的记录。
     * 4. 如果更新失败（影响行数不为1），则抛出 AppException。
     * 5. 根据更新后的 `id` 重新查询借用信息。如果未查询到，抛出 AppException。
     * 6. 判断更新后的借用状态：
     * - 如果是审核通过且业务状态为借用中 (prosstas='4', busstas='1')，则更新 `ams_property` 表中对应资产的状态为
     * "借用" (assetStatus='2')。
     * - 如果是归还完成 (prosstas='5', busstas='3')，则更新 `ams_property` 表中对应资产的状态为 "在库"
     * (assetStatus='1')。
     * 7. 返回 `true` 表示操作成功（如果中间未抛出异常）。
     *
     * @param dto 包含更新信息的资产借用 DTO。必须包含 `id` 或 `faCode`。其他字段如 `prosstas`, `busstas`
     *            用于更新状态。
     * @return 如果更新成功（未抛出异常）则返回 `true`。
     * @throws AppException 如果查找借用单据失败、更新借用记录失败或更新后查询不到信息。
     */
    @Override
    public boolean updateById(AmsChngBrwgDto dto) {
        // 当操作时归还时通过设备编码和当前借用状态查询借用的单据
        if (Objects.isNull(dto.getId())) {
            AmsChngBrwgDto amsChngBrwgDto = new AmsChngBrwgDto();
            amsChngBrwgDto.setFaCode(dto.getFaCode());
            amsChngBrwgDto.setProsstas(MedConst.TYPE_4);
            amsChngBrwgDto.setBusstas(MedConst.TYPE_1);
            List<AmsChngBrwgVo> brwgVos = amsChngBrwgReadMapper.queryList(amsChngBrwgDto);
            if (brwgVos.size() != 1) {
                throw new AppException("查询借用单据失败");
            }
            dto.setId(brwgVos.get(0).getId());
        }
        int updateById = amsChngBrwgWriteMapper.updateById(dto);
        if (updateById != 1) {
            throw new AppException("操作失败");
        }
        List<AmsChngBrwgVo> amsChngBrwgVos = amsChngBrwgReadMapper.queryList(dto);
        if (amsChngBrwgVos.isEmpty()) {
            throw new AppException("未查询到信息");
        }
        AmsPropertyDto propertyDto = new AmsPropertyDto();
        AmsChngBrwgVo borrowingRecord = amsChngBrwgVos.get(0);

        // 如果当前资产为借用状态且审核通过，修改资产状态为借用
        if (MedConst.TYPE_4.equals(borrowingRecord.getProsstas())
                && MedConst.TYPE_1.equals(borrowingRecord.getBusstas())) {
            propertyDto.setFaCode(borrowingRecord.getFaCode());
            propertyDto.setAssetStatus(MedConst.ASSET_STATUS_2);
            amsPropertyWriteMapper.updateByFaCode(propertyDto);

            // 发送借用成功通知
            try {
                notificationService.sendBorrowingSuccessNotification(borrowingRecord);
            } catch (Exception e) {
                log.warn("发送借用成功通知失败", e);
            }
        }

        // 如果是归还完成，修改资产状态为在库
        if (MedConst.TYPE_5.equals(borrowingRecord.getProsstas())
                && MedConst.TYPE_3.equals(borrowingRecord.getBusstas())) {
            propertyDto.setFaCode(borrowingRecord.getFaCode());
            propertyDto.setAssetStatus(MedConst.ASSET_STATUS_1);
            amsPropertyWriteMapper.updateByFaCode(propertyDto);

            // 发送归还确认通知
            try {
                notificationService.sendReturnConfirmationNotification(borrowingRecord);
            } catch (Exception e) {
                log.warn("发送归还确认通知失败", e);
            }
        }
        return true;
    }

    /**
     * 批量或单个更新资产借用状态，并根据情况同步更新资产状态或发送消息。
     * <p>
     * 模式一：批量更新 (当 `dto.ids` 非空时)
     * 1. 根据传入的 `ids` 查询对应的借用记录。如果查询结果数量与 `ids` 数量不匹配，抛出 AppException。
     * 2. 遍历 `ids`，为每个 `id` 构建一个更新 DTO (`AmsChngBrwgDto`) 和一个资产更新 DTO
     * (`AmsPropertyDto`)。
     * 3. 根据 `dto.updateType` 判断更新类型：
     * - updateType = '1' (审批/确认类操作):
     * - 设置审核备注 (`chkRemarks`) 和审核时间 (`chkTime`)。
     * - 如果 `prosstas` = '5' (归还确认完成):
     * - 设置业务状态 (`busstas`) 为 '3' (已归还)。
     * - 设置实际归还时间 (`actRtnTime`)。
     * - 准备将对应资产状态更新为 '1' (在库)。
     * - 否则 (其他审批状态，如借用审核通过 '4'):
     * - 如果 `prosstas` = '4' (借用审核通过)，准备将对应资产状态更新为 '2' (借用)。
     * - updateType != '1' (驳回/退回类操作):
     * - 设置退回备注 (`backRemarks`) 和退回时间 (`backTime`)。
     * - 设置业务状态 (`busstas`) 为 '2' (已驳回/退回)。
     * 4. 设置每个借用更新 DTO 的最终流程状态 (`prosstas`) 和 `id`。
     * 5. 批量更新资产状态 (`ams_property`)。
     * 6. 批量更新借用记录 (`ams_chng_brwg`)。
     * <p>
     * 模式二：单个更新 (当 `dto.ids` 为空时)
     * 1. 如果 `dto.updateType` = '3' (续期申请):
     * - 构建系统消息 (`SysMessageDto`)。
     * - 设置接收人为借用人 (`loanee`)。
     * - 设置消息标题和内容。
     * - 调用 `amsMessageFeignService.sendMessage` 发送消息。
     * 2. 直接根据传入的 `dto` (包含 `id` 和要更新的字段) 更新单个借用记录。
     *
     * @param dto 包含更新所需信息的 DTO。
     *            - `ids`: (可选) 需要批量更新的借用记录 ID 列表。
     *            - `updateType`: 更新类型 ('1': 审批/确认, 其他: 驳回/退回, '3': 续期申请)。
     *            - `prosstas`: 目标流程状态。
     *            - `chkRemarks`/`backRemarks`: 审核/退回备注。
     *            - `loanee`: (用于续期申请消息) 借用人。
     *            - `sysUser`: (用于续期申请消息) 当前操作用户信息。
     *            - 其他相关字段。
     * @throws AppException 如果批量查询时数量不匹配。
     */
    @Override
    public void updateStatus(AmsChngBrwgDto dto) {
        log.info("🔄 开始执行资产借用状态更新，更新类型：{}，目标状态：{}", dto.getUpdateType(), dto.getProsstas());

        try {
            // 1. 构建审核上下文
            AmsBorrowingAuditContext auditContext = buildAuditContext(dto);

            // 2. 获取对应的审核策略
            AmsBorrowingAuditStrategy strategy = auditStrategyFactory.getStrategy(dto.getUpdateType(), dto.getProsstas());
            if (strategy == null) {
                throw new AppException("未找到匹配的审核策略，updateType：" + dto.getUpdateType() + "，prosstas：" + dto.getProsstas());
            }

            // 3. 验证审核参数
            if (!strategy.validateAuditParams(auditContext, dto)) {
                throw new AppException("审核参数验证失败");
            }

            // 4. 执行审核策略
            boolean success = strategy.executeAudit(auditContext, dto);
            if (!success) {
                throw new AppException("审核策略执行失败");
            }

            log.info("✅ 资产借用状态更新完成，策略类型：{}", strategy.getStrategyType());

        } catch (Exception e) {
            log.error("❌ 资产借用状态更新失败", e);
            throw new AppException("更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 构建审核上下文
     */
    private AmsBorrowingAuditContext buildAuditContext(AmsChngBrwgDto dto) {
        AmsBorrowingAuditContext context = new AmsBorrowingAuditContext();

        // 设置审核人信息
        if (dto.getSysUser() != null) {
            context.setAuditor(dto.getSysUser());
        }

        // 设置审核类型和目标状态
        context.setAuditType(dto.getUpdateType());
        context.setTargetProcessStatus(dto.getProsstas());
        context.setAuditTime(DateUtil.getCurrentTime(null));

        // 判断是否为批量操作
        boolean isBatchOperation = CollectionUtil.isNotEmpty(dto.getIds());
        context.setBatchOperation(isBatchOperation);

        if (isBatchOperation) {
            // 批量操作：查询借用记录
            dto.setSqlAutowiredHospitalCondition(true);
            List<AmsChngBrwgVo> borrowingRecords = amsChngBrwgReadMapper.queryById(dto);
            if (borrowingRecords.size() != dto.getIds().size()) {
                throw new AppException("查询单据失败，期望数量：" + dto.getIds().size() + "，实际数量：" + borrowingRecords.size());
            }

            context.setBorrowingRecords(borrowingRecords);

            // 构建ID到记录的映射
            Map<Integer, AmsChngBrwgVo> recordMap = borrowingRecords.stream()
                    .collect(Collectors.toMap(AmsChngBrwgVo::getId, record -> record, (o1, o2) -> o2));
            context.setBorrowingRecordMap(recordMap);
        } else {
            // 单个操作：如果需要查询记录信息，可以在这里添加
            if (dto.getId() != null) {
                AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
                queryDto.setId(dto.getId());
                List<AmsChngBrwgVo> records = amsChngBrwgReadMapper.queryList(queryDto);
                context.setBorrowingRecords(records);
            }
        }

        return context;
    }

    /**
     * 保存新的资产借用申请。
     * <p>
     * 1. 生成唯一的文档编号 (`docNum`)。
     * 2. 设置记录为激活状态 (`activeFlag`='1')。
     * 3. 设置初始流程状态为 "待审批" (`prosstas`='1')。
     * 4. 设置初始业务状态为 "借用中" (`busstas`='1')。
     * 5. 设置创建人 (`crter`) 和创建人部门 (`crterDept`)。
     * 6. 设置创建时间 (`createTime`)。
     * 7. 调用 `super.save()` 将记录插入数据库。
     * 8. 构建系统消息 (`SysMessageDto`)。
     * 9. 设置接收人为借用人 (`loanee`)。
     * 10. 设置消息标题和推送内容。
     * 11. 调用 `amsMessageFeignService.sendMessage` 发送通知消息给借用人。
     * 12. 返回 `true` 表示保存和消息发送已尝试执行。
     *
     * @param dto 包含新借用申请信息的 DTO，至少应包含 `faCode`, `loanee`, `sysUser` 等。
     * @return 总是返回 `true`。
     */
    @Override
    public boolean save(AmsChngBrwgDto dto) {
        dto.setDocNum(ULIDUtil.generate());
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setProsstas(MedConst.TYPE_1);
        dto.setBusstas(MedConst.TYPE_1);
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setCrterDept(dto.getSysUser().getHrmUser().getHrmOrgId());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        super.save(dto);

        // 发送申请提交通知（使用新的通知服务）
        try {
            // 查询刚保存的记录以获取完整信息
            AmsChngBrwgDto queryDto = new AmsChngBrwgDto();
            queryDto.setId(dto.getId());
            List<AmsChngBrwgVo> savedRecords = amsChngBrwgReadMapper.queryList(queryDto);

            if (!savedRecords.isEmpty()) {
                AmsChngBrwgVo borrowingRecord = savedRecords.get(0);
                notificationService.sendApplicationSubmittedNotification(borrowingRecord);
            }
        } catch (Exception e) {
            log.warn("发送申请提交通知失败", e);
        }

        // 保留原有的消息发送逻辑作为备用
        SysMessageDto sysMessageDto = new SysMessageDto();
        List<String> ids = new ArrayList<>();
        ids.add(dto.getLoanee());
        String[] preInitializedArray = new String[ids.size()];
        sysMessageDto.setUsers(ids.toArray(preInitializedArray));
        sysMessageDto.setTitle("资产借用申请");
        sysMessageDto.setPushText(dto.getSysUser().getHrmUser().getHrmOrgName() + "申请借用资产，请处理");
        FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(sysMessageDto));
        return true;
    }
}
