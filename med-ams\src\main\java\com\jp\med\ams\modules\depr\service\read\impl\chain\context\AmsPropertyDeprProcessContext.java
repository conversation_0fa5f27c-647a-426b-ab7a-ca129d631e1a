package com.jp.med.ams.modules.depr.service.read.impl.chain.context;

import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import com.jp.med.ams.modules.config.vo.AmsTypenCfgVo;
import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.ams.modules.property.vo.AmsSourceAmountSplitVo;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 资产折旧处理上下文
 * 用于在责任链处理器之间传递数据
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
public class AmsPropertyDeprProcessContext {

    // ========== 输入参数 ==========
    /** 查询条件DTO */
    private AmsPropertyDeprDto queryDto;

    // ========== 基础配置数据 ==========
    /** 期号 格式yyyyMM */
    private String ym;

    /** 是否使用新资产类型 */
    private boolean useNewType;

    /** 月份最后一天 */
    private LocalDate lastDayOfMonth;

    /** 资金来源配置列表 */
    private List<AmsBasicCfgVo> sourceList;

    /** 资金来源代码映射 (code -> name) */
    private Map<String, String> sourceCodeMap;

    /** 财政补助资金代码 */
    private String finaSubsidyCode;

    /** 资产新分类配置列表 */
    private List<AmsTypenCfgVo> amsTypenCfgVos;

    // ========== 资产数据 ==========
    /** 房屋维修资产列表 */
    private List<AmsPropertyVo> houseRepairAssets;

    /** 普通资产列表 */
    private List<AmsPropertyVo> normalAssets;

    /** 混合资金来源拆分数据 */
    private List<AmsSourceAmountSplitVo> amsSourceAmountSplitVos;

    // ========== 分组数据 ==========
    /** 普通资产分组数据 (科室 -> 资产类型 -> 资金来源 -> 资产列表) */
    private Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedNormalAssets;

    /** 房屋维修资产分组数据 (科室 -> 资产类型 -> 资金来源 -> 资产列表) */
    private Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedHouseRepairAssets;

    // ========== 处理结果 ==========
    /** 处理结果列表 */
    private List<AmsPropertyDepr2Vo> result;

    /** 已使用的资产代码集合 */
    private Set<String> usedFacodeSet;

    /** 科室代码集合 */
    private Set<String> deptCodes;

    /** 科室折旧比例映射 (科室代码 -> 折旧比例) */
    private Map<String, BigDecimal> deprRateMap;

    // ========== 构造方法 ==========
    public AmsPropertyDeprProcessContext(AmsPropertyDeprDto queryDto) {
        this.queryDto = queryDto;
        this.ym = queryDto.getYm();
        this.useNewType = queryDto.getTypen();
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否需要计算大类折旧
     */
    public boolean shouldCalcBigCategoryDepr() {
        return useNewType && queryDto.getCalcBigCategoryDepr();
    }

    /**
     * 检查是否需要计算财政补助
     */
    public boolean shouldCalcFinaSubsidy() {
        return queryDto.getCalcFinaSubsidy();
    }

    /**
     * 获取资产类型代码（根据是否使用新类型）
     */
    public String getAssetTypeCode() {
        return queryDto.getAssetTypeCode();
    }

    /**
     * 获取科室代码
     */
    public String getDeptCode() {
        return queryDto.getDeptCode();
    }
}
