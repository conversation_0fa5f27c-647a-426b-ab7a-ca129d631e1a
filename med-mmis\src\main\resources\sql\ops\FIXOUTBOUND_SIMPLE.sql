-- =================================================================================================
-- 出库数据组织字段修复脚本（简化版）
-- 功能：将out_target_org_id的数据移到out_target_org，然后重新查询正确的组织ID
-- 需求：优先选择叶子节点的组织ID
-- =================================================================================================

BEGIN;

-- 第一步：数据移动 - 将out_target_org_id的数据移到out_org
UPDATE mmis_temp_xinxike_outbound_six
SET out_org = COALESCE(out_org, out_target_org_id)
WHERE out_target_org_id IS NOT NULL;

-- 第二步：清空out_target_org_id，准备重新填充
UPDATE mmis_temp_xinxike_outbound_six
SET out_target_org_id = NULL;

-- 第三步：通过组织名称查询组织ID，优先选择叶子节点
UPDATE mmis_temp_xinxike_outbound_six t
SET out_target_org_id = (
    WITH leaf_orgs AS (
        SELECT
            o.org_id,
            o.org_name,
            CASE
                WHEN NOT EXISTS (
                    SELECT 1 FROM hrm_org child
                    WHERE child.parent_org_id = o.org_id
                      AND child.hospital_id = 'zjxrmyy'
                      AND child.active_flag = '1'
                ) THEN 1
                ELSE 0
            END as is_leaf
        FROM hrm_org o
        WHERE o.hospital_id = 'zjxrmyy'
          AND o.active_flag = '1'
          AND o.org_name = t.out_org
    )
    SELECT org_id
    FROM leaf_orgs
    ORDER BY is_leaf DESC, org_id
    LIMIT 1
)
WHERE t.out_org IS NOT NULL AND TRIM(t.out_org) != '';

-- 第四步：对于仍然没有匹配的，设置默认值
UPDATE mmis_temp_xinxike_outbound_six
SET out_target_org_id = '521001'  -- 信息科
WHERE out_target_org_id IS NULL
  AND out_org IS NOT NULL
  AND TRIM(out_org) != '';

-- 验证修复结果
SELECT
    '修复结果' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) as has_org_id,
    ROUND(COUNT(CASE WHEN out_target_org_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL;

-- 显示映射结果
SELECT
    out_org,
    out_target_org_id,
    o.org_name as verified_name,
    COUNT(*) as record_count
FROM mmis_temp_xinxike_outbound_six t
LEFT JOIN hrm_org o ON t.out_target_org_id = o.org_id
WHERE t.out_org IS NOT NULL
GROUP BY out_org, out_target_org_id, o.org_name
ORDER BY record_count DESC;

-- 提交事务
COMMIT;
