package com.jp.med.ams.modules.it.controller;

import com.jp.med.ams.modules.it.mapper.read.AmsItInvtOrgConsumableReadMapper;
import com.jp.med.ams.modules.it.service.read.AmsItInvtCfgReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItInvtOrgConsumableDto;
import com.jp.med.ams.modules.it.service.read.AmsItInvtOrgConsumableReadService;
import com.jp.med.ams.modules.it.service.write.AmsItInvtOrgConsumableWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 科室耗材配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 15:04:47
 */
@Api(value = "科室耗材配置", tags = "科室耗材配置")
@RestController
@RequestMapping("AmsItInvtOrgConsumable")
public class AmsItInvtOrgConsumableController {

    @Autowired
    private AmsItInvtOrgConsumableReadService AmsItInvtOrgConsumableReadService;

    @Autowired
    private AmsItInvtOrgConsumableWriteService AmsItInvtOrgConsumableWriteService;

    @Autowired
    private AmsItInvtCfgReadService amsItInvtCfgReadService;

    /**
     * 列表
     */
    @ApiOperation("查询科室耗材配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItInvtOrgConsumableDto dto){
        return CommonResult.paging(AmsItInvtOrgConsumableReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询科室耗材配置")
    @PostMapping("/orgList")
    public CommonResult<?> orgList(@RequestBody AmsItInvtOrgConsumableDto dto){
        return CommonResult.success(AmsItInvtOrgConsumableReadService.SearchOrgList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增科室耗材配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsItInvtOrgConsumableDto dto){
        AmsItInvtOrgConsumableWriteService.saveDto(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改科室耗材配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItInvtOrgConsumableDto dto){

        AmsItInvtOrgConsumableWriteService.updateById(dto);

        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除科室耗材配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItInvtOrgConsumableDto dto){
        AmsItInvtOrgConsumableWriteService.removeById(dto);
        return CommonResult.success();
    }


    /**
     * 查询设备类型名
     * */
    @ApiOperation("查询设备类型名")
    @PostMapping("/searchEquType")
    public CommonResult<?> searchEquType(){
        return CommonResult.success(AmsItInvtOrgConsumableReadService.findEquType());
    }

    /**
     * 查询设备名称
     * */
    @ApiOperation("查询设备类型名")
    @PostMapping("/searchEquName")
    public CommonResult<?> searchEquName(){
        return CommonResult.success(AmsItInvtOrgConsumableReadService.findEquName());
    }
    /**
     * 查询耗材名称
     * */
    @ApiOperation("查询设备类型名")
    @PostMapping("/searchConsumName")
    public CommonResult<?> searchConsumName(){
        return CommonResult.success(AmsItInvtOrgConsumableReadService.findConsumName());
    }

    /**
     * 按科室查找
     *
     */
    @ApiOperation("查询设备类型名")
    @PostMapping("/searchByOrgId")
    public CommonResult<?> searchByOrgId(@RequestBody AmsItInvtOrgConsumableDto dto){

        return CommonResult.success(AmsItInvtOrgConsumableReadService.findcfgByOrgId(dto));
    }

}
