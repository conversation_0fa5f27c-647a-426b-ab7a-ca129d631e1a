package com.jp.med.ams.modules.depr.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.SneakyThrows;

import java.io.IOException;
import java.util.List;

/**
 * 资产折旧月度数据表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Data
@TableName("ams_depr_month_data")
public class AmsDeprMonthDataDto extends CommonQueryDto {

    /**
     * ID
     */
    @TableId("id")
    private Integer id;

    private String date;


    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<AmsPropertyDepr2Vo> data;


    public static class AmsPropertyDepr2VoDataListTypeHandler extends JacksonTypeHandler {

        public AmsPropertyDepr2VoDataListTypeHandler(Class<?> type) {
            super(type);
        }

        @Override
        @SneakyThrows
        public Object parse(String json) {
            try {
                return getObjectMapper().readValue(json, new TypeReference<List<AmsPropertyDepr2Vo>>() {
                });
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
}

