package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary2;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Summary2 最终汇总处理器
 * 负责对 Summary2 数据进行最终的汇总和格式化
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertySummary2FinalProcessor
        extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> {

    @Override
    protected void doProcess(AmsPropertyDeprSummary2Context context) {
        log.debug("开始进行 Summary2 最终汇总");

        // 最终汇总
        List<AmsPropertyDepr2Vo> result = finalSummary(
                context.getCurrentMonthPropertyDepr2Vos(),
                context.getSourceCodeMap(),
                context.getQueryDto());

        context.setResult(result);

        log.debug("Summary2 最终汇总完成，返回 {} 条记录", result.size());
    }

    /**
     * 最终汇总处理
     */
    private List<AmsPropertyDepr2Vo> finalSummary(List<AmsPropertyDepr2Vo> currentMonthPropertyDepr2Vos,
            Map<String, String> sourceCodeMap,
            com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto dto) {

        // 1. 设置资金来源名称
        for (AmsPropertyDepr2Vo vo : currentMonthPropertyDepr2Vos) {

            if (vo.getIsSummary() != null & Boolean.TRUE.equals(vo.getIsSummary())) {
                continue;
            }
            vo.setSourceName(sourceCodeMap.get(vo.getSourceCode()));

            if (vo.getSourceCode().startsWith("wl")) {
                vo.setSourceCode(vo.getSourceCode().substring(2));
            }

            // 格式化金额字段
            formatAmountFields(vo);
        }

        // 2. 按科室、资产类型、资金来源分组合并
        Map<String, AmsPropertyDepr2Vo> mergedMap = currentMonthPropertyDepr2Vos.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getDeptUseCode() + "-"
                                + (dto.getTypen() ? item.getAssetTypeCode() : item.getAssetTypeName()) + "-"
                                + item.getSourceCode(),
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                this::mergeDeprVoList)));

        List<AmsPropertyDepr2Vo> result = mergedMap.values().stream().collect(Collectors.toList());

        // 3. 根据科室代码过滤数据
        if (dto.getDeptCode() != null && !dto.getDeptCode().trim().isEmpty()) {
            result = result.stream()
                    .filter(item -> item.getDeptUseCode().equals(dto.getDeptCode()))
                    .collect(Collectors.toList());
        }

        // 4. 按科室分组并计算小计
        addDepartmentSummaries(result);

        // 5. 最终排序
        result.sort((a, b) -> {
            int deptCompare = a.getDeptUseCode().compareTo(b.getDeptUseCode());
            if (deptCompare != 0)
                return deptCompare;

            String aType = a.getAssetTypeCode() == null ? "" : a.getAssetTypeCode();
            String bType = b.getAssetTypeCode() == null ? "" : b.getAssetTypeCode();
            return aType.compareTo(bType);
        });

        return result;
    }

    /**
     * 格式化金额字段
     */
    private void formatAmountFields(AmsPropertyDepr2Vo vo) {
        if (vo.getMonthDeprAmt() != null) {
            vo.setMonthDeprAmt(vo.getMonthDeprAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getLastMonthDeprAmt() != null) {
            vo.setLastMonthDeprAmt(vo.getLastMonthDeprAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getLastMonthDeprAmtChange() != null) {
            vo.setLastMonthDeprAmtChange(vo.getLastMonthDeprAmtChange().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getDeprAmt() != null) {
            vo.setDeprAmt(vo.getDeprAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getDeprAmtSum() != null) {
            vo.setDeprAmtSum(vo.getDeprAmtSum().setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getDeprRate() != null) {
            vo.setDeprRate(vo.getDeprRate().setScale(2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 合并折旧VO列表
     */
    private AmsPropertyDepr2Vo mergeDeprVoList(List<AmsPropertyDepr2Vo> list) {
        AmsPropertyDepr2Vo merged = new AmsPropertyDepr2Vo();
        AmsPropertyDepr2Vo first = list.get(0);
        org.springframework.beans.BeanUtils.copyProperties(first, merged);

        // 计算汇总值
        merged.setMonthDeprAmt(list.stream()
                .map(AmsPropertyDepr2Vo::getMonthDeprAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        merged.setLastMonthDeprAmt(list.stream()
                .map(AmsPropertyDepr2Vo::getLastMonthDeprAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        merged.setLastMonthDeprAmtChange(list.stream()
                .map(AmsPropertyDepr2Vo::getLastMonthDeprAmtChange)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        merged.setDeprAmt(list.stream()
                .map(AmsPropertyDepr2Vo::getDeprAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        merged.setDeprAmtSum(list.stream()
                .map(AmsPropertyDepr2Vo::getDeprAmtSum)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        return merged;
    }

    /**
     * 添加科室汇总数据
     */
    private void addDepartmentSummaries(List<AmsPropertyDepr2Vo> result) {
        Map<String, String> deptCodeNameMap = result.stream()
                .collect(Collectors.toMap(
                        AmsPropertyDepr2Vo::getDeptUseCode,
                        AmsPropertyDepr2Vo::getDeptUseName,
                        (e, r) -> e));

        Map<String, AmsPropertyDepr2Vo> summaryByDeptUseCode = result.stream()
                .collect(Collectors.groupingBy(
                        AmsPropertyDepr2Vo::getDeptUseCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                AmsPropertyDepr2Vo::summarize)));

        summaryByDeptUseCode.forEach((key, value) -> {
            value.setDeptUseName(deptCodeNameMap.get(key) + "_小 计");
            value.setDeptUseCode(key);
            value.setIsSummary(true);
            formatAmountFields(value);
            result.add(value);
        });
    }
}
