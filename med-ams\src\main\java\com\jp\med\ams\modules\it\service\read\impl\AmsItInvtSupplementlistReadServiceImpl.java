package com.jp.med.ams.modules.it.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.it.mapper.read.AmsItInvtSupplementlistReadMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtSupplementlistDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtSupplementlistVo;
import com.jp.med.ams.modules.it.service.read.AmsItInvtSupplementlistReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsItInvtSupplementlistReadServiceImpl extends ServiceImpl<AmsItInvtSupplementlistReadMapper, AmsItInvtSupplementlistDto> implements AmsItInvtSupplementlistReadService {

    @Autowired
    private AmsItInvtSupplementlistReadMapper amsItInvtSupplementlistReadMapper;

    @Override
    public List<AmsItInvtSupplementlistVo> queryList(AmsItInvtSupplementlistDto dto) {
        List<AmsItInvtSupplementlistVo> amsItInvtSupplementlistVos = amsItInvtSupplementlistReadMapper.queryList(dto);
        return amsItInvtSupplementlistVos;
    }

    @Override
    public List<AmsItInvtSupplementlistVo> querySupplement(AmsItInvtSupplementlistDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtSupplementlistReadMapper.querySupplement(dto);
    }

    @Override
    public List<AmsItInvtSupplementlistVo> queryDetials(AmsItInvtSupplementlistDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtSupplementlistReadMapper.queryDetialsList(dto);
    }
}
