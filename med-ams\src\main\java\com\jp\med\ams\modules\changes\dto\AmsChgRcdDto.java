package com.jp.med.ams.modules.changes.dto;

import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 资产变更记录表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
@Data
@TableName("ams_chg_rcd")
public class AmsChgRcdDto extends CommonQueryDto {

    /**
     * ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 变动编号
     */
    @TableField(value = "chg_no", insertStrategy = FieldStrategy.NOT_EMPTY)
    private Integer chgNo;

    /**
     * 固定资产码
     */
    @TableField(value = "fa_code", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String faCode;

    /**
     * 变动前值
     */
    @TableField(value = "chg_before", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chgBefore;

    /**
     * 变动后值
     */
    @TableField(value = "chg_after", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chgAfter;

    /**
     * 变更时间
     */
    @TableField(value = "chg_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chgDate;

    /**
     * 变更人
     */
    @TableField(value = "chger", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chger;

    /**
     * 审核人
     */
    @TableField(value = "chker", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chker;

    /**
     * 变动类型
     */
    @TableField(value = "chg_type", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chgType;

    /**
     * 减少类型
     */
    @TableField(value = "redc_way", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String redcWay;

    /**
     * 变动原因
     */
    @TableField(value = "chg_rea", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chgRea;

    /**
     * 备注
     */
    @TableField(value = "memo", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String memo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String createTime;

    /**
     * 医疗机构编码
     */
    @TableField(value = "hospital_id", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

    /**
     * 调拨到
     */
    @TableField(value = "transfer_to", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String transferTo;

    /**
     * 变更时间范围
     */
    @TableField(exist = false)
    private String[] chgDateRange;

    @TableField(exist = false)
    private List<String> faCodes;

    @TableField(exist = false)
    private List<Integer> ids;

    @TableField(exist = false)
    private String chgTypeName;

    @TableField(exist = false)
    private Boolean pass;

    @TableField(exist = false)
    private String assetName;

    @TableField(exist = false)
    private String batchProcessing;

    /**
     * 拆分数量
     */
    @TableField(exist = false)
    private Integer splitCount;

}
