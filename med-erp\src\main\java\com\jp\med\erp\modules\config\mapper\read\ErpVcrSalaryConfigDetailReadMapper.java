package com.jp.med.erp.modules.config.mapper.read;

import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 财务核算-工资凭证科目映射配置明细
 * <AUTHOR>
 * @email -
 * @date 2024-08-22 09:54:23
 */
@Mapper
public interface ErpVcrSalaryConfigDetailReadMapper extends BaseMapper<ErpVcrSalaryConfigDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrSalaryConfigDetailVo> queryList(ErpVcrSalaryConfigDetailDto dto);
}
