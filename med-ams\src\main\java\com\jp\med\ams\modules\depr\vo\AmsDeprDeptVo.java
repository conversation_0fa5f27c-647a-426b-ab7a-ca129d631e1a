package com.jp.med.ams.modules.depr.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 存储部门信息，包括折旧额和其他相关数据
 * <AUTHOR>
 * @email -
 * @date 2024-07-19 16:16:26
 */
@Data
public class AmsDeprDeptVo {

    /**
     * 自增主键
     */
	private Integer id;

    /**
     * 科室编码
     */
    private String deptusecode;

    /**
     * 科室名称
     */
    private String deptusename;

    /** 折旧额 */
	private BigDecimal depramt;

	/** 期号 */
    private String ym;

    /** 创建时间 */
	private Date creattime;

	/** 是否已生成凭证 */
    private String vg;

    /** 有效标志 */
	private String activeflag;

}
