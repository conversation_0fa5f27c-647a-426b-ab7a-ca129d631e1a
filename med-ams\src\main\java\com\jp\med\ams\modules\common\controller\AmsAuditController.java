package com.jp.med.ams.modules.common.controller;

import com.jp.med.ams.modules.common.service.write.AmsAuditService;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 19:13
 * @description:
 */
@Api(value = "人力资源通用控制器", tags = "人力资源通用控制器")
@RestController
@RequestMapping("amsAuditRcdfm")
public class AmsAuditController {

    @Autowired
    private AmsAuditService amsAuditService;


    @ApiOperation("审核结果通知")
    @PostMapping("/complete")
    public CommonFeignResult complete(@RequestBody AuditDetail dto) {
        amsAuditService.complete(dto);
        return CommonFeignResult.build();
    }
}
