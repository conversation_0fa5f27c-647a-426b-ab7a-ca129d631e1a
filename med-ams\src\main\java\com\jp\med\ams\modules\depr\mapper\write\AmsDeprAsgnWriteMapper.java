package com.jp.med.ams.modules.depr.mapper.write;

import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 资产折旧分配
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Mapper
public interface AmsDeprAsgnWriteMapper extends BaseMapper<AmsDeprAsgnDto> {
    /**
     * 根据资产编码修改分摊
     * 
     * @param dto
     * @return
     */
    int updateActiveFlagByFaCode(AmsDeprAsgnDto dto);

    /**
     * 根据资产编码删除所有折旧分配记录
     *
     * @param faCode 资产编码
     * @return 删除的记录数
     */
    int deleteByFaCode(@Param("faCode") String faCode);
}
