package com.jp.med.ams.modules.it.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 信息科库房耗材配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
@TableName("ams_it_invt_cfg" )
public class AmsItInvtCfgDto extends CommonQueryDto {


    @TableId("id")
    private Integer id;

    /** 代码 */
    @TableField("code")
    private String code;

    /** 父节点 */
    @TableField("parent_code")
    private String parentCode;

    /** 类型(1:名称,2:供应商,3:品牌.4.型号 .5:生产厂家) */
    @TableField("type")
    private String type;

    /** 名称 */
    @TableField("name")
    private String name;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 更新人 */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

    /** 删除人 */
    @TableField("delter")
    private String delter;

    /** 删除时间 */
    @TableField("delete_time")
    private String deleteTime;

    /** 有效标志(1:删除) */
    @TableField("active_flag")
    private String activeFlag;

    /** 告警阈值 */
    @TableField("threshold")
    private Integer threshold;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 被选耗材code */
    @TableField(exist = false)
    private List<Integer> selections;

    /** 复选框id集合 */
    @TableField(exist = false)
    private List<String> choice;

}
