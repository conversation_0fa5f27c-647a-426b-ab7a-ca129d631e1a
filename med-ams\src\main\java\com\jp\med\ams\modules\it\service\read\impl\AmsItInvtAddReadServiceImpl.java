package com.jp.med.ams.modules.it.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.it.mapper.read.AmsItInvtAddReadMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtAddDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtAddVo;
import com.jp.med.ams.modules.it.service.read.AmsItInvtAddReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsItInvtAddReadServiceImpl extends ServiceImpl<AmsItInvtAddReadMapper, AmsItInvtAddDto> implements AmsItInvtAddReadService {

    @Autowired
    private AmsItInvtAddReadMapper amsItInvtAddReadMapper;

    @Override
    public List<AmsItInvtAddVo> queryList(AmsItInvtAddDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsItInvtAddReadMapper.queryList(dto);
    }

}
