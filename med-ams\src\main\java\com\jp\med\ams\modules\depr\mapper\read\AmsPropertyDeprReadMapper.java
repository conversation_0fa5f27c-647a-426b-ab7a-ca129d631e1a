package com.jp.med.ams.modules.depr.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDeprVo;
import com.jp.med.ams.modules.depr.vo.AmsPropertyValueAnalysisVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 资产折旧表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 20:21:29
 */
@Mapper
public interface AmsPropertyDeprReadMapper extends BaseMapper<AmsPropertyDeprDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsPropertyDeprVo> queryList(AmsPropertyDeprDto dto);

    /**
     * 查询资产折旧汇总
     *
     * @param dto
     * @return
     */
    List<AmsPropertyDeprVo> queryDeprSummary(AmsPropertyDeprDto dto);

    /**
     * 查询资产折旧汇总
     */

    List<AmsPropertyDepr2Vo> queryDeprSummary2(AmsPropertyDeprDto dto);

    /**
     * 查询资产部门折旧(摊销)情况统计表 月折旧
     *
     * @param dto
     * @return
     */
    List<AmsPropertyDepr2Vo> queryDeprMonth(AmsPropertyDeprDto dto);

    /**
     * 查询科室分摊折旧率
     */
    // @Select("select coalesce(rate,0.0) from ams_depreciation_amortization where
    // dept_code = #{deptCode} union all select 0.0 limit 1")
    List<AmsPropertyDeprDto> queryDeprRate(@Param("deptCodes") Collection<String> deptCodes);

    /**
     * 查询资产价值构成分析表
     *
     * @param dto 查询条件（资产类别、使用管理科室）
     * @return 资产价值构成分析数据列表
     */
    List<AmsPropertyValueAnalysisVo> queryValueAnalysis(AmsPropertyDeprDto dto);


}
