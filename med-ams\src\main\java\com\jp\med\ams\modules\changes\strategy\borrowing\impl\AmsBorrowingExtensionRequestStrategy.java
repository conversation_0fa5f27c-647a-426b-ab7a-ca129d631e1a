package com.jp.med.ams.modules.changes.strategy.borrowing.impl;

import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.feign.AmsMessageFeignService;
import com.jp.med.ams.modules.changes.mapper.write.AmsChngBrwgWriteMapper;
import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import com.jp.med.ams.modules.changes.strategy.borrowing.AmsBorrowingAuditStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.context.AmsBorrowingAuditContext;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.ienum.SysMessageTypeEnum;
import com.jp.med.common.util.FeignExecuteUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产借用续期申请策略
 * 处理借用资产续期申请的业务逻辑
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Slf4j
@Component
public class AmsBorrowingExtensionRequestStrategy implements AmsBorrowingAuditStrategy {

    @Autowired
    private AmsChngBrwgWriteMapper amsChngBrwgWriteMapper;

    @Autowired
    private AmsBorrowingNotificationService notificationService;

    @Autowired
    private AmsMessageFeignService amsMessageFeignService;

    @Override
    public boolean executeAudit(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        log.info("🔄 执行借用续期申请策略，申请人：{}", context.getAuditorName());

        try {
            // 续期申请通常是单个操作
            return executeSingleExtensionRequest(context, dto);
        } catch (Exception e) {
            log.error("❌ 借用续期申请策略执行失败", e);
            return false;
        }
    }

    /**
     * 执行单个续期申请
     */
    private boolean executeSingleExtensionRequest(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        // 更新借用记录（续期申请相关信息）
        int updateResult = amsChngBrwgWriteMapper.updateById(dto);

        if (updateResult != 1) {
            log.error("❌ 更新借用记录失败，ID：{}", dto.getId());
            return false;
        }

        // 发送续期申请通知给审核人员
        sendExtensionRequestNotification(context, dto);

        log.info("✅ 借用续期申请完成，记录ID：{}", dto.getId());
        return true;
    }

    /**
     * 发送续期申请通知
     */
    private void sendExtensionRequestNotification(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        try {
            // 构建系统消息
            SysMessageDto sysMessageDto = new SysMessageDto();

            // 设置接收人（借用人）
            List<String> recipients = new ArrayList<>();
            recipients.add(dto.getLoanee());
            sysMessageDto.setUsers(recipients.toArray(new String[0]));

            // 设置消息内容
            sysMessageDto.setCreator("SYSTEM");
            sysMessageDto.setTitle("资产借用续期申请");

            String messageContent = String.format("%s申请续期借用资产，请处理",
                    context.getAuditorName());

            if (dto.getSysUser() != null && dto.getSysUser().getHrmUser() != null) {
                messageContent = String.format("%s申请续期借用资产，请处理",
                        dto.getSysUser().getHrmUser().getHrmOrgName());
            }

            sysMessageDto.setPushText(messageContent);
            sysMessageDto.setType(SysMessageTypeEnum.AMS_BORROWING.getCode());

            // 设置跳转URL（可根据实际需求调整）
            if (dto.getId() != null) {
                sysMessageDto.setGotoUrl("/ams/borrowing/extension-audit/" + dto.getId());
            }

            // 发送消息
            FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(sysMessageDto));

            log.info("✅ 续期申请通知发送成功");

        } catch (Exception e) {
            log.warn("⚠️ 发送续期申请通知失败", e);
        }
    }

    /**
     * 发送增强版续期申请通知（使用新的通知服务）
     */
    private void sendEnhancedExtensionRequestNotification(AmsBorrowingAuditContext context,
                                                          AmsChngBrwgVo borrowingRecord,
                                                          String extensionReason,
                                                          String newExpectedReturnTime) {
        try {
            notificationService.sendExtensionRequestNotification(
                    borrowingRecord, extensionReason, newExpectedReturnTime);

            log.info("✅ 增强版续期申请通知发送成功");
        } catch (Exception e) {
            log.warn("⚠️ 发送增强版续期申请通知失败", e);
        }
    }

    @Override
    public String getStrategyType() {
        return "EXTENSION_REQUEST";
    }

    @Override
    public boolean validateAuditParams(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        if (context == null || context.getAuditor() == null) {
            log.error("❌ 审核上下文或申请人信息为空");
            return false;
        }

        if (dto == null) {
            log.error("❌ 续期申请数据为空");
            return false;
        }

        if (dto.getId() == null) {
            log.error("❌ 续期申请时借用记录ID为空");
            return false;
        }

        if (!StringUtils.hasText(dto.getLoanee())) {
            log.error("❌ 借用人信息为空");
            return false;
        }

        // 验证续期申请类型
        if (!"3".equals(dto.getUpdateType())) {
            log.error("❌ 续期申请的更新类型应为3");
            return false;
        }

        return true;
    }
}
