package com.jp.med.ams.modules.dashboard.vo;

import lombok.Data;

import java.util.List;

/**
 * 仪表盘图表数据VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class AmsDashboardChartVo {

    /**
     * 完成率趋势数据
     */
    private ChartData completionTrend;

    /**
     * 科室统计数据
     */
    private ChartData departmentStats;

    /**
     * 资产类型分布数据
     */
    private List<PieData> assetTypeDistribution;

    /**
     * 盘盈盘亏对比数据
     */
    private ChartData profitLossComparison;

    /**
     * 月度任务统计数据
     */
    private ChartData monthlyTasks;

    /**
     * 异常资产分析数据
     */
    private RadarData abnormalAssets;

    /**
     * 通用图表数据结构
     */
    @Data
    public static class ChartData {
        /**
         * X轴数据
         */
        private List<String> xAxis;

        /**
         * 系列数据
         */
        private List<Series> series;

        @Data
        public static class Series {
            /**
             * 系列名称
             */
            private String name;

            /**
             * 数据值
             */
            private List<Number> data;

            /**
             * 图表类型
             */
            private String type;
        }
    }

    /**
     * 饼图数据结构
     */
    @Data
    public static class PieData {
        /**
         * 名称
         */
        private String name;

        /**
         * 值
         */
        private Number value;

        /**
         * 百分比
         */
        private Double percentage;
    }

    /**
     * 雷达图数据结构
     */
    @Data
    public static class RadarData {
        /**
         * 指标配置
         */
        private List<Indicator> indicator;

        /**
         * 系列数据
         */
        private List<RadarSeries> series;

        @Data
        public static class Indicator {
            /**
             * 指标名称
             */
            private String name;

            /**
             * 最大值
             */
            private Number max;
        }

        @Data
        public static class RadarSeries {
            /**
             * 系列名称
             */
            private String name;

            /**
             * 数据值
             */
            private List<Number> value;
        }
    }
} 