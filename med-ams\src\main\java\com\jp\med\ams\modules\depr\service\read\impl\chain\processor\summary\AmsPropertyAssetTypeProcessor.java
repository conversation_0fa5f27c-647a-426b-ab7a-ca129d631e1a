package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.config.mapper.read.AmsTypenCfgReadMapper;
import com.jp.med.ams.modules.config.vo.AmsTypenCfgVo;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 资产类型转换处理器
 * 负责将资产新分类转换为大类（前三位 + 补000000）
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyAssetTypeProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Autowired
    private AmsTypenCfgReadMapper amsTypenCfgReadMapper;

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理资产类型转换");

        // 查询资产新分类配置
        List<AmsTypenCfgVo> amsTypenCfgVos = amsTypenCfgReadMapper.queryParent();
        context.setAmsTypenCfgVos(amsTypenCfgVos);

        // 如果需要计算大类折旧，则进行类型转换
        if (context.shouldCalcBigCategoryDepr()) {
            convertAssetTypeToBigType(context.getNormalAssets(), amsTypenCfgVos);
            convertAssetTypeToBigType(context.getHouseRepairAssets(), amsTypenCfgVos);
            log.debug("资产类型转换完成，转换为大类模式");
        } else {
            log.debug("无需进行资产类型转换");
        }
    }

    /**
     * 将资产新分类转换为大类 前三位 + 补000000
     *
     * @param assets         资产列表
     * @param amsTypenCfgVos 资产新分类配置列表
     */
    private void convertAssetTypeToBigType(List<AmsPropertyVo> assets, List<AmsTypenCfgVo> amsTypenCfgVos) {
        assets.forEach(asset -> {
            if (asset.getAssetTypeN() != null) {
                asset.setAssetTypeN(asset.getAssetTypeN().substring(0, 3) + "000000");
                amsTypenCfgVos.stream()
                        .filter(amsTypenCfgVo -> amsTypenCfgVo.getAssetTypeCode().equals(asset.getAssetTypeN()))
                        .findFirst()
                        .ifPresent(amsTypenCfgVo -> {
                            asset.setAssetTypeNName(amsTypenCfgVo.getAssetTypeName());
                        });
            }
        });
    }
}
