package com.jp.med.ams.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.config.mapper.read.AmsStorageCfgReadMapper;
import com.jp.med.ams.modules.config.dto.AmsStorageCfgDto;
import com.jp.med.ams.modules.config.vo.AmsStorageCfgVo;
import com.jp.med.ams.modules.config.service.read.AmsStorageCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsStorageCfgReadServiceImpl extends ServiceImpl<AmsStorageCfgReadMapper, AmsStorageCfgDto> implements AmsStorageCfgReadService {

    @Autowired
    private AmsStorageCfgReadMapper amsStorageCfgReadMapper;

    @Override
    public List<AmsStorageCfgVo> queryList(AmsStorageCfgDto dto) {
        return amsStorageCfgReadMapper.queryList(dto);
    }

}
