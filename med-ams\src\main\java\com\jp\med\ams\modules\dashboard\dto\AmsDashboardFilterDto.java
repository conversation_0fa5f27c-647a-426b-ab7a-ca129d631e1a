package com.jp.med.ams.modules.dashboard.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.util.List;

/**
 * 仪表盘筛选条件DTO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class AmsDashboardFilterDto extends CommonQueryDto {

    /**
     * 年度
     */
    private Integer year;

    /**
     * 科室列表
     */
    private List<String> departments;

    /**
     * 资产类型列表
     */
    private List<String> assetTypes;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 时间范围数组 [startDate, endDate]
     */
    private String[] dateRange;

    /**
     * 医院ID
     */
    private String hospitalId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 是否导出
     */
    private Boolean isExport;

    /**
     * 导出类型 (excel/pdf)
     */
    private String exportType;
} 