package com.jp.med.erp.modules.vcrGen.entity;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7 11:02
 * @description: 报销补助项目
 */
@Data
public class ErpReimSubsItemDetail {

    /** 报销详情id */
    private Long reimDetailId;

    /** 项目 */
    private String item;

    /** 天数/公里 */
    private BigDecimal daysOrKilor;

    /** 标准 */
    private BigDecimal std;

    /** 金额 */
    private BigDecimal amt;

    /** 附件 */
    private String att;

    /** 附件名称 */
    private String attName;

    /** 发票id */
    private String invoId;

    /** 附件 */
    private List<MultipartFile> attFiles;
}
