package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.mapper.read.AmsPropertyDeprReadMapper;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 科室折旧比例处理器
 * 负责获取科室分摊比例数据
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyDeprRateProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Autowired
    private AmsPropertyDeprReadMapper amsPropertyDeprReadMapper;

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理科室折旧比例");

        // 获取科室代码集合
        Set<String> deptCodes = context.getResult().stream()
                .map(AmsPropertyDepr2Vo::getDeptUseCode)
                .collect(Collectors.toSet());
        context.setDeptCodes(deptCodes);

        // 获取科室折旧比例（todo 后续可能固定比例）
        Map<String, BigDecimal> deprRateMap = getDeprRateMap(deptCodes);
        context.setDeprRateMap(deprRateMap);

        // 合并所有资产列表（为房屋维修资产处理做准备）
        context.getNormalAssets().addAll(context.getHouseRepairAssets());

        log.debug("科室折旧比例处理完成，共获取 {} 个科室的比例数据", deprRateMap.size());
    }

    /**
     * 获取折旧率映射
     */
    private Map<String, BigDecimal> getDeprRateMap(Set<String> deptCodes) {
        List<AmsPropertyDeprDto> deprRateList = amsPropertyDeprReadMapper.queryDeprRate(deptCodes);
        return deprRateList.stream()
                .collect(Collectors.toMap(
                        AmsPropertyDeprDto::getDeptCode,
                        item -> item.getRate() == null ? BigDecimal.ZERO : BigDecimal.valueOf(item.getRate())));
    }
}
