package com.jp.med.ams.modules.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 存放地点配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-12 10:23:40
 */
@Data
@TableName("ams_storage_cfg")
public class AmsStorageCfgEntity {

	/** ID */
	@TableId("id")
	private Integer id;

	/** 存放地点编码 */
	@TableField("storage_area_code")
	private String storageAreaCode;

	/** 存放地点 */
	@TableField("storage_area")
	private String storageArea;

	/** 上级编码 */
	@TableField("parent_id")
	private String parentId;

	/** 医疗机构编码 */
	@TableField("hospital_id")
	private String hospitalId;

	/** 有效标志 */
	@TableField("active_flag")
	private String activeFlag;

}
