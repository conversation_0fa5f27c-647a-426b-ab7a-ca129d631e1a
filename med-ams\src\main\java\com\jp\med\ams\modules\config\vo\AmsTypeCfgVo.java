package com.jp.med.ams.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产类型配表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 14:10:05
 */
@Data
public class AmsTypeCfgVo {

	/** ID */
	private Integer id;

	/** 类型编码 */
	private String assetTypeCode;

	/** 类型名称 */
	private String assetTypeName;

	/** 上级编码 */
	private String parentCode;

	/** 有效标志 */
	private String flag;

	/** 折旧方式 */
	private String deprCode;

	/** 计量单位 */
	private String unit;

	/** 残值率 */
	private BigDecimal resr;

	/** 建议使用年限 */
	private Integer years;

	/** 配置*/
	private String stockCfg;

	/** 卡片配置 */
	private String cardName;

}
