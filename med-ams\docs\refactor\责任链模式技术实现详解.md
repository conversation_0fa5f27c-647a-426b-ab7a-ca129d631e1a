# 责任链模式技术实现详解

## 🎯 设计模式概述

责任链模式（Chain of Responsibility Pattern）是一种行为设计模式，它允许你将请求沿着处理者链进行发送。收到请求后，每个处理者均可对请求进行处理，或将其传递给链上的下个处理者。

## 🏗️ 核心架构设计

### 1. 抽象处理器基类

```java
@Slf4j
public abstract class AbstractAmsPropertyDeprProcessor<T> {
    
    /** 下一个处理器 */
    protected AbstractAmsPropertyDeprProcessor<T> nextProcessor;
    
    /**
     * 设置下一个处理器（支持链式调用）
     */
    public AbstractAmsPropertyDeprProcessor<T> setNext(AbstractAmsPropertyDeprProcessor<T> nextProcessor) {
        this.nextProcessor = nextProcessor;
        return nextProcessor;
    }
    
    /**
     * 处理请求的入口方法
     */
    public final void handle(T context) {
        try {
            log.debug("开始执行处理器: {}", this.getClass().getSimpleName());
            
            // 执行当前处理器的业务逻辑
            doProcess(context);
            
            log.debug("完成执行处理器: {}", this.getClass().getSimpleName());
            
            // 传递给下一个处理器
            if (nextProcessor != null) {
                nextProcessor.handle(context);
            }
            
        } catch (Exception e) {
            log.error("处理器 {} 执行失败", this.getClass().getSimpleName(), e);
            throw e;
        }
    }
    
    /**
     * 具体的业务处理逻辑（子类实现）
     */
    protected abstract void doProcess(T context);
}
```

### 2. 上下文设计

#### 主要上下文类
```java
@Data
public class AmsPropertyDeprProcessContext {
    // 输入参数
    private AmsPropertyDeprDto queryDto;
    
    // 基础配置数据
    private String ym;
    private boolean useNewType;
    private LocalDate lastDayOfMonth;
    private List<AmsBasicCfgVo> sourceList;
    private Map<String, String> sourceCodeMap;
    
    // 资产数据
    private List<AmsPropertyVo> houseRepairAssets;
    private List<AmsPropertyVo> normalAssets;
    
    // 分组数据
    private Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedNormalAssets;
    private Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedHouseRepairAssets;
    
    // 处理结果
    private List<AmsPropertyDepr2Vo> result;
    private Set<String> usedFacodeSet;
    
    // 构造方法和便利方法...
}
```

### 3. 处理器工厂

```java
@Slf4j
@Component
public class AmsPropertyDeprProcessorFactory {
    
    // 注入所有处理器
    @Autowired private AmsPropertySourceConfigProcessor sourceConfigProcessor;
    @Autowired private AmsPropertyHouseRepairQueryProcessor houseRepairQueryProcessor;
    // ... 其他处理器
    
    /**
     * 创建 queryDeprSummary 方法的处理器链
     */
    public AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> createSummaryProcessorChain() {
        log.debug("创建 queryDeprSummary 处理器链");
        
        // 构建完整的处理器链
        sourceConfigProcessor
                .setNext(houseRepairQueryProcessor)
                .setNext(normalAssetsQueryProcessor)
                .setNext(mixSourceProcessor)
                .setNext(multiDeptProcessor)
                .setNext(assetTypeProcessor)
                .setNext(finaSubsidyProcessor)
                .setNext(groupingProcessor)
                .setNext(normalAssetsProcessor)
                .setNext(deprRateProcessor)
                .setNext(houseRepairProcessor)
                .setNext(finalSummaryProcessor);
        
        return sourceConfigProcessor;
    }
}
```

## 🔧 具体处理器实现示例

### 1. 资金来源配置处理器

```java
@Slf4j
@Component
public class AmsPropertySourceConfigProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    
    @Autowired
    private AmsBasicCfgReadMapper amsBasicCfgReadMapper;
    
    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理资金来源配置");
        
        // 1. 查询资金来源配置
        AmsBasicCfgDto amsBasicCfgDto = new AmsBasicCfgDto();
        amsBasicCfgDto.setType("资金来源");
        List<AmsBasicCfgVo> sourceList = amsBasicCfgReadMapper.queryList(amsBasicCfgDto);
        context.setSourceList(sourceList);
        
        // 2. 构建资金来源代码映射
        Map<String, String> sourceCodeMap = sourceList.stream()
                .collect(Collectors.toMap(
                        AmsBasicCfgVo::getCode,
                        AmsBasicCfgVo::getName
                ));
        context.setSourceCodeMap(sourceCodeMap);
        
        // 3. 获取财政补助资金代码
        String finaSubsidyCode = sourceList.stream()
                .filter(item -> item.getName().equals("财政补助资金"))
                .findFirst()
                .map(AmsBasicCfgVo::getCode)
                .orElse(null);
        context.setFinaSubsidyCode(finaSubsidyCode);
        
        // 4. 计算月份最后一天
        String ym = context.getYm();
        String lastDay = ym + String.format("%02d",
                YearMonth.parse(ym, DateTimeFormatter.ofPattern("yyyyMM")).lengthOfMonth());
        LocalDate lastDayOfMonth = LocalDate.parse(lastDay, DateTimeFormatter.ofPattern("yyyyMMdd"));
        context.setLastDayOfMonth(lastDayOfMonth);
        
        log.debug("资金来源配置处理完成，共加载 {} 个资金来源", sourceList.size());
    }
}
```

### 2. 复杂业务处理器示例

```java
@Slf4j
@Component
public class AmsPropertyMixSourceProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    
    @Autowired
    private AmsSourceAmountSplitReadMapper amsSourceAmountSplitReadMapper;
    
    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理混合资金来源拆分");
        
        // 查询混合资金来源拆分数据
        AmsSourceAmountSplitDto queryDto = new AmsSourceAmountSplitDto();
        List<AmsSourceAmountSplitVo> splitVos = amsSourceAmountSplitReadMapper.queryList(queryDto);
        context.setAmsSourceAmountSplitVos(splitVos);
        
        // 处理普通资产和房屋维修资产的混合资金来源拆分
        processMixSource(splitVos, context.getNormalAssets());
        processMixSource(splitVos, context.getHouseRepairAssets());
        
        log.debug("混合资金来源拆分处理完成，共处理 {} 条拆分记录", splitVos.size());
    }
    
    /**
     * 处理混合资金来源拆分的具体逻辑
     */
    private void processMixSource(List<AmsSourceAmountSplitVo> splitVos, List<AmsPropertyVo> assets) {
        splitVos.forEach(splitRecord -> {
            assets.stream()
                    .filter(asset -> asset.getFaCode().equals(splitRecord.getFaCode()))
                    .findFirst()
                    .ifPresent(asset -> {
                        // 移除原始资产
                        assets.remove(asset);
                        
                        // 计算总金额
                        BigDecimal totalAmount = calculateTotalAmount(splitRecord);
                        
                        // 创建拆分后的资产
                        addSplitAssets(assets, asset, splitRecord, totalAmount);
                    });
        });
    }
    
    // 其他辅助方法...
}
```

## 📊 性能优化策略

### 1. 上下文对象复用

```java
// 避免在处理器中创建大量临时对象
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    // 复用上下文中的集合对象
    List<AmsPropertyVo> assets = context.getNormalAssets();
    
    // 避免创建新的集合，直接修改现有集合
    assets.removeIf(asset -> shouldRemove(asset));
}
```

### 2. 早期过滤优化

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    // 早期过滤，减少后续处理的数据量
    List<AmsPropertyVo> assets = context.getNormalAssets();
    if (assets.isEmpty()) {
        log.debug("没有普通资产数据，跳过处理");
        return;
    }
    
    // 继续处理...
}
```

### 3. 批量操作优化

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    // 使用批量操作而不是逐个处理
    List<String> faCodes = context.getNormalAssets().stream()
            .map(AmsPropertyVo::getFaCode)
            .collect(Collectors.toList());
    
    // 批量查询而不是循环查询
    List<SomeData> batchData = someMapper.queryBatch(faCodes);
}
```

## 🧪 测试策略

### 1. 单个处理器单元测试

```java
@ExtendWith(MockitoExtension.class)
class AmsPropertySourceConfigProcessorTest {
    
    @Mock
    private AmsBasicCfgReadMapper amsBasicCfgReadMapper;
    
    @InjectMocks
    private AmsPropertySourceConfigProcessor processor;
    
    @Test
    void testDoProcess() {
        // Given
        AmsPropertyDeprProcessContext context = new AmsPropertyDeprProcessContext(createTestDto());
        List<AmsBasicCfgVo> mockSourceList = createMockSourceList();
        when(amsBasicCfgReadMapper.queryList(any())).thenReturn(mockSourceList);
        
        // When
        processor.doProcess(context);
        
        // Then
        assertThat(context.getSourceList()).hasSize(3);
        assertThat(context.getSourceCodeMap()).containsKey("001");
        assertThat(context.getFinaSubsidyCode()).isEqualTo("002");
    }
}
```

### 2. 处理器链集成测试

```java
@SpringBootTest
class AmsPropertyDeprProcessorChainTest {
    
    @Autowired
    private AmsPropertyDeprProcessorFactory processorFactory;
    
    @Test
    void testCompleteProcessorChain() {
        // Given
        AmsPropertyDeprDto dto = createTestDto();
        AmsPropertyDeprProcessContext context = new AmsPropertyDeprProcessContext(dto);
        
        // When
        AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> chain = 
                processorFactory.createSummaryProcessorChain();
        chain.handle(context);
        
        // Then
        assertThat(context.getResult()).isNotEmpty();
        assertThat(context.getResult().get(0).getMonthDeprAmt()).isNotNull();
    }
}
```

## 🔍 监控和调试

### 1. 处理器执行监控

```java
@Aspect
@Component
public class ProcessorMonitoringAspect {
    
    @Around("execution(* com.jp.med.ams.modules.depr.service.read.impl.chain.processor..*(..))")
    public Object monitorProcessor(ProceedingJoinPoint joinPoint) throws Throwable {
        String processorName = joinPoint.getTarget().getClass().getSimpleName();
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            log.info("处理器 {} 执行完成，耗时: {}ms", processorName, executionTime);
            return result;
        } catch (Exception e) {
            log.error("处理器 {} 执行失败", processorName, e);
            throw e;
        }
    }
}
```

### 2. 上下文状态跟踪

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    log.debug("处理器开始执行，当前上下文状态: 普通资产{}条, 房屋维修资产{}条", 
            context.getNormalAssets().size(), 
            context.getHouseRepairAssets().size());
    
    // 处理逻辑...
    
    log.debug("处理器执行完成，当前上下文状态: 结果{}条", 
            context.getResult() != null ? context.getResult().size() : 0);
}
```

## 🚀 扩展指南

### 1. 添加新处理器

```java
@Component
public class NewFeatureProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {
    
    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        // 检查是否需要执行
        if (!shouldProcess(context)) {
            log.debug("跳过新功能处理");
            return;
        }
        
        // 执行新功能逻辑
        processNewFeature(context);
    }
    
    private boolean shouldProcess(AmsPropertyDeprProcessContext context) {
        // 根据上下文条件判断是否需要处理
        return context.getQueryDto().getSomeFlag();
    }
}
```

### 2. 条件分支处理

```java
@Override
protected void doProcess(AmsPropertyDeprProcessContext context) {
    if (context.shouldCalcBigCategoryDepr()) {
        // 执行大类折旧计算
        processBigCategoryDepr(context);
    } else {
        // 执行普通折旧计算
        processNormalDepr(context);
    }
}
```

## 📋 最佳实践

1. **单一职责**：每个处理器只负责一个特定的业务功能
2. **无状态设计**：处理器不保存状态，所有数据通过上下文传递
3. **异常处理**：在基类中统一处理异常，子类专注业务逻辑
4. **日志记录**：记录关键处理步骤和性能指标
5. **测试覆盖**：每个处理器都要有对应的单元测试
6. **文档维护**：及时更新处理器的职责说明和使用方法

---

**文档版本**：v1.0  
**最后更新**：2025-01-20  
**维护人员**：系统架构师
