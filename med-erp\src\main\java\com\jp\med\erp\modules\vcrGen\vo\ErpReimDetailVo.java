package com.jp.med.erp.modules.vcrGen.vo;

import com.jp.med.common.entity.audit.AuditCommonRes;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 报销明细-Erp用
 */
@Data
public class ErpReimDetailVo extends AuditCommonRes {

    /** id */
    private Integer id;

    /** 申请人 */
    private String appyer;

    /** 申请科室（报销科室） */
    private String appyerDept;

    /** 出差时间 */
    private List<String> evectionTime;

    /** 出差时间(app使用) */
    private String appEvectionTime;

    /** 申请时间 */
    private String appyerTime;

    /** 预算控制 */
    private String budgCtrl;

    /** 出差地点 */
    private Integer evectionAddr;

    /** 出差事由 */
    private String evectionRea;

    /** 自驾事由 **/
    private String selfDriveRea;

    /** 开户银行 */
    private String bank;

    /** 户名 */
    private String acctname;

    /** 银行账(卡)号 */
    private String bankcode;

    /** 合计金额小写(元) */
    private BigDecimal sum;

    /** 合计金额大写 */
    private String capSum;

    /** 审核批次号 */
    private String auditBchno;

    /** 业务状态 */
    private String busstas;

    /** 创建人 */
    private String crter;

    /** 创建人名称 **/
    private String crterName;

    /** 创建时间 **/
    private String crateTime;

    /** 医疗机构id */
    private String hospitalId;

    /** 出差开始时间 */
    private String evectionBegnTime;

    /** 出差开始时间 */
    private String evectionEndTime;

    /** 出差性质 */
    private String busMet;

    /** 类型 */
    private String type;

    /** 分摊类型 */
    private String shareType;

    /** 分摊总额 **/
    private BigDecimal shareAmt;

    /**
     * 分摊月份
     */
    private String shareDate;

    /** 附件表编码 **/
    private String attCode;

    /** 附件表编码 **/
    private String attCode2;

    /** 资金类型 **/
    private String fundType;

    /** 申请科室(报销科室)名称 */
    private String appyerDeptName;

    /** 申请人名称 */
    private String appyerName;

    /** 差旅申请id */
    private Long travelApprId;

    /** 经费蕾姐id **/
    private Integer fundingId;

    /**
     * 是否冲抵借款(冲抵借款不需要上传付款证明文件)
     */
    private String isLoan;

    /**
     * 冲抵借款报销id
     */
    private Integer loanReimId;

    /**
     * 冲抵借款金额
     */
    private BigDecimal loanAmt;

    /** 付款证明文件id，用于多个报销对应相同付款文件 **/
    private Integer payRcptId;

    /**
     * 是否有凭证
     */
    private String hasPz;

    /** 审核标识 */
    private String auditFlag;

    /** 科目代码(会计) */
    private String subCode;

    /** 预算科目代码 */
    private String budgetSubCode;

    /** 凭证号 **/
    private String pzh;

    /** 报销对应凭证的idpzh */
    private String idpzh;

    /** 会计期间 **/
    private String kjqj;

    /** 差旅、培训申请页面图片 **/
    private String apprPageImage;

    /** 报销页面 **/
    private String pageImage;

    /** 附件 **/
    private String att;

    /** 附件名称 **/
    private String attName;

    /** 发票记录表id **/
    private String invoId;

    /** 收款乙方名称 **/
    private String oppositeName;

    /** 付款时间，审核通过时间作为付款时间 **/
    private String paidTime;

    //-------工资展示字段start---------
    private String ffMth;

    private Integer num;

    private BigDecimal shouldPay;

    private BigDecimal reducePay;

    private BigDecimal realPay;

    private String remark;

    /** 工资类型 1.基本工资 2:代扣及个人 3: 企业缴 **/
    private String salaryType;
    //-------工资展示字段end ---------


    //-------合同展示字段start---------
    /** 申请人编号 **/
    private String conAppyer;

    /** 申请科室 **/
    private String conAppyerDept;

    /** 合同编码 **/
    private String ctCode;

    /** 合同名称 **/
    private String ctName;

    /** 合同分类编码 **/
    private String typeCode;

    /** 合同附件 **/
    private String contractAtt;

    /** 合同附件名 **/
    private String contractAttName;

    /** 合同总额 **/
    private BigDecimal totalAmt;

    /** 阶段 **/
    private String stage;

    /** 合同id **/
    private Integer contractId;

    /** 所占比例 **/
    private String proportion;

    /** 计划付款时间 **/
    private String paymentTime;

    /** 付款期数id **/
    private Integer paymentId;

    /** 付款方式 **/
    private String paymentType;

    /** 全院统一合同编码 **/
    private String ctUnifiedCode;

    /** 待报销金额 **/
    private BigDecimal needReimAmt;

    //-------合同展示字段end---------

    //-------科研展示字段start---------

    private String rfAppyer;
    private String rfAppyerDept;
    private String projectId;
    private String projectName;
    private String projectLeader;
    private String projectLevel;
    private String topicCategory;
    private String schedulePayTime;
    //-------科研展示字段end-----------

    //--------------BPM ------------
    /**
     * 对应的流程编号
     * <p>
     * 关联 ProcessInstance 的 id 属性
     */
    private String processInstanceId;
    //---------------BPM -------------


    //----------------------药品报销展示字段start---------------------
    /** 供应商 **/
    private String spler;

    /** 付款说明 **/
    private String payIstr;

    /** 状态 **/
    private String status;

    /** 期号 **/
    private String issue;

    /** 父级审核批次号 **/
    private String parentAuditBchno;

    /**
     * 药品付款证明记录id
     */
    private String drugPayId;

    /**
     * 付款类型 1：非集采 2：集采
     */
    private String drugPayType;

    /**
     * 支付方式 1：现金
     */
    private String payMethod;

    /** 上传付款单说明 */
    private String payIstr2;

    //----------------------药品报销展示字段end-----------------------
}
