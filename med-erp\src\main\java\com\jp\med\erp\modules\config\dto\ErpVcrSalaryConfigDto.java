package com.jp.med.erp.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.util.List;

/**
 * 工资凭证配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 00:52:39
 */
@Data
@TableName("erp_vcr_salary_config" )
public class ErpVcrSalaryConfigDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 工资类型 */
    @TableField("salary_type")
    private String salaryType;

    /** 工资报销项目 */
    @TableField("reim_name")
    private String reimName;

    /** 部门code */
    @TableField("dept_code")
    private String deptCode;

    /** 个人扣减-员工编号 */
    @TableField("emp_code")
    private String empCode;

    /** 备注 */
    @TableField("abst")
    private String abst;

    /** 人员类型  在编 招聘 临聘 */
    @TableField("emp_type")
    private String empType;

    /** 科室类型  临床、医技、行政、医辅 */
    @TableField("dept_type")
    private String deptType;

    /** 配置项明细 **/
    @TableField(exist = false)
    private List<ErpVcrSalaryConfigDetailDto> details;

}
