package com.jp.med.ams.modules.depr.mapper.read;

import com.jp.med.ams.modules.depr.dto.AmsDeprDeptDto;
import com.jp.med.ams.modules.depr.vo.AmsDeprDeptVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 存储部门信息，包括折旧额和其他相关数据
 * <AUTHOR>
 * @email -
 * @date 2024-07-19 16:16:26
 */
@Mapper
public interface AmsDeprDeptReadMapper extends BaseMapper<AmsDeprDeptDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsDeprDeptVo> queryList(AmsDeprDeptDto dto);
}
