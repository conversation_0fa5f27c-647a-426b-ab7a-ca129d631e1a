package com.jp.med.ams.modules.changes.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产借用仪表盘VO
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AmsAssetBorrowingDashboardVo", description = "资产借用仪表盘VO")
public class AmsAssetBorrowingDashboardVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "统计概览")
    private StatsOverview statsOverview;

    @ApiModelProperty(value = "我的申请统计")
    private MyApplicationStats myApplicationStats;

    @ApiModelProperty(value = "待审核统计")
    private PendingAuditStats pendingAuditStats;

    @ApiModelProperty(value = "我的借用统计")
    private MyBorrowingStats myBorrowingStats;

    @ApiModelProperty(value = "快捷操作数据")
    private QuickActions quickActions;

    @ApiModelProperty(value = "最近活动列表")
    private List<RecentActivity> recentActivities;

    @ApiModelProperty(value = "过期提醒列表")
    private List<ExpiryReminder> expiryReminders;

    @ApiModelProperty(value = "热门资产统计")
    private List<PopularAsset> popularAssets;

    @ApiModelProperty(value = "科室借用排行")
    private List<DeptBorrowingRank> deptBorrowingRanks;

    @ApiModelProperty(value = "趋势分析数据")
    private TrendAnalysis trendAnalysis;

    /**
     * 统计概览
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatsOverview implements Serializable {
        @ApiModelProperty(value = "总申请数")
        private Integer totalApplications;

        @ApiModelProperty(value = "待审核数")
        private Integer pendingAudit;

        @ApiModelProperty(value = "借用中数量")
        private Integer activeBorrowings;

        @ApiModelProperty(value = "今日归还数")
        private Integer todayReturns;

        @ApiModelProperty(value = "逾期数量")
        private Integer overdueCount;

        @ApiModelProperty(value = "即将到期数量")
        private Integer expiringCount;

        @ApiModelProperty(value = "本月新增申请")
        private Integer monthlyNewApplications;

        @ApiModelProperty(value = "按时归还率")
        private BigDecimal onTimeReturnRate;
    }

    /**
     * 我的申请统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MyApplicationStats implements Serializable {
        @ApiModelProperty(value = "申请中数量")
        private Integer pending;

        @ApiModelProperty(value = "已通过数量")
        private Integer approved;

        @ApiModelProperty(value = "已拒绝数量")
        private Integer rejected;

        @ApiModelProperty(value = "借用中数量")
        private Integer borrowing;

        @ApiModelProperty(value = "本月申请数")
        private Integer monthlyApplications;

        @ApiModelProperty(value = "申请通过率")
        private BigDecimal approvalRate;
    }

    /**
     * 待审核统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PendingAuditStats implements Serializable {
        @ApiModelProperty(value = "紧急申请数")
        private Integer urgent;

        @ApiModelProperty(value = "超时申请数")
        private Integer overdue;

        @ApiModelProperty(value = "今日新增数")
        private Integer today;

        @ApiModelProperty(value = "待审核总数")
        private Integer total;

        @ApiModelProperty(value = "平均处理时间（小时）")
        private BigDecimal avgProcessingHours;
    }

    /**
     * 我的借用统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MyBorrowingStats implements Serializable {
        @ApiModelProperty(value = "借用中数量")
        private Integer active;

        @ApiModelProperty(value = "即将到期数量")
        private Integer expiring;

        @ApiModelProperty(value = "已逾期数量")
        private Integer overdue;

        @ApiModelProperty(value = "已归还数量")
        private Integer returned;

        @ApiModelProperty(value = "平均借用天数")
        private BigDecimal avgBorrowingDays;
    }

    /**
     * 快捷操作
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuickActions implements Serializable {
        @ApiModelProperty(value = "可申请资产数量")
        private Integer availableAssets;

        @ApiModelProperty(value = "待归还数量")
        private Integer pendingReturns;

        @ApiModelProperty(value = "可续期数量")
        private Integer renewableCount;

        @ApiModelProperty(value = "待审核数量")
        private Integer pendingAudits;
    }

    /**
     * 最近活动
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecentActivity implements Serializable {
        @ApiModelProperty(value = "活动ID")
        private Integer id;

        @ApiModelProperty(value = "活动类型：apply-申请，audit-审核，borrow-借用，return-归还，renew-续期")
        private String type;

        @ApiModelProperty(value = "活动描述")
        private String description;

        @ApiModelProperty(value = "资产名称")
        private String assetName;

        @ApiModelProperty(value = "资产编码")
        private String assetCode;

        @ApiModelProperty(value = "操作人")
        private String operator;

        @ApiModelProperty(value = "操作时间")
        private LocalDateTime operateTime;

        @ApiModelProperty(value = "状态")
        private String status;
    }

    /**
     * 过期提醒
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpiryReminder implements Serializable {
        @ApiModelProperty(value = "借用记录ID")
        private Integer borrowingId;

        @ApiModelProperty(value = "资产名称")
        private String assetName;

        @ApiModelProperty(value = "资产编码")
        private String assetCode;

        @ApiModelProperty(value = "借用人")
        private String borrower;

        @ApiModelProperty(value = "借用科室")
        private String borrowDept;

        @ApiModelProperty(value = "借用时间时间")
        private LocalDateTime loaneeTime;
        @ApiModelProperty(value = "预计归还时间")
        private LocalDateTime expectedReturnTime;

        @ApiModelProperty(value = "剩余天数")
        private Integer remainingDays;

        @ApiModelProperty(value = "提醒类型：expiring-即将到期，overdue-已逾期")
        private String reminderType;

        @ApiModelProperty(value = "紧急程度：low-低，medium-中，high-高")
        private String urgency;
    }

    /**
     * 热门资产
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PopularAsset implements Serializable {
        @ApiModelProperty(value = "资产类型代码")
        private String assetTypeCode;

        @ApiModelProperty(value = "资产类型名称")
        private String assetTypeName;

        @ApiModelProperty(value = "借用次数")
        private Integer borrowCount;

        @ApiModelProperty(value = "当前借用数量")
        private Integer currentBorrowings;

        @ApiModelProperty(value = "平均借用天数")
        private BigDecimal avgBorrowingDays;

        @ApiModelProperty(value = "热门指数")
        private BigDecimal popularityIndex;
    }

    /**
     * 科室借用排行
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeptBorrowingRank implements Serializable {
        @ApiModelProperty(value = "科室ID")
        private String deptId;

        @ApiModelProperty(value = "科室名称")
        private String deptName;

        @ApiModelProperty(value = "借用次数")
        private Integer borrowCount;

        @ApiModelProperty(value = "当前借用数量")
        private Integer currentBorrowings;

        @ApiModelProperty(value = "按时归还率")
        private BigDecimal onTimeReturnRate;

        @ApiModelProperty(value = "排名")
        private Integer rank;
    }

    /**
     * 趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis implements Serializable {
        @ApiModelProperty(value = "日期标签列表")
        private List<String> dateLabels;

        @ApiModelProperty(value = "申请数量趋势")
        private List<Integer> applicationTrend;

        @ApiModelProperty(value = "审核通过趋势")
        private List<Integer> approvalTrend;

        @ApiModelProperty(value = "归还数量趋势")
        private List<Integer> returnTrend;

        @ApiModelProperty(value = "逾期数量趋势")
        private List<Integer> overdueTrend;

        @ApiModelProperty(value = "分析周期：daily-日，weekly-周，monthly-月")
        private String period;
    }
}
