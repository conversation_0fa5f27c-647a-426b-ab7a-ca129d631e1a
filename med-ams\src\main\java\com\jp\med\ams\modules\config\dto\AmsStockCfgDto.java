package com.jp.med.ams.modules.config.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.ams.modules.config.vo.AmsStockCfgVo;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 资产卡片配置
 * <AUTHOR>
 * @email -
 * @date 2023-10-10 15:21:09
 */
@Data
@TableName("ams_stock_cfg" )
public class AmsStockCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 卡片编码 */
    @TableField(value = "card_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String cardCode;

    /** 卡片名称 */
    @TableField(value = "card_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String cardName;

    /** 字段 */
    @TableField(value = "fld",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String fld;

    /** 是否必填 */
    @TableField(value = "mustl",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String mustl;

    @TableField(exist = false)
    private List<AmsStockCfgVo> list;

    @TableField(exist = false)
    private List<String> flds;

}
