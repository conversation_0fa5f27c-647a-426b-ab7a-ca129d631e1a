package com.jp.med.ams.modules.amsPropertyInAndOut.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsOutStockAuditReadMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.read.AmsOutStockAuditReadService;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.write.AmsOutStockAuditWriteService;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.dto.AmsRecordsDto;
import com.jp.med.ams.modules.property.mapper.read.AmsRecordsReadMapper;
import com.jp.med.ams.modules.property.service.read.AmsPropertyReadService;
import com.jp.med.ams.modules.property.service.write.AmsPropertyWriteService;
import com.jp.med.ams.modules.property.vo.AmsRecordsVo;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;


/**
 * 资产入库审核
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-27 20:15:50
 */
@Api(value = "资产入库审核", tags = "资产入库审核")
@RestController
@RequestMapping("amsOutStockAudit")
public class AmsOutStockAuditController {

    @Autowired
    private AmsOutStockAuditReadService amsOutStockAuditReadService;

    @Autowired
    private AmsOutStockAuditWriteService amsOutStockAuditWriteService;

    @Autowired
    private AmsOutStockAuditReadMapper amsOutStockAuditReadMapper;

    @Autowired
    private AmsPropertyReadService amsPropertyReadService;

    @Autowired
    private AmsRecordsReadMapper amsRecordsReadMapper;

    @Autowired
    private AmsPropertyWriteService amsPropertyWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产入库审核")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsOutStockAuditDto dto) {
        return CommonResult.paging(amsOutStockAuditReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("供应商查询资产入库审核")
    @PostMapping("/thirdPartyInList")
    public CommonResult<?> thirdPartyInList(@RequestBody AmsOutStockAuditDto dto) {
        if (StrUtil.isEmpty(dto.getInUser()) || StrUtil.isEmpty(dto.getQrId())) {
            return CommonResult.failed("参数错误");
        }
        AmsOutStockAuditDto amsOutStockAuditDto = new AmsOutStockAuditDto();
        amsOutStockAuditDto.setQrId(dto.getQrId());
        amsOutStockAuditDto.setInUser(dto.getInUser());
        amsOutStockAuditDto.setPageNum(dto.getPageNum());
        amsOutStockAuditDto.setPageSize(dto.getPageSize());
        return CommonResult.paging(amsOutStockAuditReadService.queryList(amsOutStockAuditDto));
    }


    @ApiOperation("供应商修改")
    @PostMapping("/updatePropertyDataThirdParty")
    public CommonResult<?> updatePropertyDataThirdParty(AmsPropertyDto dto) {
        //校验供应商是否可以修改
        QueryWrapper<AmsOutStockAuditDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("qr_id", dto.getQrId())
                .eq("property_id", dto.getId())
                .eq("in_user", dto.getInpter());
        List<AmsOutStockAuditDto> amsOutStockAuditDtos = amsOutStockAuditReadMapper.selectList(queryWrapper);
        if (amsOutStockAuditDtos.isEmpty()) {
            return CommonResult.failed("该资产不属于该供应商");
        }
        AmsOutStockAuditDto amsOutStockAuditDto = amsOutStockAuditDtos.get(0);
        if (!StrUtil.equals(amsOutStockAuditDto.getProsstas(), "0")) {
            return CommonResult.failed("该资产已进入审核流程或已入库无法修改");
        }

        dto.setType("4");

        amsPropertyWriteService.updateData(dto);
        return CommonResult.success();
    }

    /**
     * 查询byId 供应商查询
     */
    @ApiOperation("查询资产详情byId 供应商查询")
    @PostMapping("/queryByIdThirdParty")
    public CommonResult<?> queryByIdThirdParty(@RequestBody AmsOutStockAuditDto dto) {
        if (StrUtil.isEmpty(dto.getInUser()) || StrUtil.isEmpty(dto.getQrId()) || dto.getPropertyId() == null) {
            return CommonResult.failed("参数错误");
        }
        AmsOutStockAuditDto amsOutStockAuditDto = new AmsOutStockAuditDto();
        amsOutStockAuditDto.setQrId(dto.getQrId());
        amsOutStockAuditDto.setInUser(dto.getInUser());
        amsOutStockAuditDto.setPageNum(dto.getPageNum());
        amsOutStockAuditDto.setPageSize(dto.getPageSize());
        QueryWrapper<AmsOutStockAuditDto> queryWrapper = new QueryWrapper<AmsOutStockAuditDto>()
                .eq("qr_id", dto.getQrId())
                .eq("in_user", dto.getInUser())
                .eq("property_id", dto.getPropertyId());
        if (!amsOutStockAuditReadMapper.exists(queryWrapper)) {
            throw new RuntimeException("资产不存在");
        }
        AmsRecordsDto amsPropertyDto = new AmsRecordsDto();
        amsPropertyDto.setPropertyId(dto.getPropertyId());

        List<AmsRecordsVo> amsRecordsVos = amsRecordsReadMapper.queryList(amsPropertyDto);

        AmsPropertyDto propertyDto = amsPropertyReadService.getById(dto.getPropertyId());

        return CommonResult.success(new HashMap<String, Object>() {{
            put("property", propertyDto);
            put("records", amsRecordsVos);
        }});
    }


    @ApiOperation("查询待审核数量")
    @PostMapping("/queryAuditCount")
    public CommonResult<?> queryAuditCount(@RequestBody AmsOutStockAuditDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsOutStockAuditReadService.queryAuditCount(dto));
    }

    @ApiOperation("查询待确认出库数量")
    @PostMapping("/queryOutCount")
    public CommonResult<?> queryOutCount(@RequestBody AmsOutStockAuditDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsOutStockAuditReadService.queryOutCount(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增资产入库审核")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsOutStockAuditDto dto) {
        amsOutStockAuditWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产入库审核")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsOutStockAuditDto dto) {
        amsOutStockAuditWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产入库审核")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsOutStockAuditDto dto) {
        amsOutStockAuditWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 批量审批
     * batchApplyBackApi
     */
    @ApiOperation("批量审批")
    @PostMapping("/batchApply")
    public CommonResult<?> batchApply(AmsOutStockAuditDto dto) {

        return CommonResult.success(amsOutStockAuditWriteService.batchApply(dto));
    }


}
