package com.jp.med.erp.modules.vcrGen.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 分摊往来单位
 */
public enum ShareRelCoEnum {
    /**
     * 水费往来单位
     */
    SHARE_TYPE_WATER("1","121803","其他","10020101","中国银行","010204","支付的其他与日常活动有关的现金"),

    SHARE_TYPE_ELECTRICITY("2","121803","其他","10020101","中国银行","010204","支付的其他与日常活动有关的现金");

    private String shareType;

    private String actigCRCode;

    private String actigCRName;


    private String actigDRCode;

    private String actigDRName;

    private String cashFlowCode;

    private String cashFlowName;

    ShareRelCoEnum(String shareType, String actigCRCode, String actigCRName, String actigDRCode, String actigDRName, String cashFlowCode, String cashFlowName) {
        this.shareType = shareType;
        this.actigCRCode = actigCRCode;
        this.actigCRName = actigCRName;
        this.actigDRCode = actigDRCode;
        this.actigDRName = actigDRName;
        this.cashFlowCode = cashFlowCode;
        this.cashFlowName = cashFlowName;
    }

    public String getShareType() {
        return shareType;
    }

    public String getActigCRCode() {
        return actigCRCode;
    }

    public String getActigCRName() {
        return actigCRName;
    }

    public String getActigDRCode() {
        return actigDRCode;
    }

    public String getActigDRName() {
        return actigDRName;
    }

    public String getCashFlowCode() {
        return cashFlowCode;
    }

    public String getCashFlowName() {
        return cashFlowName;
    }

    public static ShareRelCoEnum getByType(String shareType) {
        for (ShareRelCoEnum status : ShareRelCoEnum.values()) {
            if (StringUtils.equals(status.getShareType(),shareType)) {
                return status;
            }
        }
        return null;
    }
}
