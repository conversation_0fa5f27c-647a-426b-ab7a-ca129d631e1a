-- =================================================================================================
-- 修复缺失字段脚本：为源表添加缺失的字段
-- 功能：确保源表具有导入脚本所需的所有字段
-- 执行时机：在运行导入脚本之前执行
-- =================================================================================================

-- 为入库源表添加缺失字段
ALTER TABLE mmis_temp_inport_storage_six 
ADD COLUMN IF NOT EXISTS mat_unique_code varchar(100),
ADD COLUMN IF NOT EXISTS item_num varchar(50),
ADD COLUMN IF NOT EXISTS meter_code varchar(50),
ADD COLUMN IF NOT EXISTS create_time varchar(50),
ADD COLUMN IF NOT EXISTS bill_date date,
ADD COLUMN IF NOT EXISTS hospital_id varchar(50),
ADD COLUMN IF NOT EXISTS amt numeric(20,6),
ADD COLUMN IF NOT EXISTS is_deleted int,
ADD COLUMN IF NOT EXISTS supplier_id int,
ADD COLUMN IF NOT EXISTS modspec varchar(200);

-- 为出库源表添加缺失字段
ALTER TABLE mmis_temp_xinxike_outbound_six 
ADD COLUMN IF NOT EXISTS out_target_org_id varchar(100),
ADD COLUMN IF NOT EXISTS out_appyer varchar(50),
ADD COLUMN IF NOT EXISTS mat_unique_code varchar(100),
ADD COLUMN IF NOT EXISTS item_num varchar(50),
ADD COLUMN IF NOT EXISTS meter_code varchar(50),
ADD COLUMN IF NOT EXISTS create_time varchar(50),
ADD COLUMN IF NOT EXISTS bill_date date,
ADD COLUMN IF NOT EXISTS opter varchar(50),
ADD COLUMN IF NOT EXISTS opter_org varchar(50),
ADD COLUMN IF NOT EXISTS wrhs_addr varchar(50),
ADD COLUMN IF NOT EXISTS item_count numeric(5,2),
ADD COLUMN IF NOT EXISTS hospital_id varchar(50),
ADD COLUMN IF NOT EXISTS is_deleted int,
ADD COLUMN IF NOT EXISTS chk_state varchar(1),
ADD COLUMN IF NOT EXISTS appy_org_id varchar(50),
ADD COLUMN IF NOT EXISTS modspec varchar(200);

-- 验证字段添加结果
SELECT 
    '入库源表字段检查' as table_check,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_inport_storage_six'
  AND column_name IN (
    'mat_unique_code', 'item_num', 'meter_code', 'create_time', 
    'bill_date', 'hospital_id', 'amt', 'is_deleted', 'supplier_id', 'modspec'
  )
ORDER BY column_name;

SELECT 
    '出库源表字段检查' as table_check,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
  AND column_name IN (
    'out_target_org_id', 'out_appyer', 'mat_unique_code', 'item_num', 
    'meter_code', 'create_time', 'bill_date', 'opter', 'opter_org', 
    'wrhs_addr', 'item_count', 'hospital_id', 'is_deleted', 'chk_state', 
    'appy_org_id', 'modspec'
  )
ORDER BY column_name;
