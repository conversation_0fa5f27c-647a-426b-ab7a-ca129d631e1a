package com.jp.med.ams.modules.amsPropertyInAndOut.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * 资产出库审批记录表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Data
@TableName("ams_out_stock_audit")
public class AmsOutStockAuditDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 审核批次号
     */
    @TableField("bchno")
    private String bchno;

    /**
     * 资产表id
     */
    @TableField("property_id")
    private Integer propertyId;

    /**
     * 出库科室
     */
    @TableField("out_dept")
    private String outDept;

    /**
     * 入库科室
     */
    @TableField("in_dept")
    private String inDept;

    /**
     * 出库人
     */
    @TableField("out_user")
    private String outUser;

    /**
     * 入库人
     */
    @TableField("in_user")
    private String inUser;

//    receive

    /**
     * 接收人
     */
    @TableField("receive_user")
    private String receiveUser;

    /**
     * 接收科室
     */
    @TableField("receive_dept")
    private String receiveDept;

    /**
     * 接收时间
     */
    @TableField("receive_time")
    private Date receiveTime;

    /**
     * 出库时间
     */
    @TableField("out_time")
    private Date outTime;

    /**
     * 接收时间
     */
    @TableField("in_time")
    private Date inTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 接收备注
     */
    @TableField("rec_remarks")
    private String recRemarks;

    /**
     * 业务状态 0待出库 1已出库待审核 2 出库审核中 3出库审核完成 4 出库审核完成并且已确认
     */
    @TableField("prosstas")
    private String prosstas;

    /**
     * 医疗机构id
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 有效标准
     */
    @TableField("active_flag")
    private String activeFlag;

    /**
     * 扫码入库id
     */
    @TableField("qr_id")
    private String qrId;

    /**
     * 出库科室
     */
    @TableField(exist = false)
    private String outDeptName;

    /**
     * 入库科室
     */
    @TableField(exist = false)
    private String inDeptName;

    /**
     * 出库建人
     */
    @TableField(exist = false)
    private String outUserName;

    /**
     * 入库人名称
     */
    @TableField(exist = false)
    private String inUserName;

    /**
     * 接收人名称
     */
    @TableField(exist = false)
    private String receiveUserName;

    /**
     * 接收科室名称
     */
    @TableField(exist = false)
    private String receiveDeptName;

    /**
     * 资产名称
     */
    @TableField(exist = false)
    private String assetName;

    /**
     * 当前审核人
     */
    @TableField(exist = false)
    private String nowChker;


    /**
     * 批量审批id
     */
    @TableField(exist = false)
    private List<Long> batchAuditIds;

    /**
     * 审批签名
     */
    @TableField(exist = false)
    private MultipartFile signFile;

    /**
     * 审批签名url
     */
    @TableField(exist = false)
    private String signFileUrl;
}
