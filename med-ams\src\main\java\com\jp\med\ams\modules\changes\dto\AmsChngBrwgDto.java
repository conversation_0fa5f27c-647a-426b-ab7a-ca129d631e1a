package com.jp.med.ams.modules.changes.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 资产借用信息表
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 16:01:14
 */
@Data
@TableName("ams_chng_brwg" )
public class AmsChngBrwgDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 医疗器械分类目录ID
     */
    @TableField("ccm_id")
    private Long ccmId;

    /** 资产类型编码 */
    @TableField("asset_type_code")
    private String assetTypeCode;

    /** 固定资产编码 */
    @TableField("fa_code")
    private String faCode;

    /**
     * 单据号
     */
    @TableField("doc_num")
    private String docNum;

    /**
     * 办理状态
     */
    @TableField("prosstas")
    private String prosstas;

    /**
     * 业务状态
     */
    @TableField("busstas")
    private String busstas;

    /** 借用科室 */
    @TableField("loanee_dept")
    private String loaneeDept;

    /**
     * 借用人
     */
    @TableField("loanee")
    private String loanee;

    /**
     * 借用时间
     */
    @TableField("loanee_time")
    private String loaneeTime;

    /**
     * 预计归还时间
     */
    @TableField("exp_rtn_time")
    private String expRtnTime;

    /**
     * 实际归还时间
     */
    @TableField("act_rtn_time")
    private String actRtnTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 归还备注
     */
    @TableField("back_remarks")
    private String backRemarks;

    /** 归还时间 */
    @TableField("back_time")
    private String backTime;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 创建科室
     */
    @TableField("crter_dept")
    private String crterDept;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 医疗机构ID
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 审核备注
     */
    @TableField("chk_remarks")
    private String chkRemarks;

    /**
     * 审核时间
     */
    @TableField("chk_time")
    private String chkTime;

    /**
     * 有效标志
     */
    @TableField("active_flag")
    private String activeFlag;

    /** 状态，1：申请中，2：待审核，3：已完成 */
    @TableField(exist = false)
    private String status;

    /** 记录id */
    @TableField(exist = false)
    private List<Integer> ids;

    /**
     * 更改类型1：审核，2：归还 3续期
     */
    @TableField(exist = false)
    private String updateType;

    /** 借用记录类型：1：我的借用，2：我的审核 */
    @TableField(exist = false)
    private String loaneeRecordType;

    /** 借用记录类型：时间类型 */
    @TableField(exist = false)
    private String timeType;

    /** 时间 */
    @TableField(exist = false)
    private List<String> time;
}
