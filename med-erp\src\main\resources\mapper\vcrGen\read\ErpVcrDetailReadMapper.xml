<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.erp.modules.vcrGen.vo.ErpVcrDetailVo" id="vcrDetailMap">
        <result property="id" column="id"/>
        <result property="idpzh" column="idpzh"/>
        <result property="kjqj" column="kjqj"/>
        <result property="pzh" column="pzh"/>
        <result property="pzje" column="pzje"/>
        <result property="yspzje" column="yspzje"/>
        <result property="status" column="status"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpVcrDetailVo">
        select
            a.id as id,
            a.idpzh as idpzh,
            a.kjqj as kjqj,
            a.pzh as pzh,
            a.pzje as pzje,
            a.yspzje as yspzje,
            a.status as status,
            b.type as type,
            b.sup_type as supType
        from erp_vcr_detail a
        left join erp_vcr_apply b
        on a.idpzh = b.idpzh
        <where>
            <if test="ids!=null">
                a.id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="idpzh!=null and idpzh!=''">
                and a.idpzh = #{idpzh,jdbcType=VARCHAR}
            </if>
            <if test="kjqj!=null and kjqj != ''">
                and a.kjqj = #{kjqj,jdbcType=VARCHAR}
            </if>
            <if test="type!=null and type!=''">
                and b.type = #{type,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryVcrGenList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT A.*
        FROM
            (
                SELECT
                       A.ID AS ID,
                       A.appyer AS appyer,
                       A.evection_begn_time AS evectionBegnTime,
                       A.evection_end_time AS evectionEndTime,
                       A.appyer_time AS appyerTime,
                       A.budg_ctrl AS budgCtrl,
                       A.evection_addr AS evectionAddr,
                       A.evection_rea AS evectionRea,
                        a.self_drive_rea as selfDriveRea,
                       A.bank AS bank,
                       A.acctname AS acctname,
                       A.bankcode AS bankcode,
                       A."sum" AS "sum",
                       A.cap_sum AS capSum,
                       A.audit_bchno AS auditBchno,
                       A.busstas AS busstas,
                       A.crter AS crter,
                       A.hospital_id AS hospitalId,
                       A."type" AS TYPE,
                       A.share_type AS shareType,
                       A.share_amt AS shareAmt,
                       A.share_date AS shareDate,
                       A.att_code AS attCode,
                       A.fund_type AS fundType,
                       A.bus_met AS busMet,
                       A.appyer_dept AS appyerDept,
                       A.page_image AS pageImage,
                       A.att as att,
                       A.att_name as attName,
                       A.invo_id as invoId,
                       A.opposite_name AS oppositeName,
                       A.travel_appr_id AS travelApprId,
                        a.project_id as projectId,
                        a.funding_id as fundingId,
                        a.pay_method          as payMethod,
                        a.is_loan            as isLoan,
                        a.loan_reim_id       as loanReimId,
                        a.loan_amt           as loanAmt,
                        a.pay_rcpt_id        as payRcptId,
                        a.has_pz             as hasPz,
                       A.process_instance_id as processInstanceId,
                       case
                            when a.audit_bchno is not null then H.chk_time
                            else TO_CHAR(G.end_time, 'yyyy-MM-dd HH24:MI:SS')
                      END AS paidTime,
                    <if test="type != null and type != ''">
                        <choose>
                            <when test='type == "1" or type == "2"'>
                                f.page_image AS apprPageImage,
                            </when>
                            <when test='type == "5"'>
                                m.ff_mth as ffMth,
                                m.num as num,
                                m.should_pay as shouldPay,
                                m.reduce_pay as reducePay,
                                m.real_pay as realPay,
                                m.remark as remark,
                                m.type as salaryType,
                            </when>
                            <when test='type == "6"'>
                                m.appyer            as conAppyer,
                                m.appyer_dept       as conAppyerDept,
                                m.ct_code           as ctCode,
                                m.ct_name           as ctName,
                                m.ct_unified_code   as ctUnifiedCode,
                                m.type_code         as typeCode,
                                m.contract_att      as contractAtt,
                                m.contract_att_name as contractAttName,
                                m.total_amt         as totalAmt,
                                m.stage             as stage,
                                m.contract_id       as contractId,
                                m.proportion        as proportion,
                                m.payment_time      as paymentTime,
                                m.payment_id        as paymentId,
                                m.payment_type      as paymentType,
                                m.need_reim_amt     as needReimAmt,
                            </when>
                            <when test='type == "8"'></when>
                            <!-- 科研 -->
                            <when test='type == "9"'>
                                m.appyer as rfAppyer,
                                m.appyer_dept as rfAppyerDept,
                                m.project_id as projectId,
                                m.project_name as projectName,
                                m.project_leader as projectLeader,
                                m.project_level as projectLevel,
                                m.topic_category as topicCategory,
                                m.reim_amt as reimAmt,
                                m.schedule_pay_time as schedulePayTime,
                            </when>
                        </choose>
                    </if>
                       b.org_name as appyerDeptName,
                       C.emp_name as appyerName,
                       '0' as auditFlag,
                       e.pzid,
                       CASE WHEN e.pzid IS NULL THEN
                                '0' ELSE'1'
                           END AS genFlag,
                       e.idpzh,
                       q.pzh,
                       q.idpzh,
                       q.kjqj
                FROM
                    ecs_reim_detail A
                     LEFT JOIN hrm_org b ON A.appyer_dept = b.org_id
                      LEFT JOIN hrm_employee_info C ON A.appyer = C.emp_code
                    <choose>
                        <!-- 零星采购和物资采购查询方式不同 -->
                        <when test='type == "8" or type == "10"'>
                          LEFT JOIN (select b.idpzh,b.pzid,a.module_id from erp_vcr_preview a,erp_vcr_item_detail b where a.vpzh = b.pzid group by b.idpzh,b.pzid,a.module_id) e ON A.ID = e.module_id
                        </when>
                        <otherwise>
                          LEFT JOIN ( SELECT idpzh,pzid FROM erp_vcr_item_detail A GROUP BY A.idpzh,A.pzid ) e ON A.ID :: TEXT = e.pzid
                        </otherwise>
                    </choose>
                    LEFT JOIN erp_vcr_detail Q ON e.idpzh = Q.idpzh
                    LEFT JOIN ( SELECT bchno, chk_time, ROW_NUMBER ( ) OVER ( PARTITION BY bchno ORDER BY chk_seq DESC ) AS rn FROM ecs_audit_rcdfm ) H ON H.rn = 1 and H.bchno = A.audit_bchno
                    LEFT JOIN ( SELECT process_instance_id, end_time, ROW_NUMBER ( ) OVER ( PARTITION BY process_instance_id ORDER BY start_time DESC ) AS rn FROM bpm_tsktime_edit ) G ON G.rn = 1 and G.process_instance_id = A.process_instance_id
                    <if test="type != null and type != ''">
                        <choose>
                            <when test='type == "1" or type == "2"'>
                                LEFT JOIN ecs_reim_travel_appr f on a.travel_appr_id = f."id"
                            </when>
                            <when test='type == "5"'>
                                LEFT JOIN ecs_reim_salary_task m on a.travel_appr_id = m.id
                            </when>
                            <when test='type == "6"'>
                                LEFT JOIN ecs_reim_contract_task m on a.travel_appr_id = m.id
                            </when>
                            <when test='type == "8"'>

                            </when>
                            <when test='type == "9"'>
                                LEFT JOIN ecs_research_funding_task m on a.travel_appr_id = m.id
                            </when>
                        </choose>
                    </if>
        WHERE A.type = #{type,jdbcType=VARCHAR}
            and A.busstas = '1'
            and A.appyer is not null
            ) A
        WHERE
            A.genFlag =  #{genFlag,jdbcType=VARCHAR}
    </select>

    <select id="queryPurcVcrGenList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT A.*,q.pzh,q.kjqj
        FROM
            (
                SELECT
                       A.ID AS ID,
                       A.appyer AS appyer,
                       A.evection_begn_time AS evectionBegnTime,
                       A.evection_end_time AS evectionEndTime,
                       A.appyer_time AS appyerTime,
                       A.budg_ctrl AS budgCtrl,
                       A.evection_addr AS evectionAddr,
                       A.evection_rea AS evectionRea,
                        a.self_drive_rea as selfDriveRea,
                       A.bank AS bank,
                       A.acctname AS acctname,
                       A.bankcode AS bankcode,
                       A."sum" AS "sum",
                       A.cap_sum AS capSum,
                       A.audit_bchno AS auditBchno,
                       A.busstas AS busstas,
                       A.crter AS crter,
                       A.hospital_id AS hospitalId,
                       A."type" AS TYPE,
                       A.share_type AS shareType,
                       A.share_amt AS shareAmt,
                       A.share_date AS shareDate,
                       A.att_code AS attCode,
                       A.fund_type AS fundType,
                       A.bus_met AS busMet,
                       A.appyer_dept AS appyerDept,
                       A.page_image AS pageImage,
                       A.att as att,
                       A.att_name as attName,
                       A.invo_id as invoId,
                       A.opposite_name AS oppositeName,
                       A.travel_appr_id AS travelApprId,
                        a.project_id as projectId,
                        a.funding_id as fundingId,
                        a.pay_method          as payMethod,
                        a.is_loan            as isLoan,
                        a.loan_reim_id       as loanReimId,
                        a.loan_amt           as loanAmt,
                        a.pay_rcpt_id        as payRcptId,
                        a.has_pz             as hasPz,
                       A.process_instance_id as processInstanceId,
                       case
                            when a.audit_bchno is not null then H.chk_time
                            else TO_CHAR(G.end_time, 'yyyy-MM-dd HH24:MI:SS')
                      END AS paidTime,
                       b.org_name as appyerDeptName,
                       C.emp_name as appyerName,
                       '0' as auditFlag,
                       case when e1.idpzh is null then e.idpzh else e1.idpzh end as idpzh,
					   case when e1.pzid is null then e.pzid else e1.pzid end as pzid
                FROM
                    ecs_reim_detail A
                    LEFT JOIN hrm_org b ON A.appyer_dept = b.org_id
                    LEFT JOIN hrm_employee_info C ON A.appyer = C.emp_code
                    LEFT JOIN (select b.idpzh,b.pzid,a.module_id from erp_vcr_preview a,erp_vcr_item_detail b
                                where a.vpzh = b.pzid
                                <if test="supType != null and supType != ''">
                                    and a.sup_type = #{supType,jdbcType=VARCHAR}
                                </if>
                                  group by b.idpzh,b.pzid,a.module_id) e ON A.ID = e.module_id
                    LEFT JOIN ( SELECT idpzh,pzid FROM erp_vcr_item_detail A GROUP BY A.idpzh,A.pzid ) e1 ON A.ID :: TEXT = e1.pzid
                    LEFT JOIN ( SELECT bchno, chk_time, ROW_NUMBER ( ) OVER ( PARTITION BY bchno ORDER BY chk_seq DESC ) AS rn FROM ecs_audit_rcdfm ) H ON H.rn = 1 and H.bchno = A.audit_bchno
                    LEFT JOIN ( SELECT process_instance_id, end_time, ROW_NUMBER ( ) OVER ( PARTITION BY process_instance_id ORDER BY start_time DESC ) AS rn FROM bpm_tsktime_edit ) G ON G.rn = 1 and G.process_instance_id = A.process_instance_id
        WHERE A.type = #{type,jdbcType=VARCHAR}
            and A.busstas = '1'
            and A.appyer is not null
            ) A LEFT JOIN erp_vcr_detail Q on a.idpzh = q.idpzh
        WHERE
            <if test='genFlag =="0"'>
                A.hasPZ =  '0' or A.hasPz is null
            </if>
            <if test='genFlag =="1"'>
                A.hasPZ =  '1'
            </if>
        order by paidTime desc
    </select>

    <select id="queryReimDetailList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT
            A.ID AS ID,
            A.appyer AS appyer,
            A.evection_begn_time AS evectionBegnTime,
            A.evection_end_time AS evectionEndTime,
            A.appyer_time AS appyerTime,
            A.budg_ctrl AS budgCtrl,
            A.evection_addr AS evectionAddr,
            A.evection_rea AS evectionRea,
            a.self_drive_rea as selfDriveRea,
            A.bank AS bank,
            A.acctname AS acctname,
            A.bankcode AS bankcode,
            A."sum" AS "sum",
            A.cap_sum AS capSum,
            A.audit_bchno AS auditBchno,
            A.busstas AS busstas,
            A.crter AS crter,
            A.hospital_id AS hospitalId,
            A."type" AS TYPE,
            A.share_type AS shareType,
            A.share_amt AS shareAmt,
            A.share_date AS shareDate,
            A.att_code AS attCode,
            A.fund_type AS fundType,
            A.bus_met AS busMet,
            A.appyer_dept AS appyerDept,
            A.travel_appr_id AS travelApprId,
            a.project_id as projectId,
            a.funding_id as fundingId,
            a.pay_method          as payMethod,
            a.is_loan            as isLoan,
            a.loan_reim_id       as loanReimId,
            a.loan_amt           as loanAmt,
            a.pay_rcpt_id        as payRcptId,
            a.has_pz             as hasPz,
            A.page_image AS pageImage,
            A.att as att,
            A.att_name as attName,
            A.invo_id as invoId,
            A.opposite_name as oppositeName,
            A.process_instance_id as processInstanceId,
            b.org_name as appyerDeptName,
            C.emp_name as appyerName
        FROM
            ecs_reim_detail A
            LEFT JOIN hrm_org b ON A.appyer_dept = b.org_id
            LEFT JOIN hrm_employee_info C ON A.appyer = C.emp_code
        where A.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryActigCfg" resultType="com.jp.med.erp.modules.vcrGen.entity.ErpActigCfg">
        select
        a.id as id,
        a.sub_code as subCode,
        a.sub_name as subName,
        a.sub_type as subType,
        a.pinyin as pinyin,
        a.remarks as remarks,
        a.asst_info as asstInfo,
        a.actig_elem as actigElem,
        a.actig_sys as actigSys,
        a.balc_dirc as balcDirc,
        a.emp_type as empType,
        a.crter as crter,
        a.create_time as createTime,
        a.modi_time as modiTime,
        a.hospital_id as hospitalId,
        a.active_flag as activeFlag,
        a.parent_code as parentCode
        from ecs_actig_cfg a
        <where>
            a.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
            <if test="sub != null and sub != ''">
                and a.sub_code = #{sub,jdbcType=VARCHAR}
            </if>
            <if test="actigSys != null and actigSys != ''">
                and a.actig_sys = #{actigSys,jdbcType=VARCHAR}
            </if>
            <if test="activeFlag != null and activeFlag != ''">
                and a.active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.id
    </select>

    <select id="queryReimAsstList" resultType="com.jp.med.erp.modules.vcrGen.entity.CertificateDetail">
        select
        a.reim_detail_id as pzid,
        a.pay_type_code as zflxdm,
        a.pay_type_name as zflxmc,
        a.actig_sub_code as kmdm,
        a.actig_sub_name as kmmc,
        case when a.actig_sys = '1' then '01' else '02' end as type,
        a.dept_code as ksdm,
        a.dept_name as ksmc,
        a.rel_co_code as wldwdm,
        a.rel_co_name as wldwmc,
        a.fun_sub_code as gnkmdm,
        a.fun_sub_name as gnkmmc,
        a.econ_sub_code as jjkmdm,
        a.econ_sub_name as jjkmmc,
        a.proj_code as xmdm,
        a.proj_name as xmmc,
        a.cash_flow_code as xjlldm,
        a.cash_flow_name as xjllmc,
        a.fund_type as zjxzdm,
        a.vpzh as vpzh,
        case when a.actig_amt_type = '1' then '借' else '贷' end as jdbz,
        ROUND(a.actig_amt,2) as je,
        SUBSTRING(a.create_time FROM 1 FOR 10) as rq,
        a.abst as zy
        from ecs_reim_asst_detail a
        where
         a.sup_type = #{supType,jdbcType=VARCHAR}
         and a.reim_detail_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryDrugAsstList" resultType="com.jp.med.erp.modules.vcrGen.entity.CertificateDetail">
        select
        a.vpzh as pzid,
        a.pay_type_code as zflxdm,
        a.pay_type_name as zflxmc,
        a.actig_sub_code as kmdm,
        a.actig_sub_name as kmmc,
        case when a.actig_sys = '1' then '01' else '02' end as type,
        a.dept_code as ksdm,
        a.dept_name as ksmc,
        a.rel_co_code as wldwdm,
        a.rel_co_name as wldwmc,
        a.fun_sub_code as gnkmdm,
        a.fun_sub_name as gnkmmc,
        a.econ_sub_code as jjkmdm,
        a.econ_sub_name as jjkmmc,
        a.proj_code as xmdm,
        a.proj_name as xmmc,
        a.cash_flow_code as xjlldm,
        a.cash_flow_name as xjllmc,
        a.fund_type as zjxzdm,
        a.vpzh as vpzh,
        case when a.actig_amt_type = '1' then '借' else '贷' end as jdbz,
        ROUND(a.actig_amt,2) as je,
        SUBSTRING(a.create_time FROM 1 FOR 10) as rq,
        a.abst as zy
        from ecs_reim_asst_detail a,(select b.vpzh
        from erp_vcr_preview b
        where b.sup_type = #{supType,jdbcType=VARCHAR} and b.module_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach> group by b.vpzh) b
        where
        a.vpzh = b.vpzh
    </select>

    <select id="queryReimAsstVoList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo">
        SELECT
            <include refid="reimAsstCommon"/>
        FROM
            ecs_reim_asst_detail A
        WHERE
            A.reim_detail_id = #{reimDetailId,jdbcType=INTEGER}
        and A.sup_type = #{supType,jdbcType=VARCHAR}
    </select>

    <!-- 查询项目信息 -->
    <select id="queryItemDetail" resultType="com.jp.med.erp.modules.vcrGen.entity.ErpReimItemDetail">
        select a.id,
            a.reim_detail_id as reimDetailId,
            a.dept_code as deptCode,
            c.org_name as deptName,
            a.item,
            a.doc_num as docNum,
            a.days_or_kilor as daysOrKilor,
            a.std as std,
            a.amt,
            a.extra_amt as extraAmt,
            a.att,
            a.att_name as attName,
            a.type,
            a.reim_abst as reimAbst,
            a.budget_code as budgetCode,
            a.invo_id as invoId,
            a.emp_code as empCode,
            a.reim_name as reimName,
            a.rel_co,
			d.ins_name as relCoName,
            b.sub_name as subName,
            b.bgt_summary as bgtSummary
            from ecs_reim_item_detail a
            left join ecs_econ_fun_sub_cfg b
            on a.type = b.sub_code and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
            left join hrm_org c
            on a.dept_code = c.org_id
            left join ecs_corrs_ins_cfg d on d."year" = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT and d.ins_code = a.rel_co
        where a.reim_detail_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 查询补助项目信息 -->
    <select id="querySubItemDetail" resultType="com.jp.med.erp.modules.vcrGen.entity.ErpReimSubsItemDetail">
        select a.id,
            a.reim_detail_id as reimDetailId,
            a.item,
            a.days_or_kilor as daysOrKilor,
            a.std,
            a.amt,
            a.att,
            a.att_name as attName,
            a.invo_id as invoId
        from ecs_reim_subs_item_detail a
        where a.reim_detail_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryPsnDetail" resultType="com.jp.med.erp.modules.vcrGen.entity.ErpReimPsnDetail">
        SELECT
            A.ID,
            A.reim_detail_id,
            A.dept,
            b.org_name AS deptName,
            A.trip_psn,
            c.emp_name as tripPsnName,
            A.reim_amt,
            A."type"
        FROM
        ecs_reim_psn_detail A
        LEFT JOIN hrm_org b
        ON A.dept = b.org_id
        left join hrm_employee_info c
        on a.trip_psn = c.emp_code
        WHERE
        A."type" = '2'
        AND A.reim_detail_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryVcrApplyMsg" resultType="com.jp.med.erp.modules.vcrGen.entity.Certificate">
        SELECT
            id,
            organ_code as organCode,
            accounting_supervisor as accountingSupervisor,
            certificate_date as certificateDate,
            record_person_id as recordPersonId ,
            record_person_name as recordPersonName,
            attach_amount as attachAmount,
            certificate_abs as certificateAbs,
            create_time as createTime,
            organ_name as organName,
            idpzh
        FROM
            erp_vcr_apply
        WHERE
            idpzh = #{idpzh,jdbcType=VARCHAR}
    </select>

    <sql id="reimAsstCommon">
            A.ID,
            A.reim_detail_id as reimDetailId,
            A.pay_type_code as payTypeCode,
            A.pay_type_name as payTypeName,
            A.actig_sub_code as actigSubCode,
            A.actig_sub_name as actigSubName,
            A.actig_sys as actigSys,
            A.dept_code as deptCode,
            A.dept_name as deptName,
            A.rel_co_code as relCoCode,
            A.rel_co_name as relCoName,
            A.fun_sub_code as funSubCode,
            A.fun_sub_name as funSubName,
            A.econ_sub_code as econSubCode,
            A.econ_sub_name as econSubName,
            A.proj_code as projCode,
            A.proj_name as projName,
            A.cash_flow_code as cashFlowCode,
            A.cash_flow_name as cashFlowName,
            A.actig_amt_type as actigAmtType,
            A.actig_amt as actigAmt,
            A.crter,
            A.create_time as createTime,
            A.hospital_id as hospitalId,
            A.reim_dept_code as reimDeptCode,
            A.reim_dept_name as reimDeptName,
            A.asst_no as asstNo,
            A.abst,
            A.sup_type as supType,
            A.fund_type as fundType,
            A.fund_type_name as fundTypeName,
            A.vpzh as vpzh
    </sql>

    <select id="queryVcrAsst" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo">
        SELECT
            <include refid="reimAsstCommon"/>
        FROM
            ecs_reim_asst_detail A,
            ( SELECT pzid FROM erp_vcr_item_detail A WHERE A.idpzh = #{idpzh,jdbcType=VARCHAR} GROUP BY pzid ) b
        WHERE
            A.reim_detail_id :: TEXT = b.pzid
            AND A.sup_type = #{supType,jdbcType=VARCHAR}
            AND A.actig_sub_code IS NOT NULL
    </select>

    <select id="queryVcrAsstByvpzh" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo">
        SELECT
            <include refid="reimAsstCommon"/>
        FROM
            ecs_reim_asst_detail A,
            ( SELECT pzid FROM erp_vcr_item_detail A WHERE A.idpzh = #{idpzh,jdbcType=VARCHAR} GROUP BY pzid ) b
        WHERE
            A.vpzh = b.pzid
            AND A.sup_type = #{supType,jdbcType=VARCHAR}
            AND A.actig_sub_code IS NOT NULL
    </select>

    <select id="queryAsst" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo">
        SELECT
            <include refid="reimAsstCommon"/>
        FROM
            ecs_reim_asst_detail A
        WHERE
            A.sup_type = #{supType,jdbcType=VARCHAR}
            AND A.reim_detail_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
        ORDER BY A.asst_no
    </select>

    <select id="queryAsstByPreviewVcr" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo">
        SELECT <include refid="reimAsstCommon"/>
        FROM
        ecs_reim_asst_detail A,
        (
        SELECT
            B.vpzh
        FROM
            erp_vcr_preview B
        WHERE
            B.sup_type = #{supType,jdbcType=VARCHAR}
        AND B.module_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
        GROUP BY
            b.vpzh
        ) B
        WHERE
            B.vpzh = A.vpzh
    </select>

    <select id="queryShareDetails" resultType="com.jp.med.erp.modules.vcrGen.entity.ErpReimShareEntity">
        SELECT
            A.ID,
            A.reim_detail_id,
            A.share_type,
            A.dept_code,
            A.dept_name,
            A.amt,
            A."abs"
        FROM
            ecs_reim_share_detail A
        WHERE
            A.reim_detail_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryFileRecord" resultType="com.jp.med.erp.modules.vcrGen.entity.FileRecordEntity">
        SELECT A.ID AS ID,
               A.att AS att,
               A.att_name AS attName,
               A.TYPE AS TYPE,
               A.att_code AS attCode,
               A.FLAG AS FLAG
        FROM
            ecs_reim_file_record A,
            ecs_reim_detail b
        WHERE
            b.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
          AND b.att_code = A.att_code
          AND FLAG = '0'
    </select>

    <select id="queryFileRecordByCode" resultType="com.jp.med.erp.modules.vcrGen.entity.FileRecordEntity">
        SELECT A.ID AS ID,
               A.att AS att,
               A.att_name AS attName,
               A.TYPE AS TYPE,
               A.att_code AS attCode,
               A.FLAG AS FLAG
        FROM
            ecs_reim_file_record A
        WHERE
            A.FLAG = '0'
            AND A.att_code IN
        <foreach collection="attCodes" item="code" separator="," open="(" close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>

    </select>

    <select id="queryApprInfo" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimTravelApprVo">
        SELECT A.ID AS ID,
               A.appyer_time AS appyerTime,
               A.appyer AS appyer,
               A.evection_rea AS evectionRea,
               A.evection_begn_time AS evectionBegnTime,
               A.evection_end_time AS evectionEndTime,
               A.evection_addr AS evectionAddr,
               A.evection_detl_addr AS evectionDetlAddr,
               A.detour_or_not AS detourOrNot,
               A.kil AS kil,
               A.trnp AS trnp,
               A.trnp_num AS trnpNum,
               A.food AS food,
               A.stay AS stay,
               A.prse AS prse,
               A.plan_amt AS planAmt,
               A.plan_amt2 AS planAmt2,
               A.plan_amt3 as planAmt3,
               A.hospital_id AS hospitalId,
               A."type" AS TYPE,
               A.att AS att,
               A.att_name AS attName,
               A.audit_bchno AS auditBchno,
               A.page_image AS pageImage,
               A.reim_flag AS reimFlag,
               A.bus_met AS busMet,
               A.appr_dept_type AS apprDeptType,
               A.travel_range as travelRange,
               A.status as status,
                a.self_drive_rea as selfDriveRea,
               A.process_instance_id as processInstanceId,
               A.chker_flow AS chkerFlow,
               C.emp_name AS appyerName
        FROM
            ecs_reim_travel_appr A LEFT JOIN hrm_employee_info C ON A.appyer = C.emp_code
        WHERE A.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryReimInfo" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT A
                   .ID AS ID,
               A.appyer AS appyer,
               A.evection_begn_time AS evectionBegnTime,
               A.evection_end_time AS evectionEndTime,
               A.appyer_time AS appyerTime,
               A.budg_ctrl AS budgCtrl,
               A.evection_addr AS evectionAddr,
               A.evection_rea AS evectionRea,
                a.self_drive_rea as selfDriveRea,
               A.bank AS bank,
               A.acctname AS acctname,
               A.bankcode AS bankcode,
               A."sum" AS "sum",
               A.cap_sum AS capSum,
               A.audit_bchno AS auditBchno,
               A.busstas AS busstas,
               A.crter AS crter,
               A.hospital_id AS hospitalId,
               A."type" AS TYPE,
               A.share_type AS shareType,
               A.bus_met AS busMet,
               A.chker_flow AS chkerFlow,
               A.share_amt AS shareAmt,
               A.share_date AS shareDate,
               A.att_code AS attCode,
               A.fund_type AS fundType,
               A.appyer_dept AS appyerDept,
               A.travel_appr_id AS travelApprId,
                a.project_id as projectId,
                a.funding_id as fundingId,
                a.pay_method          as payMethod,
                a.is_loan            as isLoan,
                a.loan_reim_id       as loanReimId,
                a.loan_amt           as loanAmt,
                a.pay_rcpt_id        as payRcptId,
                a.has_pz             as hasPz,
               A.page_image AS pageImage,
               A.att as att,
               A.att_name as attName,
               A.invo_id as invoId,
               A.opposite_name AS oppositeName,
               A.process_instance_id as processInstanceId,
                case
                    when a.audit_bchno is not null then H.chk_time
                    else TO_CHAR(G.end_time, 'yyyy-MM-dd HH24:MI:SS')
                END AS paidTime,
               b.org_name AS appyerDeptName,
               C.emp_name AS appyerName
        FROM
            ecs_reim_detail
                A LEFT JOIN hrm_org b ON A.appyer_dept = b.org_id
                  LEFT JOIN hrm_employee_info C ON A.appyer = C.emp_code
                LEFT JOIN ( SELECT bchno, chk_time, ROW_NUMBER ( ) OVER ( PARTITION BY bchno ORDER BY chk_seq DESC ) AS rn FROM ecs_audit_rcdfm ) H ON H.rn = 1 and H.bchno = A.audit_bchno
                LEFT JOIN ( SELECT process_instance_id, end_time, ROW_NUMBER ( ) OVER ( PARTITION BY process_instance_id ORDER BY start_time DESC ) AS rn FROM bpm_tsktime_edit ) G ON G.rn = 1 and G.process_instance_id = A.process_instance_id
        WHERE A.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryReimRelCoDetails" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimRelCoDetail">
        SELECT A.ID,
            A.reim_detail_id,
            A.rel_co_code,
            A.rel_co_name,
            A.amt
        FROM
        "ecs_reim_rel_co_detail" A
        WHERE
        A.reim_detail_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="drugToVcrList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT
        *
        FROM
        (
        SELECT A
        .ID,
        A.spler,
        A.SUM,
        A.cap_sum,
        A.pay_istr,
        A.audit_bchno,
        A.crter,
        A.craete_time,
        A.hospital_id,
        A.issue,
        A.parent_audit_bchno,
        A.status,
        A.drug_pay_id,
        A.drug_pay_type,
        A.pay_method,
        A.pay_istr2 as payIstr2,
        a.process_instance_id as processInstanceId,
        A.craete_time as crateTime,
        d.att,
        d.attName,
        e.emp_name AS crterName,
        CASE WHEN b.idpzh IS NULL THEN
        '0' ELSE'1'
        END AS genFlag,
        M.pzh,
        M.idpzh,
        M.kjqj
        FROM
        ecs_drug_reim_detai
        A LEFT JOIN (
        SELECT A
        .vpzh,
        A.module_id,
        b.idpzh
        FROM
        erp_vcr_preview A,
        erp_vcr_item_detail b
        WHERE
        A.vpzh = b.pzid
        AND A.sup_type = '2'
        AND A.pay_type = '1'
        GROUP BY
        b.idpzh,
        A.vpzh,
        A.module_id
        ) b ON A.ID = b.module_id
        LEFT JOIN erp_vcr_detail M ON M.idpzh = b.idpzh
        LEFT JOIN (
        SELECT
        A.ID,
        STRING_AGG ( b.att, ',' ) AS att,
        STRING_AGG ( b.att_name, ',' ) AS attName
        FROM
        ecs_drug_pay_detail A,
        ecs_reim_file_record B
        WHERE
        A.att_code = B.att_code
        GROUP BY A.ID
        ) D ON A.drug_pay_id = D.ID
        LEFT JOIN hrm_employee_info e ON A.crter = e.emp_code
        <!-- 集采报销不生成凭证 -->
        WHERE A.drug_pay_type = '1'
        ) A
        WHERE
        A.status = '5'
        AND A.genFlag = #{genFlag,jdbcType=VARCHAR}
    </select>

    <select id="salaryToVcrList" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimSalaryTaskVo">
        SELECT
        *
        FROM
        (
        SELECT
        A.ID,
        A.ff_mth,
        A.num,
        A.should_pay,
        A.reduce_pay,
        A.real_pay,
        A.remark,
        A.crter,
        A.crte_time,
        A.reim_flag,
        A.salary_id,
        A.reim_id,
        A."type" AS salaryType,
        e.emp_name AS crterName,
        CASE
        WHEN C.reim_detail_id IS NULL THEN '0'
        ELSE '1'
        END AS hasAssts,
        CASE
        WHEN b.pzid IS NULL THEN '0'
        ELSE '1'
        END AS genFlag,
        m.pzh,
        m.idpzh,
        m.kjqj
        FROM
        ecs_reim_salary_task A
        LEFT JOIN (
        SELECT
        A.idpzh,
        b.pzid
        FROM
        erp_vcr_apply A
        LEFT JOIN (
        SELECT
        idpzh,
        pzid
        FROM
        erp_vcr_item_detail A
        GROUP BY
        idpzh,
        pzid
        ) b ON A.idpzh = b.idpzh
        WHERE
        A.sup_type = #{supType,jdbcType=VARCHAR}
        AND A."type" = '5'
        ) b ON A.ID :: TEXT = b.pzid
        LEFT JOIN erp_vcr_detail m on m.idpzh = b.idpzh
        LEFT JOIN (
        SELECT
        A.reim_detail_id
        FROM
        ecs_reim_asst_detail A
        WHERE
        A.sup_type = #{supType,jdbcType=VARCHAR}
        GROUP BY
        A.reim_detail_id
        ) C ON C.reim_detail_id = A."id"
        LEFT JOIN hrm_employee_info e ON A.crter = e.emp_code
        ) A
        WHERE
        A.reim_flag = '1'
        AND A.genFlag = #{genFlag,jdbcType=VARCHAR}
    </select>

    <select id="deprToVcrList" resultType="com.jp.med.common.vo.EcsReimDeprTaskVo">
        SELECT
            *
        FROM
            (
                SELECT A.ID,
                       A.launch_date,
                       A.launch_dept,
                       A.crter,
                       A.create_time,
                       e.emp_name AS crterName,
                       A.reim_flag,
                       CASE WHEN C.reim_detail_id IS NULL THEN
                               '0' ELSE'1'
                           END AS hasAssts,
                       CASE WHEN b.pzid IS NULL THEN
                               '0' ELSE'1'
                           END AS genFlag,
                       A.amt as amt,
                       A.att_code as attCode,
                       f.org_name as launchDeptName,
                       m.pzh,
                       m.idpzh,
                       m.kjqj
                FROM
                    ecs_reim_depr_task
                        A LEFT JOIN (
                        SELECT A.idpzh,
                               b.pzid
                        FROM
                            erp_vcr_apply
                                A LEFT JOIN ( SELECT idpzh, pzid FROM erp_vcr_item_detail A GROUP BY idpzh, pzid ) b ON A.idpzh = b.idpzh
                        WHERE
                            A.sup_type = #{supType,jdbcType=VARCHAR}
                          AND A."type" = '7'
                    ) b ON A.ID :: TEXT = b.pzid
                    LEFT JOIN erp_vcr_detail M ON M.idpzh = b.idpzh
                          LEFT JOIN ( SELECT A.reim_detail_id FROM ecs_reim_asst_detail A WHERE A.sup_type = #{supType,jdbcType=VARCHAR} GROUP BY A.reim_detail_id ) C ON C.reim_detail_id = A."id"
                          LEFT JOIN hrm_employee_info e ON A.crter = e.emp_code
                    left join hrm_org f on f.org_id = a.launch_dept
            ) A
        WHERE
            A.genFlag = #{genFlag,jdbcType=VARCHAR}
    </select>

    <select id="queryDrugReimDetailVo" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpDrugReimDetailVo">
        SELECT A.ID,
                A.spler,
                A."sum",
                A.cap_sum,
                A.pay_istr,
                A.audit_bchno,
                A.crter,
                A.craete_time,
                A.hospital_id,
                A.issue,
                A.parent_audit_bchno,
                A.status,
                A.drug_pay_id,
                A.drug_pay_type,
                A.pay_method,
                A.pay_istr2 as payIstr2,
                a.process_instance_id as processInstanceId,
                b.att_code
        FROM
            ecs_drug_reim_detai A
        LEFT JOIN ecs_drug_pay_detail b on a.drug_pay_id = b."id"
        WHERE
        A.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryPurcReimDetailVo" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT
            A.ID AS ID,
            A.appyer AS appyer,
            A.evection_begn_time AS evectionBegnTime,
            A.evection_end_time AS evectionEndTime,
            A.appyer_time AS appyerTime,
            A.budg_ctrl AS budgCtrl,
            A.evection_addr AS evectionAddr,
            A.evection_rea AS evectionRea,
            a.self_drive_rea as selfDriveRea,
            A.bank AS bank,
            A.acctname AS acctname,
            A.bankcode AS bankcode,
            A."sum" AS "sum",
            A.cap_sum AS capSum,
            A.audit_bchno AS auditBchno,
            A.busstas AS busstas,
            A.crter AS crter,
            A.hospital_id AS hospitalId,
            A."type" AS TYPE,
            A.share_type AS shareType,
            A.share_amt AS shareAmt,
            A.share_date AS shareDate,
            A.att_code AS attCode,
            A.fund_type AS fundType,
            A.bus_met AS busMet,
            A.appyer_dept AS appyerDept,
            A.travel_appr_id AS travelApprId,
            a.project_id as projectId,
            a.funding_id as fundingId,
            a.pay_method          as payMethod,
            a.is_loan            as isLoan,
            a.loan_reim_id       as loanReimId,
            a.loan_amt           as loanAmt,
            a.pay_rcpt_id        as payRcptId,
            a.has_pz             as hasPz,
            A.page_image AS pageImage,
            A.att as att,
            A.att_name as attName,
            A.invo_id as invoId,
            A.opposite_name as oppositeName,
            A.process_instance_id as processInstanceId,
            b.att_code as attCode2
        FROM
            ecs_reim_detail A LEFT JOIN ecs_drug_pay_detail b on a.pay_rcpt_id = b.id
        where a.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>



    <select id="queryDrugReimDetailVoByIdPzh" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpDrugReimDetailVo">
        SELECT A.ID,
               A.spler,
               A."sum",
               A.cap_sum,
               A.pay_istr,
               A.audit_bchno,
               A.crter,
               A.craete_time,
               A.hospital_id,
               A.issue,
               A.parent_audit_bchno,
               A.status,
               A.drug_pay_id,
               A.pay_istr2 as payIstr2,
               A.drug_pay_type as drugPayType,
                a.process_instance_id as processInstanceId,
               A.pay_method as payMethod,
               a.att_code
        FROM
            (SELECT A.*,B.att_code FROM ecs_drug_reim_detai A LEFT JOIN ecs_drug_pay_detail b on a.drug_pay_id = b.id) A,
            (select idpzh,pzid from erp_vcr_item_detail c where c.idpzh = #{idpzh,jdbcType=VARCHAR} group by idpzh,pzid) c,
            erp_vcr_preview B
        WHERE
            C.pzid = B.vpzh
          AND B.module_id = a.id
    </select>

    <select id="queryReimDetailVoByIdPzh" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT A.*
        FROM
            ( SELECT A.*, b.att_code AS attCode2 FROM ecs_reim_detail A LEFT JOIN ecs_drug_pay_detail b ON A.pay_rcpt_id = b."id" ) A,
            ( SELECT idpzh, pzid FROM erp_vcr_item_detail C WHERE C.idpzh = #{idpzh,jdbcType=VARCHAR} GROUP BY idpzh, pzid ) C,
            erp_vcr_preview b
        WHERE
            C.pzid = b.vpzh
          AND b.module_id = A.ID
    </select>

    <select id="queryPayReceiptVoByDrugPayDetailIds" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo">

    </select>

    <select id="queryDrugStoinVoByReimIds" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpStoinVo">
        SELECT A.ID AS ID,
               A.stoin_num AS stoinNum,
               SUBSTRING ( A.stoin_date, 1, 10 ) AS stoinDate,
               A.totlcnt,
               A.rtal_amt AS rtalAmt,
               A.purcpric_amt AS purcpricAmt,
               A.invono AS invono,
               A.spler AS spler,
               A.reim_flag AS reimFlag,
               A.sync_date AS syncDate,
               A.hospital_id AS hospitalId,
               A.drug_reim_detail_id,
               A.att,
               A.att_name AS attName,
               A.invo_id AS invoId,
               A.xh AS xh,
               A.stoin_type AS stoinType,
               A.is_back AS isBack
        FROM
            ecs_stoin A
        WHERE A.drug_reim_detail_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryPropertyDeprList" resultType="com.jp.med.common.vo.ErpPropertyVo">
        <!--SELECT
            o.openingDate as openingDate,
            o.dept as dept,
            o.asset_type_code as assetType,
            o.asset_type_name as assetTypeName,
            o.source,
            o.deptName,
            SUM ( o.dep )
        FROM
            (
                SELECT A.dept,
                       b.asset_type_code,
                       b.asset_type_name,
                       A.SOURCE,
                       e.org_name AS deptName,
                       SUBSTRING ( A.opening_date, 1, 4 ) AS openingDate,
                       A.dep
                FROM
                    ams_property
                        A LEFT JOIN ams_type_cfg b ON A.asset_type = b.asset_type_code
                          LEFT JOIN hrm_org e ON A.dept = e.org_id
                WHERE
                    A.is_chk = '1'
                  AND A.is_canc = '0'
                  AND A.TYPE = '1'
            ) o
        GROUP BY
            o.openingDate,
            o.dept,
            o.asset_type_code,
            o.asset_type_name,
            o.SOURCE,
            o.deptName
        ORDER BY
            o.openingDate ASC-->
        SELECT
        o.dept as dept,
        o.openingDate as openingDate,
        o.asset_type_code as assetType,
        o.asset_type_name as assetTypeName,
        o.SOURCE,
        o.deptName,
        SUM ( o.dep ) as dep
        FROM
        (
        SELECT
        A.dept,
        b.asset_type_code,
        b.asset_type_name,
        A.SOURCE,
        e.org_name AS deptName,
        case when SUBSTRING (A.opening_date, 1, 4 ) = (EXTRACT(YEAR FROM CURRENT_DATE)-1):: TEXT THEN '当前年度'
        ELSE '以前年度'
        END AS openingDate,
        A.dep
        FROM
        ams_property
        A LEFT JOIN ams_typen_cfg b ON A.asset_type_n = b.asset_type_code
        LEFT JOIN hrm_org e ON A.dept = e.org_id
        WHERE
        A.is_chk = '1'
        AND A.is_canc = '0'
        AND A.TYPE = '1'
        AND A.opening_date is not null
        AND A.asset_type_n is not null
        AND A.dep is not null
        ) o
        GROUP BY
            o.openingDate,
            o.dept,
            o.asset_type_code,
            o.asset_type_name,
            o.SOURCE,
            o.deptName
        ORDER BY
        o.dept,
        o.openingDate ASC
    </select>

    <select id="queryReimDetailWithSalaryId" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT
            A.ID AS ID,
            A.appyer AS appyer,
            A.evection_begn_time AS evectionBegnTime,
            A.evection_end_time AS evectionEndTime,
            A.appyer_time AS appyerTime,
            A.budg_ctrl AS budgCtrl,
            A.evection_addr AS evectionAddr,
            A.evection_rea AS evectionRea,
            a.self_drive_rea as selfDriveRea,
            A.bank AS bank,
            A.acctname AS acctname,
            A.bankcode AS bankcode,
            A."sum" AS "sum",
            A.cap_sum AS capSum,
            A.audit_bchno AS auditBchno,
            A.busstas AS busstas,
            A.crter AS crter,
            A.hospital_id AS hospitalId,
            A."type" AS TYPE,
            A.share_type AS shareType,
            A.share_amt AS shareAmt,
            A.share_date AS shareDate,
            A.att_code AS attCode,
            A.fund_type AS fundType,
            A.bus_met AS busMet,
            A.appyer_dept AS appyerDept,
            A.travel_appr_id AS travelApprId,
            a.project_id as projectId,
            a.funding_id as fundingId,
            a.pay_method          as payMethod,
            a.is_loan            as isLoan,
            a.loan_reim_id       as loanReimId,
            a.loan_amt           as loanAmt,
            a.pay_rcpt_id        as payRcptId,
            a.has_pz             as hasPz,
            A.page_image AS pageImage,
            A.att as att,
            A.att_name as attName,
            A.invo_id as invoId,
            A.opposite_name as oppositeName,
            A.process_instance_id as processInstanceId,
            b.org_name as appyerDeptName,
            C.emp_name as appyerName
        FROM
            ecs_reim_detail A
                LEFT JOIN hrm_org b ON A.appyer_dept = b.org_id
                LEFT JOIN hrm_employee_info C ON A.appyer = C.emp_code
        where A.type = '5'
            AND A.travel_appr_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryToConfigSalary" resultType="com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo">
        SELECT M.empCode,M.empName FROM (
                                            SELECT
                                                A.emp_code AS empCode,
                                                A.emp_name as empName,
                                                b.emp_code as empCode2
                                            FROM
                                                (
                                                    SELECT
                                                        b.emp_code,
                                                        b.emp_name
                                                    FROM
                                                        ecs_reim_salary_task_detail A,
                                                        hrm_employee_info b
                                                    WHERE
                                                        A.task_id = #{id,jdbcType=INTEGER}
                                                      AND SUBSTRING ( A.reim_name, 1, 21 ) = 'temporaryReduceSalary'
                                                      AND A.emp_code = b.emp_code
                                                )
                                                    A LEFT JOIN erp_vcr_salary_config b ON A.emp_code = b.emp_code ) M
        WHERE m.empCode2 IS NULL
    </select>

    <select id="queryDrugPayDetail" resultType="com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo">
        SELECT
            *
        FROM
            ecs_drug_pay_detail A
        WHERE
            A.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryReimDetailByPayRcptId" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo">
        SELECT
            A.ID AS ID,
            A.appyer AS appyer,
            A.evection_begn_time AS evectionBegnTime,
            A.evection_end_time AS evectionEndTime,
            A.appyer_time AS appyerTime,
            A.budg_ctrl AS budgCtrl,
            A.evection_addr AS evectionAddr,
            A.evection_rea AS evectionRea,
            a.self_drive_rea as selfDriveRea,
            A.bank AS bank,
            A.acctname AS acctname,
            A.bankcode AS bankcode,
            A."sum" AS "sum",
            A.cap_sum AS capSum,
            A.audit_bchno AS auditBchno,
            A.busstas AS busstas,
            A.crter AS crter,
            A.hospital_id AS hospitalId,
            A."type" AS TYPE,
            A.share_type AS shareType,
            A.share_amt AS shareAmt,
            A.share_date AS shareDate,
            A.att_code AS attCode,
            A.fund_type AS fundType,
            A.bus_met AS busMet,
            A.appyer_dept AS appyerDept,
            A.travel_appr_id AS travelApprId,
            a.project_id as projectId,
            a.funding_id as fundingId,
            a.pay_method          as payMethod,
            a.is_loan            as isLoan,
            a.loan_reim_id       as loanReimId,
            a.loan_amt           as loanAmt,
            a.pay_rcpt_id        as payRcptId,
            a.has_pz             as hasPz,
            A.page_image AS pageImage,
            A.att as att,
            A.att_name as attName,
            A.invo_id as invoId,
            A.opposite_name as oppositeName,
            A.process_instance_id as processInstanceId
        FROM
            ecs_reim_detail A
        WHERE A.pay_rcpt_id in
        <foreach collection="payRcptIds" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryVcrPreview" resultType="com.jp.med.erp.modules.vcrGen.vo.ErpVcrPreviewVo">
        SELECT A
                   .*
        FROM
            erp_vcr_preview A,
            ( SELECT A.idpzh, A.pzid FROM erp_vcr_item_detail A WHERE A.idpzh = #{idpzh,jdbcType=VARCHAR} GROUP BY idpzh, pzid ) b
        WHERE
            b.pzid = A.vpzh
    </select>
</mapper>
