package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 房屋维修资产处理器
 * 负责处理房屋维修资产的折旧计算和科室分摊
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyHouseRepairProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

    @Override
    protected void doProcess(AmsPropertyDeprProcessContext context) {
        log.debug("开始处理房屋维修资产");

        // 处理房屋维修资产
        processHouseRepairAssets(
                context.getGroupedHouseRepairAssets(),
                context.getResult(),
                context.getUsedFacodeSet(),
                context.getDeprRateMap(),
                context.getDeptCodes(),
                context.getLastDayOfMonth(),
                context.getHouseRepairAssets(),
                context.isUseNewType());

        log.debug("房屋维修资产处理完成");
    }

    /**
     * 处理房屋维修资产
     */
    private void processHouseRepairAssets(
            Map<String, Map<String, Map<String, List<AmsPropertyVo>>>> groupedAssets,
            List<AmsPropertyDepr2Vo> result,
            java.util.Set<String> usedFacodeSet,
            Map<String, BigDecimal> deprRateMap,
            java.util.Set<String> deptCodes,
            java.time.LocalDate lastDayOfMonth,
            List<AmsPropertyVo> houseRepairAssets,
            Boolean useNewType) {

        // 临时处理

        for (Map.Entry<String, Map<String, Map<String, List<AmsPropertyVo>>>> deptEntry : groupedAssets.entrySet()) {
            for (Map.Entry<String, Map<String, List<AmsPropertyVo>>> typeEntry : deptEntry.getValue().entrySet()) {
                for (Map.Entry<String, List<AmsPropertyVo>> sourceEntry : typeEntry.getValue().entrySet()) {
                    List<AmsPropertyVo> assets = sourceEntry.getValue();

                    // 计算总原值，用于误差控制
                    BigDecimal totalOriginalValue = calculateTotalOriginalValue(assets);
                    BigDecimal remainingOriginalValue = totalOriginalValue;

                    // 收集资产的月折旧和累计折旧，用于误差控制
                    BigDecimal totalAssetsDeprMon = assets.stream()
                            .map(AmsPropertyVo::getDeprMon)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalAssetsDep = assets.stream()
                            .map(AmsPropertyVo::getDep)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal remainingDeprMon = totalAssetsDeprMon;
                    BigDecimal remainingDep = totalAssetsDep;

                    // 获取科室列表并转换为列表，以便处理最后一个科室
                    List<String> deptCodesList = new ArrayList<>(deptCodes);

                    for (int i = 0; i < deptCodesList.size(); i++) {
                        String deptCode = deptCodesList.get(i);
                        BigDecimal rate = deprRateMap.getOrDefault(deptCode, BigDecimal.ZERO);
                        if (rate.compareTo(BigDecimal.ZERO) == 0) {
                            log.info("未找到科室{}的折旧率", deptCode);
                        } else {
                            boolean isLast = (i == deptCodesList.size() - 1);
                            // 临时处理

                            String deptName = result.stream()
                                    .filter(item -> item.getDeptUseCode().equals(deptCode))
                                    .findFirst()
                                    .map(AmsPropertyDepr2Vo::getDeptUseName) // 安全获取属性
                                    .orElse("默认部门名称"); // 提供默认值避免异常Ï
                            AmsPropertyDepr2Vo vo = createHouseRepairDeprVo(
                                    deptCode, deptName,
                                    typeEntry.getKey(),
                                    sourceEntry.getKey(),
                                    assets,
                                    rate,
                                    usedFacodeSet,
                                    lastDayOfMonth,
                                    houseRepairAssets,
                                    useNewType,
                                    isLast,
                                    remainingOriginalValue,
                                    remainingDeprMon,
                                    remainingDep);

                            vo.setIsAmortizedAssets(true);
                            result.add(vo);

                            // 更新剩余值
                            if (!isLast) {
                                remainingOriginalValue = remainingOriginalValue.subtract(vo.getMonthDeprAmt());
                                if (vo.getDeprAmt() != null) {
                                    remainingDeprMon = remainingDeprMon.subtract(vo.getDeprAmt());
                                }
                                if (vo.getDeprAmtSum() != null) {
                                    remainingDep = remainingDep.subtract(vo.getDeprAmtSum());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 计算原值总和
     */
    private BigDecimal calculateTotalOriginalValue(List<AmsPropertyVo> assets) {
        return assets.stream()
                .map(AmsPropertyVo::getAssetNav)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 创建房屋维修折旧VO对象
     */
    private AmsPropertyDepr2Vo createHouseRepairDeprVo(
            String deptCode,
            String deptName,
            String assetTypeCode,
            String sourceCode,
            List<AmsPropertyVo> assets,
            BigDecimal rate,
            java.util.Set<String> usedFacodeSet,
            java.time.LocalDate lastDayOfMonth,
            List<AmsPropertyVo> allAssets,
            Boolean useNewType,
            boolean isLast,
            BigDecimal remainingOriginalValue,
            BigDecimal remainingDeprMon,
            BigDecimal remainingDep) {

        AmsPropertyDepr2Vo vo = new AmsPropertyDepr2Vo();

        // 设置基础信息
        vo.setFaCodes(assets.stream()
                .map(AmsPropertyVo::getFaCode)
                .peek(usedFacodeSet::add)
                .collect(Collectors.joining(",")));
        vo.setDeptUseCode(deptCode);
        vo.setDeptUseName(deptName);
        vo.setAssetTypeCode(assetTypeCode);
        vo.setSourceCode(sourceCode);
        vo.setIsSummary(false);

        // 设置名称
        allAssets.stream()
                .filter(item -> item.getDeptUse().equals(deptCode))
                .findFirst()
                .ifPresent(item -> vo.setDeptUseName(item.getDeptUseName()));

        allAssets.stream()
                .filter(item -> useNewType.equals(Boolean.TRUE) ? item.getAssetTypeN().equals(assetTypeCode)
                        : item.getAssetType().equals(assetTypeCode))
                .findFirst()
                .ifPresent(item -> {
                    if (useNewType) {
                        vo.setAssetTypeName(item.getAssetTypeNName());
                    } else {
                        vo.setAssetTypeName(item.getAssetTypeName());
                    }
                });

        // 如果是最后一个项目，直接使用剩余值，否则计算
        if (isLast) {
            // 使用剩余值
            vo.setMonthDeprAmt(remainingOriginalValue);
            vo.setDeprAmt(remainingDeprMon);
            vo.setDeprAmtSum(remainingDep);

            // 计算加权平均折旧率，使用原始计算方式保持逻辑一致性
            BigDecimal weightedDeprRate = BigDecimal.ZERO;
            BigDecimal totalWeight = BigDecimal.ZERO;

            for (AmsPropertyVo asset : assets) {
                if (asset.getUl() == null || asset.getUl().compareTo(BigDecimal.ZERO) <= 0
                        || asset.getAssetNav() == null
                        || asset.getAssetNav().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                BigDecimal assetNav = asset.getAssetNav().multiply(rate);
                BigDecimal monthlyDeprRate = asset.getDeprratMon();
                if (monthlyDeprRate == null) {
                    monthlyDeprRate = BigDecimal.ONE.divide(
                            asset.getUl().multiply(BigDecimal.valueOf(12)),
                            4,
                            RoundingMode.HALF_UP);
                }

                weightedDeprRate = weightedDeprRate.add(monthlyDeprRate.multiply(assetNav));
                totalWeight = totalWeight.add(assetNav);
            }

            if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
                vo.setDeprRate(weightedDeprRate.divide(totalWeight, 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)));
            } else {
                vo.setDeprRate(BigDecimal.ZERO);
            }
        } else {
            // 计算本月原值总额
            BigDecimal totalOriginalValue = calculateTotalOriginalValue(assets);

            // 应用分摊率并四舍五入
            totalOriginalValue = totalOriginalValue.multiply(rate).setScale(2, RoundingMode.HALF_UP);
            vo.setMonthDeprAmt(totalOriginalValue);

            // 计算月折旧额和累计折旧额
            BigDecimal totalMonthlyDeprAmount = BigDecimal.ZERO;
            BigDecimal totalDeprAmtSum = BigDecimal.ZERO;
            BigDecimal weightedDeprRate = BigDecimal.ZERO;
            BigDecimal totalWeight = BigDecimal.ZERO;

            // 对每个资产分别计算
            for (AmsPropertyVo asset : assets) {
                if (asset.getUl() == null || asset.getUl().compareTo(BigDecimal.ZERO) <= 0
                        || asset.getAssetNav() == null
                        || asset.getAssetNav().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // 应用分摊率到资产原值
                BigDecimal assetNav = asset.getAssetNav().multiply(rate);
                BigDecimal monthlyDeprRate = asset.getDeprratMon();
                if (monthlyDeprRate == null) {
                    monthlyDeprRate = BigDecimal.ONE.divide(
                            asset.getUl().multiply(BigDecimal.valueOf(12)),
                            4,
                            RoundingMode.HALF_UP);
                }

                if (asset.getDeprMon() != null) {
                    totalMonthlyDeprAmount = totalMonthlyDeprAmount.add(asset.getDeprMon().multiply(rate));
                }

                if (asset.getDep() != null) {
                    totalDeprAmtSum = totalDeprAmtSum.add(asset.getDep().multiply(rate));
                }

                // 计算加权平均折旧率
                weightedDeprRate = weightedDeprRate.add(monthlyDeprRate.multiply(assetNav));
                totalWeight = totalWeight.add(assetNav);
            }

            // 设置月折旧额 - 四舍五入为2位小数
            vo.setDeprAmt(totalMonthlyDeprAmount.setScale(2, RoundingMode.HALF_UP));

            // 设置累计折旧额 - 四舍五入为2位小数
            vo.setDeprAmtSum(totalDeprAmtSum.setScale(2, RoundingMode.HALF_UP));

            // 设置加权平均折旧率（百分比）
            if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
                vo.setDeprRate(weightedDeprRate.divide(totalWeight, 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)));
            } else {
                vo.setDeprRate(BigDecimal.ZERO);
            }
        }

        return vo;
    }
}
