package com.jp.med.ams.modules.inventory.controller;

import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrDetailReadService;
import com.jp.med.ams.modules.inventory.service.write.AmsIntrDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 盘点数据明细表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Api(value = "盘点数据明细表", tags = "盘点数据明细表")
@RestController
@RequestMapping("amsIntrDetail")
public class AmsIntrDetailController {

    @Autowired
    private AmsIntrDetailReadService amsIntrDetailReadService;

    @Autowired
    private AmsIntrDetailWriteService amsIntrDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询盘点数据明细表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsIntrDetailDto dto){
        return CommonResult.success(amsIntrDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增盘点数据明细表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsIntrDetailDto dto){
        amsIntrDetailWriteService.save(dto);
        return CommonResult.success();
    }

    @ApiOperation("手工盘点")
    @PostMapping("/intr")
    public CommonResult<?> intr(@RequestBody AmsIntrDetailDto dto){
        amsIntrDetailWriteService.intr(dto);
        return CommonResult.success();
    }

    @ApiOperation("取消手工盘点")
    @PostMapping("/cleanIntr")
    public CommonResult<?> cleanIntr(@RequestBody AmsIntrDetailDto dto) {
        amsIntrDetailWriteService.cleanIntr(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改盘点数据明细表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsIntrDetailDto dto){
        amsIntrDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除盘点数据明细表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsIntrDetailDto dto){
        amsIntrDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除盘盈资产")
    @DeleteMapping("/deleteProfit")
    public CommonResult<?> deleteProfit(@RequestBody AmsIntrDetailDto dto){
        amsIntrDetailWriteService.deleteProfit(dto);
        return CommonResult.success();
    }

}
