package com.jp.med.ams.modules.changes.mapper.write;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资产变更记录表
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
@Mapper
public interface AmsChgRcdWriteMapper extends BaseMapper<AmsChgRcdDto> {

    int getMaxChgCode();

    void approval(AmsChgRcdDto dto);

    void unApproval(AmsChgRcdDto dto);
}
