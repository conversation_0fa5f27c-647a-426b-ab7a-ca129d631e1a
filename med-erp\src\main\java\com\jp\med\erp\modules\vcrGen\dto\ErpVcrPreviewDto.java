package com.jp.med.erp.modules.vcrGen.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * 凭证预览映射表
 * <AUTHOR>
 * @email -
 * @date 2025-03-11 17:00:33
 */
@Data
@TableName("erp_vcr_preview" )
public class ErpVcrPreviewDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 支付类型(先确定大类supType) */
    @TableField("pay_type")
    private String payType;

    /** 类型id */
    @TableField("module_id")
    private Integer moduleId;

    /** 大类型 1：费用报销 2：药品 3：工资 4：折旧 (根据需求新增) */
    @TableField("sup_type")
    private String supType;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 更新者 */
    @TableField("updter")
    private String updter;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

    /** 是否推送 **/
    @TableField("push_flag")
    private String pushFlag;

    /** hrp凭证号 **/
    @TableField("vpzh")
    private String vpzh;

}
