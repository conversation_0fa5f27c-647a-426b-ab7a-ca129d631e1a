package com.jp.med.ams.modules.inventory.vo;

import lombok.Data;

/**
 * 资产盘点任务
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 10:12:34
 */
@Data
public class AmsIntrTaskVo {

	/** ID */
	private Integer id;

	/** 唯一值 */
	private String key;

	/** 盘点任务名称 */
	private String intrTask;

	/** 待盘点科室 */
	private String dept;

	/** 总数量 */
	private Integer totlcnt;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 有效标志 */
	private String flag;

	/** 盘盈*/
	private String profitCount;

	/** 盘亏 */
	private String lossCount;

	/** 原值*/
	private String assetNav;

	/** 已盘点数量 */
	private String ct;
	private String already;

	/** 科室名称 */
	private String orgName;

	/** 状态(0:盘点中,1:盘点完成) */
		private String status;

}
