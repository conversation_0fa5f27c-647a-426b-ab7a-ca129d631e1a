package com.jp.med.ams.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 09:35:21
 */
@Data
public class AmsCfgCcmdVo implements BaseTree<String, AmsCfgCcmdVo> {

	/** ID */
	private Integer id;

	private String key;

	/** 医疗器械分类编码 */
	private String ccmCode;

	/** 医疗器械分类名称 */
	private String ccmName;

	/** 医疗器械分类父类编码 */
	private String ccmParentCode;

	/** 描述 */
	private String desc;

	/** 预期用途 */
	private String intdUse;

	/** 品名举例 */
	private String exmProdName;

	/** 有效标志 */
	private String activeFlag;

	private List<AmsCfgCcmdVo> children;

	@Override
	public void setCode(String id) {
		this.ccmCode = id;
	}

	@Override
	public String getCode() {
		return this.ccmCode;
	}

	@Override
	public String getPid() {
		return this.ccmParentCode;
	}

	@Override
	public void setPid(String pid) {
		this.ccmParentCode = pid;
	}

	@Override
	public void addChild(AmsCfgCcmdVo node) {
		if(Objects.isNull(children)){
			this.children = new ArrayList<>();
		}
		this.children.add(node);
	}
}
