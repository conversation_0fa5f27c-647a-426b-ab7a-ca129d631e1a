package com.jp.med.ams.modules.changes.strategy.impl;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.strategy.AbstractChangeRecordProcessor;
import com.jp.med.ams.modules.changes.strategy.ProcessContext;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产拆分处理器
 * 处理资产拆分业务逻辑
 * 优先级：2
 */
@Component
@Order(2)
public class AssetSplitProcessor extends AbstractChangeRecordProcessor {

    @Override
    public boolean supports(AmsChgRcdDto dto) {
        return "8".equals(dto.getRedcWay()) &&
                dto.getFaCode() != null && !dto.getFaCode().trim().isEmpty();
    }

    @Override
    protected List<String> extractFaCodes(AmsChgRcdDto dto) {
        // 拆分操作使用单个资产编码
        List<String> faCodes = new ArrayList<>();
        if (dto.getFaCode() != null && !dto.getFaCode().trim().isEmpty()) {
            faCodes.add(dto.getFaCode());
        }
        return faCodes;
    }

    @Override
    protected AmsPropertyDto createPropertyUpdate(AmsChgRcdDto dto, String faCode, ProcessContext context) {
        // 拆分操作的具体逻辑需要在专门的拆分页面处理
        // 这里暂时返回null，表示不进行资产状态更新
        return null;
    }
}
