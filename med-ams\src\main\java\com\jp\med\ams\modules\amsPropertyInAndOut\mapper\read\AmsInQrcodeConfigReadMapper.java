package com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInQrcodeConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.vo.AmsInQrcodeConfigVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产入库二维码配置
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
@Mapper
public interface AmsInQrcodeConfigReadMapper extends BaseMapper<AmsInQrcodeConfigDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsInQrcodeConfigVo> queryList(AmsInQrcodeConfigDto dto);

    AmsInQrcodeConfigVo queryOneByQrIdAms(@Param("qrId") String qrId);

    List<AmsOutStockAuditDto> queryUserInQrCodePropertyByQrId(@Param("qrId") String qrId);
}
