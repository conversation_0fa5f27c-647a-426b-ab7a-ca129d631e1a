-- =================================================================================================
-- 修复后的导入脚本执行指南
-- 功能：提供修复后的脚本执行步骤和注意事项
-- 创建时间：2025年1月
-- =================================================================================================

-- =================================================================================================
-- 问题修复说明 🔧
-- =================================================================================================

/*
发现的问题：
1. 源表 mmis_temp_inport_storage_six 缺少 modspec 字段
2. 源表 mmis_temp_xinxike_outbound_six 缺少 modspec 字段

已修复：
1. 在 ALTER TABLE 语句中添加了 modspec varchar(200) 字段
2. 更新了两个导入脚本文件
*/

-- =================================================================================================
-- 执行前检查步骤 ✅
-- =================================================================================================

-- 步骤1：检查源表结构
\i med-mmis/src/main/resources/sql/ops/check_source_table_structure.sql

-- 步骤2：执行快速数据检查
\i med-mmis/src/main/resources/sql/ops/quick_data_check.sql

-- 步骤3：执行导入前预检查
\i med-mmis/src/main/resources/sql/ops/pre_import_check.sql

-- =================================================================================================
-- 正式导入步骤 🚀
-- =================================================================================================

-- 步骤4：执行入库数据导入（如果有入库数据）
-- \i med-mmis/src/main/resources/sql/ops/xinxike_new_storage_import.sql

-- 步骤5：执行出库数据导入（如果有出库数据）
-- \i med-mmis/src/main/resources/sql/ops/xinxike_new_outbound_import.sql

-- =================================================================================================
-- 导入后验证步骤 🔍
-- =================================================================================================

-- 验证入库数据导入结果
SELECT 
    '入库导入结果验证' as check_type,
    COUNT(*) as imported_storage_records
FROM mmis_aset_storage 
WHERE crter = 'SYS_IMPORT' 
  AND create_time >= CURRENT_DATE - INTERVAL '1 day';

SELECT 
    '入库明细导入结果验证' as check_type,
    COUNT(*) as imported_detail_records
FROM mmis_aset_storage_detail 
WHERE crter = 'SYS_IMPORT' 
  AND create_time >= CURRENT_DATE - INTERVAL '1 day';

-- 验证出库数据导入结果
SELECT 
    '出库导入结果验证' as check_type,
    COUNT(*) as imported_outbound_records
FROM mmis_outbound_apply 
WHERE crter = 'SYS_IMPORT' 
  AND create_time >= CURRENT_DATE - INTERVAL '1 day';

SELECT 
    '出库明细导入结果验证' as check_type,
    COUNT(*) as imported_detail_records
FROM mmis_outbound_apply_details 
WHERE crter = 'SYS_IMPORT' 
  AND create_time >= CURRENT_DATE - INTERVAL '1 day';

-- =================================================================================================
-- 回滚脚本（如果需要） ⚠️
-- =================================================================================================

/*
如果导入有问题，可以使用以下脚本回滚：

-- 回滚入库数据
DELETE FROM mmis_aset_storage_detail 
WHERE apply_id IN (
    SELECT id FROM mmis_aset_storage 
    WHERE crter = 'SYS_IMPORT' 
      AND create_time >= CURRENT_DATE - INTERVAL '1 day'
);

DELETE FROM mmis_aset_storage 
WHERE crter = 'SYS_IMPORT' 
  AND create_time >= CURRENT_DATE - INTERVAL '1 day';

-- 回滚出库数据
DELETE FROM mmis_outbound_apply_details 
WHERE apply_id IN (
    SELECT id FROM mmis_outbound_apply 
    WHERE crter = 'SYS_IMPORT' 
      AND create_time >= CURRENT_DATE - INTERVAL '1 day'
);

DELETE FROM mmis_outbound_apply 
WHERE crter = 'SYS_IMPORT' 
  AND create_time >= CURRENT_DATE - INTERVAL '1 day';
*/

-- =================================================================================================
-- 注意事项 📝
-- =================================================================================================

/*
重要提醒：
1. 执行导入前请务必备份相关表数据
2. 建议在测试环境先执行一遍
3. 导入过程中如果遇到错误，请检查错误信息并修复后重试
4. 导入完成后请验证数据的正确性
5. 如果数据量很大，建议分批导入

常见问题解决：
1. 如果提示字段不存在，检查源表结构是否正确
2. 如果提示外键约束错误，检查关联表数据是否存在
3. 如果提示数据类型错误，检查源数据格式是否正确
4. 如果导入速度慢，可以考虑临时禁用索引后重建
*/
