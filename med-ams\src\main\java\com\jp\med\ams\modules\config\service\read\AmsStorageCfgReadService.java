package com.jp.med.ams.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.config.dto.AmsStorageCfgDto;
import com.jp.med.ams.modules.config.vo.AmsStorageCfgVo;

import java.util.List;

/**
 * 存放地点配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-12 10:23:40
 */
public interface AmsStorageCfgReadService extends IService<AmsStorageCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsStorageCfgVo> queryList(AmsStorageCfgDto dto);
}

