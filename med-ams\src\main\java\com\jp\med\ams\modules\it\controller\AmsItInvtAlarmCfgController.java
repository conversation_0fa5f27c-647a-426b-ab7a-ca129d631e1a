package com.jp.med.ams.modules.it.controller;

import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.service.read.AmsItInvtCfgReadService;
import com.jp.med.ams.modules.it.service.write.AmsItInvtCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 信息科库房耗材告警
 * <AUTHOR>
 * @email -
 * @date 2023-12-11 14:02:30
 */
@Api(value = "信息科库房耗材告警", tags = "信息科库房耗材告警")
@RestController
@RequestMapping("amsItInvtAlarmCfg")
public class AmsItInvtAlarmCfgController {

    @Autowired
    private AmsItInvtCfgReadService amsItInvtCfgReadService;

    @Autowired
    private AmsItInvtCfgWriteService amsItInvtCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("信息科库房耗材告警")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItInvtCfgDto dto){
        return CommonResult.success(amsItInvtCfgReadService.cfgList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询信息科库房耗材告警")
    @PostMapping("/queryList")
    public CommonResult<?> queryList(@RequestBody AmsItInvtCfgDto dto){
        return CommonResult.paging(amsItInvtCfgReadService.searchAlarmList(dto));
    }

    /**
     * 查询耗材集合
     * */
    @ApiOperation("查询耗材集合")
    @PostMapping("/queryConsumableList")
    public CommonResult<?> queryConsumableList(@RequestBody AmsItInvtCfgDto dto){
        return CommonResult.success(amsItInvtCfgReadService.queryConsumableList(dto));
    }

    /**
     * 查询耗材集合
     * */
    @ApiOperation("根据id集合查询耗材编码集合")
    @PostMapping("/queryCodeList")
    public CommonResult<?> queryCodeList(@RequestBody AmsItInvtCfgDto dto){
        return CommonResult.success(amsItInvtCfgReadService.searchCodeList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增信息科库房耗材告警")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsItInvtCfgDto dto){
        amsItInvtCfgWriteService.saveAlarmCfg(dto);
        return CommonResult.success();
    }



    /**
     * 修改
     */
    @ApiOperation("修改信息科库房耗材告警")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItInvtCfgDto dto){
        amsItInvtCfgWriteService.updateCfgById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除信息科库房耗材告警")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItInvtCfgDto dto){
        amsItInvtCfgWriteService.removeCfgById(dto);
        return CommonResult.success();
    }

}
