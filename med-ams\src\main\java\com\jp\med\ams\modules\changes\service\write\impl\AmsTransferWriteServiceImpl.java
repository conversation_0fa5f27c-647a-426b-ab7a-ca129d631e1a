package com.jp.med.ams.modules.changes.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.changes.dto.AmsTransferDto;
import com.jp.med.ams.modules.changes.mapper.read.AmsTransferReadMapper;
import com.jp.med.ams.modules.changes.mapper.write.AmsTransferWriteMapper;
import com.jp.med.ams.modules.changes.service.write.AmsTransferWriteService;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.ULIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 资产转移
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
@Service
@Transactional(readOnly = false)
public class AmsTransferWriteServiceImpl extends
        ServiceImpl<AmsTransferWriteMapper, AmsTransferDto> implements AmsTransferWriteService {

    @Autowired
    private AmsTransferWriteMapper amsTransferWriteMapper;
    @Autowired
    private AmsTransferReadMapper amsTransferReadMapper;
    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    // @GlobalTransactional
    public void saveTransfer(AmsTransferDto dto) {
        if (dto.getFaCodes() == null || dto.getFaCodes().isEmpty()) {
            throw new AppException("转移资产为空");
        }
        // 1、生成审核批次号
        String ulid = ULIDUtil.generate();
        String bchno = AuditConst.AMS_TRANSFER_APPLY + ulid;

        dto.setBchno(bchno);
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        // 申请中
        dto.setProsstas(MedConst.TYPE_1);

        // 转移申请校验
        dto.setSqlAutowiredHospitalCondition(true);
        if (amsTransferReadMapper.validateTransferApply(dto)) {
            throw new AppException("当前资产处于转移或划拨中");
        }

        // 转移申请详情
        amsTransferWriteMapper.insert(dto);
        // 插入批量转移关联表
        amsTransferWriteMapper.insertTransferPropertyRef(dto);
        // 2、构建审核消息
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            HrmUser hrmUser = dto.getSysUser().getHrmUser();
            String appyer = org.apache.commons.lang3.StringUtils.isNotEmpty(hrmUser.getEmpCode())
                    ? hrmUser.getEmpCode()
                    : dto.getSysUser().getUsername();
            String appyerName = org.apache.commons.lang3.StringUtils.isNotEmpty(hrmUser.getEmpName())
                    ? hrmUser.getEmpName()
                    : dto.getSysUser().getNickname();

            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle("资产转移申请");
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);

            appMsgSup.setContent(
                    "[" + dto.getTrafOutDeptName() + "]申请[" + dto.getFaCodeName() + "]转移到" + dto.getTrafInDeptName());

            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(bchno);

            // 进入详情页获取申请详细信息的请求地址

            auditPayload.setDetailUrl(dto.getRoutePath());
            // 详情页的表格数据columns,需要在上面这个请求地址返回以下这些字段,用于详情页数据展示
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("trafOutDeptName", "转出科室");
            map.put("trafInDeptName", "转入科室");
            map.put("faCodeName", "转出资产");
            auditPayload.setDisplayItem(map);

            var temp = new AuditDetail(bchno, dto.getAuditDetails(), appMsgSup, auditPayload,
                    OSSConst.BUCKET_AMS);
            temp.setRoutePath(dto.getRoutePath());
            // 远程调用 加入审核表
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(temp));
        }

    }

    @Override
    public void removeTransfer(AmsTransferDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        boolean canDel = amsTransferReadMapper.validateTransferCanDel(dto);
        if (canDel) {
            this.removeById(dto.getId());
            // 删除
            // ams_audit_rcdfm
            // ams_audit_res
        } else {
            throw new AppException("转移正在进行中无法删除");
        }
    }
}
