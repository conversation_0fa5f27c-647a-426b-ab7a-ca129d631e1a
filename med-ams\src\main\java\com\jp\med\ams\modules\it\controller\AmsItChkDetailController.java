package com.jp.med.ams.modules.it.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItChkDetailDto;
import com.jp.med.ams.modules.it.service.read.AmsItChkDetailReadService;
import com.jp.med.ams.modules.it.service.write.AmsItChkDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 耗材申请审核详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Api(value = "耗材申请审核详情", tags = "耗材申请审核详情")
@RestController
@RequestMapping("amsItChkDetail")
public class AmsItChkDetailController {

    @Autowired
    private AmsItChkDetailReadService amsItChkDetailReadService;

    @Autowired
    private AmsItChkDetailWriteService amsItChkDetailWriteService;

    @ApiOperation("查询耗材申请审核详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItChkDetailDto dto){
        return CommonResult.success(amsItChkDetailReadService.getList(dto));
    }
    /**
     * 列表
     */
    @ApiOperation("查询耗材申请审核详情")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody AmsItChkDetailDto dto){
        return CommonResult.paging(amsItChkDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增耗材申请审核详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsItChkDetailDto dto){
        amsItChkDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改耗材申请审核详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItChkDetailDto dto){
        amsItChkDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除耗材申请审核详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItChkDetailDto dto){
        amsItChkDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
