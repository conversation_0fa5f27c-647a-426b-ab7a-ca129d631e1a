package com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write;

import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInStockConfigDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Mapper
public interface AmsInStockConfigWriteMapper extends BaseMapper<AmsInStockConfigDto> {
}
