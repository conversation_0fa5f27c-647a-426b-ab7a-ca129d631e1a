package com.jp.med.ams.modules.depr.service.read.impl.chain.factory;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary.*;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary2.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 资产折旧处理器工厂
 * 负责创建和组装责任链处理器
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyDeprProcessorFactory {

    // ========== Summary 处理器 ==========
    @Autowired
    private AmsPropertySourceConfigProcessor sourceConfigProcessor;

    @Autowired
    private AmsPropertyHouseRepairQueryProcessor houseRepairQueryProcessor;

    @Autowired
    private AmsPropertyNormalAssetsQueryProcessor normalAssetsQueryProcessor;

    @Autowired
    private AmsPropertyMixSourceProcessor mixSourceProcessor;

    @Autowired
    private AmsPropertyMultiDeptProcessor multiDeptProcessor;

    @Autowired
    private AmsPropertyAssetTypeProcessor assetTypeProcessor;

    @Autowired
    private AmsPropertyFinaSubsidyProcessor finaSubsidyProcessor;

    @Autowired
    private AmsPropertyGroupingProcessor groupingProcessor;

    @Autowired
    private AmsPropertyNormalAssetsProcessor normalAssetsProcessor;

    @Autowired
    private AmsPropertyDeprRateProcessor deprRateProcessor;

    @Autowired
    private AmsPropertyHouseRepairProcessor houseRepairProcessor;

    @Autowired
    private AmsPropertyFinalSummaryProcessor finalSummaryProcessor;

    @Autowired
    private AmsPropertyInfoDeptWarehouseProcessor infoDeptWarehouseProcessor;

    // ========== Summary2 处理器 ==========
    @Autowired
    private AmsPropertyLastMonthDataProcessor lastMonthDataProcessor;

    @Autowired
    private AmsPropertyDataMappingProcessor dataMappingProcessor;

    @Autowired
    private AmsPropertyDataMergeProcessor dataMergeProcessor;

    @Autowired
    private AmsPropertyLastMonthOnlyProcessor lastMonthOnlyProcessor;

    @Autowired
    private AmsPropertySourceMappingProcessor sourceMappingProcessor;

    @Autowired
    private AmsPropertySummary2FinalProcessor summary2FinalProcessor;

    /**
     * 创建 queryDeprSummary 方法的处理器链
     * 
     * @return 处理器链的头节点
     */
    public AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> createSummaryProcessorChain() {
        log.debug("创建 queryDeprSummary 处理器链");

        // 构建完整的处理器链
        sourceConfigProcessor
                .setNext(houseRepairQueryProcessor) // 房屋维修资产查询处理器
                .setNext(normalAssetsQueryProcessor) // 普通资产查询处理器
                .setNext(mixSourceProcessor) // 资金来源混合处理器
                .setNext(multiDeptProcessor) // 多科室分摊处理器
                .setNext(assetTypeProcessor) // 资产类型转换处理器
                .setNext(finaSubsidyProcessor) // 财政补助处理器
                .setNext(infoDeptWarehouseProcessor) // 信息科库房资产处理器
                .setNext(groupingProcessor) // 资产分组处理器
                .setNext(normalAssetsProcessor) // 普通资产处理器
                .setNext(deprRateProcessor) // 科室折旧比例处理器
                .setNext(houseRepairProcessor) // 房屋维修资产处理器
                .setNext(finalSummaryProcessor);// 最终汇总处理器

        return sourceConfigProcessor;
    }

    /**
     * 创建 queryDeprSummary2 方法的处理器链
     *
     * @return 处理器链的头节点
     */
    public AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> createSummary2ProcessorChain() {
        log.debug("创建 queryDeprSummary2 处理器链");

        // 构建 Summary2 处理器链
        lastMonthDataProcessor
                .setNext(dataMappingProcessor) // 资产数据映射处理器
                .setNext(dataMergeProcessor) // 资产数据合并处理器
                .setNext(lastMonthOnlyProcessor) // 上月独有数据处理器
                .setNext(sourceMappingProcessor) // 资金来源映射处理器
                .setNext(summary2FinalProcessor); // Summary2 最终汇总处理器

        return lastMonthDataProcessor;
    }
}
