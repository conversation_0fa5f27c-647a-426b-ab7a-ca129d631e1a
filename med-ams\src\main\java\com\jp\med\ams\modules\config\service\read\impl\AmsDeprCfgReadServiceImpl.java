package com.jp.med.ams.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.config.mapper.read.AmsDeprCfgReadMapper;
import com.jp.med.ams.modules.config.dto.AmsDeprCfgDto;
import com.jp.med.ams.modules.config.vo.AmsDeprCfgVo;
import com.jp.med.ams.modules.config.service.read.AmsDeprCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsDeprCfgReadServiceImpl extends ServiceImpl<AmsDeprCfgReadMapper, AmsDeprCfgDto> implements AmsDeprCfgReadService {

    @Autowired
    private AmsDeprCfgReadMapper amsDeprCfgReadMapper;

    @Override
    public List<AmsDeprCfgVo> queryList(AmsDeprCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 调用mapper的queryList方法查询数据
        List<AmsDeprCfgVo> list = amsDeprCfgReadMapper.queryList(dto);

        return amsDeprCfgReadMapper.queryList(dto);
    }

}
