package com.jp.med.ams.modules.it.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 科室耗材配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 15:04:47
 */
@Data
@TableName("ams_it_invt_org_consumable" )
public class AmsItInvtOrgConsumableDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 医院科室ID */
    @TableField("org_id")
    private String orgId;

    /** 设备编码 */
    @TableField("equ_code")
    private String equCode;

    /** 耗材编码 */
    @TableField("consum_code")
    private String consumCode;

    /** 父编码 */
    @TableField("parent_code")
    private String parentCode;

    /** 耗材名称 */
    @TableField("consum_name")
    private String consumName;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改人员 */
    @TableField("updtr")
    private String updtr;

    /** 修改时间 */
    @TableField("update_time")
    private String updateTime;

    /** 删除人员 */
    @TableField("delter")
    private String delter;

    /** 删除时间 */
    @TableField("delete_time")
    private String deleteTime;

    /** 逻辑删除时间(0:存在 1:不存在) */
    @TableField("active_flag")
    private String activeFlag;

    /** 医院ID */
    @TableField("hospital_id")
    private String hospitalId;

    /** 设备名称 */
    @TableField("equ_name")
    private String equName;

    /** 设备类型 */
    @TableField("equ_type_code")
    private String equTypeCode;

    /** 设备类型名 */
    @TableField("equ_type_name")
    private String equTypeName;


    @TableField(exist = false)
    private String orgName;
}
