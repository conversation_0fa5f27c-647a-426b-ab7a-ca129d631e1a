package com.jp.med.ams.modules.changes.strategy.borrowing.factory;

import com.jp.med.ams.modules.changes.strategy.borrowing.AmsBorrowingAuditStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.impl.AmsBorrowingApprovalStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.impl.AmsBorrowingExtensionRequestStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.impl.AmsBorrowingRejectionStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.impl.AmsBorrowingReturnConfirmationStrategy;
import com.jp.med.common.constant.MedConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 资产借用审核策略工厂
 * 根据审核类型和目标状态创建对应的策略实例
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Slf4j
@Component
public class AmsBorrowingAuditStrategyFactory {

    /**
     * 策略缓存映射
     */
    private final Map<String, AmsBorrowingAuditStrategy> strategyCache = new HashMap<>();
    @Autowired
    private AmsBorrowingApprovalStrategy approvalStrategy;
    @Autowired
    private AmsBorrowingRejectionStrategy rejectionStrategy;
    @Autowired
    private AmsBorrowingReturnConfirmationStrategy returnConfirmationStrategy;
    @Autowired
    private AmsBorrowingExtensionRequestStrategy extensionRequestStrategy;

    /**
     * 根据审核类型和目标状态获取对应的审核策略
     *
     * @param updateType          更新类型
     *                            1: 审核通过/确认
     *                            2: 审核拒绝/驳回
     *                            3: 续期申请
     * @param targetProcessStatus 目标流程状态
     *                            4: 借用审核通过
     *                            5: 归还确认完成
     * @return 对应的审核策略实例
     */
    public AmsBorrowingAuditStrategy getStrategy(String updateType, String targetProcessStatus) {
        String strategyKey = buildStrategyKey(updateType, targetProcessStatus);

        // 先从缓存中获取
        AmsBorrowingAuditStrategy strategy = strategyCache.get(strategyKey);
        if (strategy != null) {
            return strategy;
        }

        // 根据业务规则创建策略
        strategy = createStrategy(updateType, targetProcessStatus);

        if (strategy != null) {
            // 缓存策略实例
            strategyCache.put(strategyKey, strategy);
            log.debug("🔧 创建并缓存审核策略：{} -> {}", strategyKey, strategy.getClass().getSimpleName());
        }

        return strategy;
    }

    /**
     * 创建策略实例
     */
    private AmsBorrowingAuditStrategy createStrategy(String updateType, String targetProcessStatus) {
        // 续期申请策略
        if ("3".equals(updateType)) {
            return extensionRequestStrategy;
        }

        // 审核通过/确认策略
        if (MedConst.TYPE_1.equals(updateType)) {
            if (MedConst.TYPE_5.equals(targetProcessStatus)) {
                // 归还确认完成
                return returnConfirmationStrategy;
            } else {
                // 借用审核通过或其他确认操作
                return approvalStrategy;
            }
        }

        // 审核拒绝/驳回策略
        if (MedConst.TYPE_2.equals(updateType) ||
                (!MedConst.TYPE_1.equals(updateType) && !"3".equals(updateType))) {
            return rejectionStrategy;
        }

        log.warn("⚠️ 未找到匹配的审核策略，updateType：{}，targetProcessStatus：{}",
                updateType, targetProcessStatus);
        return null;
    }

    /**
     * 构建策略缓存键
     */
    private String buildStrategyKey(String updateType, String targetProcessStatus) {
        return String.format("%s_%s",
                updateType != null ? updateType : "null",
                targetProcessStatus != null ? targetProcessStatus : "null");
    }

    /**
     * 根据策略类型直接获取策略实例
     *
     * @param strategyType 策略类型
     * @return 策略实例
     */
    public AmsBorrowingAuditStrategy getStrategyByType(String strategyType) {
        switch (strategyType) {
            case "APPROVAL":
                return approvalStrategy;
            case "REJECTION":
                return rejectionStrategy;
            case "RETURN_CONFIRMATION":
                return returnConfirmationStrategy;
            case "EXTENSION_REQUEST":
                return extensionRequestStrategy;
            default:
                log.warn("⚠️ 未知的策略类型：{}", strategyType);
                return null;
        }
    }

    /**
     * 清空策略缓存（用于测试或重新初始化）
     */
    public void clearStrategyCache() {
        strategyCache.clear();
        log.info("🧹 策略缓存已清空");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", strategyCache.size());
        stats.put("cachedStrategies", strategyCache.keySet());
        return stats;
    }

    /**
     * 验证策略工厂是否正确初始化
     */
    public boolean validateFactory() {
        boolean isValid = true;

        if (approvalStrategy == null) {
            log.error("❌ 审核通过策略未正确注入");
            isValid = false;
        }

        if (rejectionStrategy == null) {
            log.error("❌ 审核拒绝策略未正确注入");
            isValid = false;
        }

        if (returnConfirmationStrategy == null) {
            log.error("❌ 归还确认策略未正确注入");
            isValid = false;
        }

        if (extensionRequestStrategy == null) {
            log.error("❌ 续期申请策略未正确注入");
            isValid = false;
        }

        if (isValid) {
            log.info("✅ 借用审核策略工厂初始化完成");
        }

        return isValid;
    }
}
