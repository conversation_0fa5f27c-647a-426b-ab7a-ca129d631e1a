-- =================================================================================================
-- 修复出库源表缺失字段脚本
-- 功能：为出库源表添加导入脚本所需的所有字段
-- 执行时机：在运行出库导入脚本之前执行
-- =================================================================================================

-- 为出库源表添加所有必要字段
ALTER TABLE mmis_temp_xinxike_outbound_six 
ADD COLUMN IF NOT EXISTS out_target_org_id varchar(100),
ADD COLUMN IF NOT EXISTS out_appyer varchar(50),
ADD COLUMN IF NOT EXISTS mat_unique_code varchar(100),
ADD COLUMN IF NOT EXISTS item_num varchar(50),
ADD COLUMN IF NOT EXISTS meter_code varchar(50),
ADD COLUMN IF NOT EXISTS create_time varchar(50),
ADD COLUMN IF NOT EXISTS bill_date date,
ADD COLUMN IF NOT EXISTS opter varchar(50),
ADD COLUMN IF NOT EXISTS opter_org varchar(50),
ADD COLUMN IF NOT EXISTS wrhs_addr varchar(50),
ADD COLUMN IF NOT EXISTS item_count numeric(5,2),
ADD COLUMN IF NOT EXISTS hospital_id varchar(50),
ADD COLUMN IF NOT EXISTS is_deleted int,
ADD COLUMN IF NOT EXISTS chk_state varchar(1),
ADD COLUMN IF NOT EXISTS appy_org_id varchar(50),
ADD COLUMN IF NOT EXISTS modspec varchar(200),
ADD COLUMN IF NOT EXISTS out_emp varchar(50),
ADD COLUMN IF NOT EXISTS out_org varchar(100);

-- 验证所有必要字段是否已添加
SELECT 
    '出库源表必要字段检查' as check_type,
    CASE 
        WHEN COUNT(*) = 18 THEN '✅ 所有字段已添加'
        ELSE '❌ 缺少字段: ' || (18 - COUNT(*))::text || ' 个'
    END as status,
    COUNT(*) as existing_fields,
    18 as required_fields
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
  AND column_name IN (
    'out_target_org_id', 'out_appyer', 'mat_unique_code', 'item_num', 
    'meter_code', 'create_time', 'bill_date', 'opter', 'opter_org', 
    'wrhs_addr', 'item_count', 'hospital_id', 'is_deleted', 'chk_state', 
    'appy_org_id', 'modspec', 'out_emp', 'out_org'
  );

-- 显示添加的字段详情
SELECT 
    '新添加字段详情' as info_type,
    column_name,
    data_type,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'mmis_temp_xinxike_outbound_six'
  AND column_name IN (
    'out_target_org_id', 'out_appyer', 'mat_unique_code', 'item_num', 
    'meter_code', 'create_time', 'bill_date', 'opter', 'opter_org', 
    'wrhs_addr', 'item_count', 'hospital_id', 'is_deleted', 'chk_state', 
    'appy_org_id', 'modspec', 'out_emp', 'out_org'
  )
ORDER BY column_name;

-- 检查源表数据样例（确认原始数据字段）
SELECT 
    '源表原始字段数据样例' as sample_type,
    *
FROM mmis_temp_xinxike_outbound_six 
LIMIT 2;
