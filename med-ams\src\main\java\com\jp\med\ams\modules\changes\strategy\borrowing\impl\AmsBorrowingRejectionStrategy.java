package com.jp.med.ams.modules.changes.strategy.borrowing.impl;

import com.jp.med.ams.modules.changes.dto.AmsBorrowingAuditNotificationDto;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.mapper.write.AmsChngBrwgWriteMapper;
import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import com.jp.med.ams.modules.changes.strategy.borrowing.AmsBorrowingAuditStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.context.AmsBorrowingAuditContext;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产借用审核拒绝策略
 * 处理借用申请审核拒绝的业务逻辑
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Slf4j
@Component
public class AmsBorrowingRejectionStrategy implements AmsBorrowingAuditStrategy {

    @Autowired
    private AmsChngBrwgWriteMapper amsChngBrwgWriteMapper;

    @Autowired
    private AmsBorrowingNotificationService notificationService;

    @Override
    public boolean executeAudit(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        log.info("🔄 执行借用审核拒绝策略，审核人：{}，拒绝原因：{}",
                context.getAuditorName(), dto.getBackRemarks());

        try {
            if (context.isBatchOperation()) {
                return executeBatchRejection(context, dto);
            } else {
                return executeSingleRejection(context, dto);
            }
        } catch (Exception e) {
            log.error("❌ 借用审核拒绝策略执行失败", e);
            return false;
        }
    }

    /**
     * 执行批量审核拒绝
     */
    private boolean executeBatchRejection(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        List<AmsChngBrwgDto> updateDtoList = new ArrayList<>();

        // 构建批量更新数据
        for (Integer id : dto.getIds()) {
            AmsChngBrwgVo borrowingRecord = context.getBorrowingRecordMap().get(id);
            if (borrowingRecord == null) {
                log.warn("⚠️ 未找到借用记录，ID：{}", id);
                continue;
            }

            // 构建借用记录更新DTO
            AmsChngBrwgDto updateDto = buildRejectionUpdateDto(dto, id, context.getAuditTime());
            updateDtoList.add(updateDto);
        }

        // 批量更新借用记录
        if (!CollectionUtils.isEmpty(updateDtoList)) {
            BatchUtil.batch("updateById", updateDtoList, AmsChngBrwgWriteMapper.class);
        }

        // 发送批量拒绝通知
        sendBatchRejectionNotifications(context, dto);

        log.info("✅ 批量借用审核拒绝完成，处理记录数：{}", dto.getIds().size());
        return true;
    }

    /**
     * 执行单个审核拒绝
     */
    private boolean executeSingleRejection(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        // 更新借用记录
        AmsChngBrwgDto updateDto = buildRejectionUpdateDto(dto, dto.getId(), context.getAuditTime());
        int updateResult = amsChngBrwgWriteMapper.updateById(updateDto);

        if (updateResult != 1) {
            log.error("❌ 更新借用记录失败，ID：{}", dto.getId());
            return false;
        }

        // 发送单个拒绝通知
        if (!CollectionUtils.isEmpty(context.getBorrowingRecords())) {
            AmsChngBrwgVo borrowingRecord = context.getBorrowingRecords().get(0);
            sendSingleRejectionNotification(context, borrowingRecord, dto.getBackRemarks());
        }

        log.info("✅ 单个借用审核拒绝完成，记录ID：{}", dto.getId());
        return true;
    }

    /**
     * 构建审核拒绝更新DTO
     */
    private AmsChngBrwgDto buildRejectionUpdateDto(AmsChngBrwgDto originalDto, Integer id, String auditTime) {
        AmsChngBrwgDto updateDto = new AmsChngBrwgDto();
        updateDto.setId(id);
        updateDto.setProsstas(originalDto.getProsstas());
        updateDto.setBackRemarks(originalDto.getBackRemarks());
        updateDto.setBackTime(auditTime);
        updateDto.setBusstas(MedConst.TYPE_2); // 已驳回
        return updateDto;
    }

    /**
     * 发送批量审核拒绝通知
     */
    private void sendBatchRejectionNotifications(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        try {
            String rejectReason = StringUtils.hasText(dto.getBackRemarks()) ?
                    dto.getBackRemarks() : "未提供拒绝原因";

            for (Integer id : dto.getIds()) {
                AmsChngBrwgVo borrowingRecord = context.getBorrowingRecordMap().get(id);
                if (borrowingRecord != null) {
                    AmsBorrowingAuditNotificationDto notificationDto =
                            AmsBorrowingAuditNotificationDto.buildRejectionNotification(
                                    borrowingRecord, context.getAuditorName(),
                                    context.getAuditorDept(), context.getAuditTime(), rejectReason);

                    notificationService.sendApplicationRejectedNotification(borrowingRecord, notificationDto);
                }
            }
        } catch (Exception e) {
            log.warn("⚠️ 发送批量审核拒绝通知失败", e);
        }
    }

    /**
     * 发送单个审核拒绝通知
     */
    private void sendSingleRejectionNotification(AmsBorrowingAuditContext context,
                                                 AmsChngBrwgVo borrowingRecord, String rejectReason) {
        try {
            String finalRejectReason = StringUtils.hasText(rejectReason) ? rejectReason : "未提供拒绝原因";

            AmsBorrowingAuditNotificationDto notificationDto =
                    AmsBorrowingAuditNotificationDto.buildRejectionNotification(
                            borrowingRecord, context.getAuditorName(),
                            context.getAuditorDept(), context.getAuditTime(), finalRejectReason);

            notificationService.sendApplicationRejectedNotification(borrowingRecord, notificationDto);
        } catch (Exception e) {
            log.warn("⚠️ 发送单个审核拒绝通知失败", e);
        }
    }

    @Override
    public String getStrategyType() {
        return "REJECTION";
    }

    @Override
    public boolean validateAuditParams(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        if (context == null || context.getAuditor() == null) {
            log.error("❌ 审核上下文或审核人信息为空");
            return false;
        }

        if (dto == null) {
            log.error("❌ 审核数据为空");
            return false;
        }

        if (context.isBatchOperation() && CollectionUtils.isEmpty(dto.getIds())) {
            log.error("❌ 批量操作时ID列表为空");
            return false;
        }

        if (!context.isBatchOperation() && dto.getId() == null) {
            log.error("❌ 单个操作时ID为空");
            return false;
        }

        // 验证拒绝原因（可选，根据业务需求决定是否必填）
        if (!StringUtils.hasText(dto.getBackRemarks())) {
            log.warn("⚠️ 拒绝原因为空，将使用默认原因");
        }

        return true;
    }
}
