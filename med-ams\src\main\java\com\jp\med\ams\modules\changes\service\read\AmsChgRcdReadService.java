package com.jp.med.ams.modules.changes.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.vo.AmsChgRcdVo;

import java.util.List;

/**
 * 资产变更记录表
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
public interface AmsChgRcdReadService extends IService<AmsChgRcdDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsChgRcdVo> queryList(AmsChgRcdDto dto);
}

