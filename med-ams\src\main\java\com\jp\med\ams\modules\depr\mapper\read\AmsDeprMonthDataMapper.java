package com.jp.med.ams.modules.depr.mapper.read;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.ams.modules.depr.dto.AmsDeprMonthDataDto;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产折旧分配
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Mapper
public interface AmsDeprMonthDataMapper extends BaseMapper<AmsDeprMonthDataDto> {

    default List<AmsPropertyDepr2Vo> getAmsDeprMonthData(String date) {
        LambdaQueryWrapper<AmsDeprMonthDataDto> monthDataDtoLambdaQueryWrapper = Wrappers
                .lambdaQuery(AmsDeprMonthDataDto.class);
        monthDataDtoLambdaQueryWrapper.eq(AmsDeprMonthDataDto::getDate, date);
        List<AmsDeprMonthDataDto> amsDeprMonthDataDtos = selectList(monthDataDtoLambdaQueryWrapper);
        if (!amsDeprMonthDataDtos.isEmpty()) {
            return amsDeprMonthDataDtos.get(0).getData();
        }
        return null;
    }

    default void inserAmsDeprMonthData(List<AmsPropertyDepr2Vo> dto, String date) {
        AmsDeprMonthDataDto amsDeprMonthDataDto = new AmsDeprMonthDataDto();
        amsDeprMonthDataDto.setDate(date);
        amsDeprMonthDataDto.setData(dto);
        insert(amsDeprMonthDataDto);
    }

    /**
     * 更新所有折旧数据
     * 按照README.md中定义的步骤计算资产折旧
     */
    default void updateAllDepreciationData(String date) {
        // 1. 基础数据设置
        // 1.1 更新折旧年限
        updateUsefulLife();
        // 1.2 更新折旧率
        updateDepreciationRate();
        // 1.3 更新purc_date 如果purc_date为空设置为开始使用日期
        updatePurcDate();
        // 2. 时间计算
        // 2.1 更新已使用年限
        updateUsedExp(date);
        // 2.2 更新已使用月数
        updateUsedMonths(date);
        // 2.3 更新折旧月数
        updateDeprMonths(date);
        // 2.4 设置不折旧月数为null
        setDeprMonthsNull();
        // 3. 折旧额计算
        // 3.1 更新月折旧额（普通资产）
        updateMonthlyDepreciation();
        // 3.3 将不折旧资产的月折旧额设置为空
        setMonthlyDepreciationNull();
        // 4. 净值和累计折旧计算
        // 4.1 更新净值
        updateResidualValue();
        // 4.2 特殊处理不折旧资产的累计折旧（除土地外）
        updateNoDepre();
        // 4.3 更新累计折旧（正常折旧资产）
        updateAccumulatedDepreciation();
    }

    AmsDeprMonthDataDto findAmsPropertyDepr2VoByDate(String date);

    /**
     * 1.1 更新折旧年限
     * 从资产类型配置表获取年限数据，更新到对应资产
     */
    int updateUsefulLife();

    /**
     * 1.2 更新折旧率
     * 计算月折旧率 = 1 / (折旧年限 × 12)
     */
    int updateDepreciationRate();

    /**
     * 2.1 更新已使用年限
     * 当前年份 - 日期年份（优先使用采购日期，其次是开始使用日期，如果两者都为空则设置为0）
     */
    int updateUsedExp(String date);

    /**
     * 2.2 更新已使用月数
     * 当前日期与日期的月份差（优先使用采购日期，其次是开始使用日期，如果两者都为空则设置为0）
     */
    int updateUsedMonths(String date);

    /**
     * 2.3 更新折旧月数
     * 与已使用月数相同（优先使用采购日期，其次是开始使用日期，如果两者都为空则设置为0），但不包括不折旧资产
     */
    int updateDeprMonths(String date);

    /**
     * 2.4 将不折旧资产的折旧月数设置为null
     */
    int setDeprMonthsNull();

    /**
     * 3.1 更新月折旧额
     * 月折旧额 = 折旧率 × 原值
     * 如果已折旧月数 > 折旧年限×12，则月折旧额为0
     */
    int updateMonthlyDepreciation();

    /**
     * 3.2 更新房屋建筑物的月折旧额
     * 特殊处理房屋建筑物(asset_type_n like 'A01%')的月折旧额
     */
    int updateMonthlyDepreciationFW();

    /**
     * 3.3 将不折旧资产的月折旧额设置为null
     */
    int setMonthlyDepreciationNull();

    /**
     * 4.1 更新净值
     * 净值 = 原值 - (月折旧额 × 已折旧月数)
     * 如果已折旧月数 >= 使用年限×12，则净值为0
     * 土地资产(asset_type='701')的净值等于原值
     */
    int updateResidualValue();

    /**
     * 4.2 特殊处理不折旧资产的累计折旧
     * 对于dm='NO'的资产（除土地外），累计折旧 = 原值
     */
    int updateNoDepre();

    /**
     * 更新purc_date 如果purc_date为空设置为开始使用日期
     */
    int updatePurcDate();

    /**
     * 4.3 更新累计折旧
     * 累计折旧 = 原值 - 净值
     * 对于正常折旧资产和土地资产
     */
    int updateAccumulatedDepreciation();
}
