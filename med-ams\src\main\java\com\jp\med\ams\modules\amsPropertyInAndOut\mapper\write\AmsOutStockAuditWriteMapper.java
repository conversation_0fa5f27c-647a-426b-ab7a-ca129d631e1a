package com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @email -
 * @date 2024-05-27 20:15:50
 */
@Mapper
public interface AmsOutStockAuditWriteMapper extends BaseMapper<AmsOutStockAuditDto> {

    /**
     * 更新审批状态
     *
     * @param bchno
     * @param prosstas
     */
    void updateApplyProsstas(@Param("bchno") String bchno, @Param("prosstas") String prosstas);

    void updateProsstasByPropertyId(@Param("propertyId") Long propertyId, @Param("prosstas") String prosstas);

    @Select(" SELECT aaa.id,aaa.bchno\n" +
            " FROM ams_audit_rcdfm aaa\n" +
            "   INNER JOIN ams_out_stock_audit a ON aaa.bchno = a.bchno\n" +
            " WHERE a.id = #{id}\n" +
            " AND aaa.chk_time IS NULL\n" +
            " ORDER BY aaa.chk_seq\n" +
            " LIMIT 1")
    AmsOutStockAuditDto queryFirstAuditRcdfmId(@Param("id") Long id);

    @Select("SELECT CASE WHEN chk_seq = (SELECT MAX(chk_seq) FROM ams_audit_rcdfm WHERE bchno = #{bchno}) THEN true ELSE false END AS is_max_seq " +
            "FROM ams_audit_rcdfm WHERE id = #{id} ")
    boolean isLastNode(AmsOutStockAuditDto dto);

    /**
     * 查询审批的最新待审批节点id用于批量审批
     */
    @Update(" UPDATE ams_audit_rcdfm\n" +
            " SET chk_time = to_char(now(), 'yyyy-MM-dd HH:mm:ss'),\n" +
            "     chk_state  = #{type}::numeric,\n" +
            "     chk_sign_path = #{signFileUrl}\n" +
            " WHERE id = (\n" +
            " SELECT rcdfm_id\n" +
            " FROM (\n" +
            " SELECT aaa.id AS rcdfm_id\n" +
            " FROM ams_audit_rcdfm aaa\n" +
            "   INNER JOIN ams_out_stock_audit a ON aaa.bchno = a.bchno\n" +
            " WHERE aaa.id = #{id}\n" +
            " AND aaa.chk_time IS NULL\n" +
            " ORDER BY aaa.chk_seq\n" +
            " LIMIT 1\n" +
            " ) tmp\n" +
            " );")
    Long batchApply(AmsOutStockAuditDto dto);

    @Update("update ams_audit_res set audit_res = #{audit_res} where  bchno = #{bchno} ")
    void updateAuditStatus(@Param("bchno") String bchno, @Param("audit_res") String audit_res);

}
