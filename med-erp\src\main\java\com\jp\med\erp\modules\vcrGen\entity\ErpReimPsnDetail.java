package com.jp.med.erp.modules.vcrGen.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpReimPsnDetail {

    /** id */
    private Integer id;

    /** 报销详情id */
    private Integer reimDetailId;

    /** 科室 */
    private String dept;

    /** 科室名称 */
    private String deptName;

    /** 出差人员 */
    private String tripPsn;

    /** 出差金额 */
    private BigDecimal reimAmt;

    /** 类型(1:差旅申请,2:报销) */
    private String type;

    /** 出差人员名称 */
    private String tripPsnName;
}
