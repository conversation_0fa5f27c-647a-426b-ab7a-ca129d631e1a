package com.jp.med.ams.modules.changes.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.changes.dto.AmsAllocDto;

/**
 * 资产划拨
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
public interface AmsAllocWriteService extends IService<AmsAllocDto> {

    /**
     * 新增资产划拨记录
     * @param dto
     */
    void saveAlloc(AmsAllocDto dto);

    /**
     * 删除资产划拨记录
     * @param dto
     */
    void removeAlloc(AmsAllocDto dto);
}

