package com.jp.med.ams.modules.depr.vo;

import java.math.BigDecimal;

import lombok.Data;

@Data
public class AmsPropertyValueAnalysisVo {
    /**
     * 资产类别代码
     */
    private String assetTypeCode;

    /**
     * 资产类别名称
     */
    private String assetTypeName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 本期折旧
     */
    private BigDecimal currentDepreciation;
    /**
     * 期末原值
     */
    private BigDecimal originalValue;

    /**
     * 期末累计折旧/摊销金额
     */
    private BigDecimal totalDeprAmount;

    /**
     * 期末累计折旧/摊销占原值比例
     */
    private BigDecimal totalDeprRate;

    /**
     * 期末净值
     */
    private BigDecimal netValue;

    /**
     * 期末净值占原值比例
     */
    private BigDecimal netValueRate;

}