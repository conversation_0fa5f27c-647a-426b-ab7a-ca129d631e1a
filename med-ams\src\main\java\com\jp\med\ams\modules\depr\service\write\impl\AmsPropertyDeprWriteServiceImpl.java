package com.jp.med.ams.modules.depr.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.depr.mapper.write.AmsPropertyDeprWriteMapper;
import com.jp.med.ams.modules.depr.dto.AmsPropertyDeprDto;
import com.jp.med.ams.modules.depr.service.write.AmsPropertyDeprWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资产折旧表
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 20:21:29
 */
@Service
@Transactional(readOnly = false)
public class AmsPropertyDeprWriteServiceImpl extends ServiceImpl<AmsPropertyDeprWriteMapper, AmsPropertyDeprDto> implements AmsPropertyDeprWriteService {
}
