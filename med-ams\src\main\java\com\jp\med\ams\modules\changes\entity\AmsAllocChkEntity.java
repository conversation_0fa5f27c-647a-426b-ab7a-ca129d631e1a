package com.jp.med.ams.modules.changes.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产划拨审核明细表
 * <AUTHOR>
 * @email -
 * @date 2023-09-12 16:04:42
 */
@Data
public class AmsAllocChkEntity {

	/** id */
	private Long id;

	/** 资产划拨表ID */
	private Long allocId;

	/** 审核人 */
	private String chker;

	/** 审核科室 */
	private String chkDept;

	/** 审核时间 */
	private String chkTime;

	/** 审核备注 */
	private String chkRemarks;

	/** 审核顺序 */
	private Integer chkSeq;

	/** 审核状态 */
	private String chkState;

	/** 审核人名称 */
	private String chkerName;

	/** 审核科室名称 */
	private String chkDeptName;
}
