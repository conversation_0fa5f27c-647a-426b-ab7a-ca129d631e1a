package com.jp.med.ams.modules.amsPropertyInAndOut.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInStockConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.read.AmsInStockConfigReadService;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.write.AmsOutStockConfigWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import java.util.List;


/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Api(value = "资产入库信息填写配置", tags = "资产入库信息填写配置")
@RestController
@RequestMapping("amsOutStockConfig")
public class AmsInStockConfigController {

    @Autowired
    private AmsInStockConfigReadService amsOutStockConfigReadService;

    @Autowired
    private AmsOutStockConfigWriteService amsOutStockConfigWriteService;



    /**
     * 列表
     */
    @ApiOperation("查询资产入库信息配置列表")
    @PostMapping("/list")
    public CommonResult<?> page(@RequestBody AmsInStockConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.paging(amsOutStockConfigReadService.queryList(dto));
    }


    @ApiOperation("查询资产入库信息配置")
    @PostMapping("/queryConfig")
    public CommonResult<?> queryConfig(@RequestBody AmsInStockConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        LambdaQueryWrapper<AmsInStockConfigDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmsInStockConfigDto::getOutDept, dto.getOutDept());
        List<AmsInStockConfigDto> list = amsOutStockConfigReadService.list(wrapper);
        return CommonResult.success(list);
    }


    /**
     * 保存
     */
    @ApiOperation("新增资产入库信息填写配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsInStockConfigDto dto) {
        amsOutStockConfigWriteService.saveConfig(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产入库信息填写配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsInStockConfigDto dto) {
//        amsOutStockConfigWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产入库信息填写配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsInStockConfigDto dto) {
        amsOutStockConfigWriteService.removeConfig(dto);
        return CommonResult.success();
    }

    @ApiOperation("发起出库流程")
    @PostMapping("/startOutProcess")
    public CommonResult<?> startOutProcess(@RequestBody AmsOutStockAuditDto dto) {
        amsOutStockConfigWriteService.startOutProcess(dto);
        return CommonResult.success();
    }



    @ApiOperation("扫描二维码重定向入库页面+token登录")
    @GetMapping("/scanQrCode/{code}")
    public RedirectView scanQrCode(@PathVariable("code") String code){
//        ossUtil.uploadFile(Co)
        String testUrl = "https://www.baidu.com";
        return new RedirectView(testUrl);
    }

}
