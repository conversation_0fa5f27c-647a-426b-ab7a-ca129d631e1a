package com.jp.med.ams.bpmApproveMessageHandl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jp.med.ams.modules.property.dto.AmsBorrowFromOtherHospitalsDto;
import com.jp.med.ams.modules.property.service.write.AmsBorrowFromOtherHospitalsWriteService;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 📦 医院间资产借用审批消息处理
 * <p>
 * 该类处理从其他医院借用资产的BPM审批流程消息
 * 监听AMS_Borrow_from_other_hospitals队列的消息
 */
@Component
@Slf4j
@Getter
@Setter
public class BpmAmsBorrowMessageHandle extends AbstractBpmApproveMessageHandle {

    @Autowired
    private AmsBorrowFromOtherHospitalsWriteService amsBorrowFromOtherHospitalsWriteService;

    @Override
    public String[] getProcessIdentifier() {
        return new String[]{"AMS_Borrow_from_other_hospitals"};
    }

    @Override
    @RabbitListener(queues = {"AMS_Borrow_from_other_hospitals"})
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        super.receiveMessage0(msg);
    }

    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {
        log.info("🆕 处理资产借用流程创建: 业务ID={}", message.getBusinessKey());
        // 更新流程实例ID和状态为审批中
        updateBorrowStatus(message.getBusinessKey(), "2", message.getProcessInstanceId());
    }

    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {
        log.info("✅ 处理资产借用审批通过: 业务ID={}", message.getBusinessKey());
        // 更新状态为审批通过
        updateBorrowStatus(message.getBusinessKey(), "3", message.getProcessInstanceId());
    }

    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        log.info("❌ 处理资产借用审批拒绝: 业务ID={}", message.getBusinessKey());
        // 更新状态为审批不通过
        updateBorrowStatus(message.getBusinessKey(), "4", message.getProcessInstanceId());
    }

    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {
        log.info("🔄 处理资产借用审批进行中: 业务ID={}", message.getBusinessKey());
        // 更新状态为审批中
        updateBorrowStatus(message.getBusinessKey(), "2", message.getProcessInstanceId());
    }

    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {
        log.info("🚫 处理资产借用审批取消: 业务ID={}", message.getBusinessKey());
        // 更新状态为审批取消
        updateBorrowStatus(message.getBusinessKey(), "5", message.getProcessInstanceId());
    }

    /**
     * 更新借用状态
     *
     * @param businessKey       业务ID
     * @param status            状态码
     * @param processInstanceId 流程实例ID
     */
    private void updateBorrowStatus(String businessKey, String status, String processInstanceId) {
        log.info("💾 更新资产借用状态: 业务ID={}, 状态={}, 流程实例ID={}", businessKey, status, processInstanceId);

        // 创建更新条件
        UpdateWrapper<AmsBorrowFromOtherHospitalsDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", Integer.valueOf(businessKey))
                .set("prosstas", status)
                .set("process_instance_id", processInstanceId);

        // 执行更新
        amsBorrowFromOtherHospitalsWriteService.update(null, updateWrapper);

        log.info("✅ 资产借用状态更新成功: 业务ID={}, 状态={}", businessKey, status);
    }
}