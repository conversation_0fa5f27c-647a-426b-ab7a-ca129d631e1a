package com.jp.med.ams.modules.amsPropertyInAndOut.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;

/**
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-27 20:15:50
 */
public interface AmsOutStockAuditWriteService extends IService<AmsOutStockAuditDto> {
    /**
     * 批量审批
     *
     * @param dto
     * @return
     */
    Object batchApply(AmsOutStockAuditDto dto);
}

