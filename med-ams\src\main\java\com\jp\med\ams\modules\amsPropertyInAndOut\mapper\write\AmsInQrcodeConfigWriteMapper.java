package com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInQrcodeConfigDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资产入库二维码配置
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
@Mapper
public interface AmsInQrcodeConfigWriteMapper extends BaseMapper<AmsInQrcodeConfigDto> {
}
