package com.jp.med.ams.modules.it.service.read.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.mapper.read.AmsItInvtCfgReadMapper;
import com.jp.med.ams.modules.it.service.read.AmsItInvtCfgReadService;
import com.jp.med.ams.modules.it.vo.AmsItInvtCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.it.mapper.read.AmsItInvtOrgConsumableReadMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtOrgConsumableDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo;
import com.jp.med.ams.modules.it.service.read.AmsItInvtOrgConsumableReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsItInvtOrgConsumableReadServiceImpl extends ServiceImpl<AmsItInvtOrgConsumableReadMapper, AmsItInvtOrgConsumableDto> implements AmsItInvtOrgConsumableReadService {

    @Autowired
    private AmsItInvtOrgConsumableReadMapper AmsItInvtOrgConsumableReadMapper;

    @Autowired
    private AmsItInvtCfgReadMapper amsItInvtCfgReadMapper;

    @Override
    public List<AmsItInvtOrgConsumableVo> queryList(AmsItInvtOrgConsumableDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return AmsItInvtOrgConsumableReadMapper.queryList(dto);
    }

    @Override
    public List<AmsItInvtCfgVo>  findEquType() {
        AmsItInvtCfgDto amsItInvtCfgDto = new AmsItInvtCfgDto();
        amsItInvtCfgDto.setType("1");
//        HashMap<String, String> EquTypeOption = new HashMap<>();
        List<AmsItInvtCfgVo> amsItInvtCfgVos = amsItInvtCfgReadMapper.queryList(amsItInvtCfgDto);

        return amsItInvtCfgVos;

    }

    @Override
    public List<AmsItInvtCfgVo>  findEquName() {
        AmsItInvtCfgDto amsItInvtCfgDto = new AmsItInvtCfgDto();
        amsItInvtCfgDto.setType("5");
        return amsItInvtCfgReadMapper.queryList(amsItInvtCfgDto);
    }

    @Override
    public List<AmsItInvtCfgVo>  findConsumName() {
        AmsItInvtCfgDto amsItInvtCfgDto = new AmsItInvtCfgDto();
        amsItInvtCfgDto.setType("6");
        return amsItInvtCfgReadMapper.queryList(amsItInvtCfgDto);
    }

    /**
     * 根据科室id查找相应配置
     * */
    @Override
    public List<AmsItInvtOrgConsumableVo> findcfgByOrgId(AmsItInvtOrgConsumableDto dto) {

       return AmsItInvtOrgConsumableReadMapper.selectByOrgId(dto.getOrgId());
    }

    /**
     * 查找完整名称
     * */
    @Override
    public List<AmsItInvtOrgConsumableVo> SearchOrgList(AmsItInvtOrgConsumableDto dto) {
         dto.setOrgId(dto.getSysUser().getHrmUser().getHrmOrgId());
        return AmsItInvtOrgConsumableReadMapper.selectOrgList(dto) ;
    }
}
