package com.jp.med.ams.modules.changes.strategy.approval;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;

import java.util.List;

/**
 * 审批处理器接口
 * 使用策略模式处理不同类型的资产变更审批
 */
public interface ApprovalProcessor {
    
    /**
     * 判断是否支持处理该类型的审批
     * @param changeRecord 变更记录
     * @param approvalDto 审批DTO
     * @return 是否支持
     */
    boolean supports(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto);
    
    /**
     * 处理审批
     * @param changeRecord 变更记录
     * @param approvalDto 审批DTO
     * @param context 审批上下文
     * @return 处理结果
     */
    ApprovalResult process(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto, ApprovalContext context);
    
    /**
     * 审批处理结果
     */
    class ApprovalResult {
        private List<AmsPropertyDto> propertyUpdates;
        private boolean success;
        private String message;
        
        public ApprovalResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public ApprovalResult(List<AmsPropertyDto> propertyUpdates) {
            this.propertyUpdates = propertyUpdates;
            this.success = true;
        }
        
        // Getters and Setters
        public List<AmsPropertyDto> getPropertyUpdates() {
            return propertyUpdates;
        }
        
        public void setPropertyUpdates(List<AmsPropertyDto> propertyUpdates) {
            this.propertyUpdates = propertyUpdates;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
    }
}
