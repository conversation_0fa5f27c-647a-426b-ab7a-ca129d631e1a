package com.jp.med.erp.modules.vcrGen.entity;

import lombok.Data;

/**
 * @description: 凭证内容明细
 */
@Data
public class CertificateDetail {

    /**
     * 会计系统类型 01财务会计 02预算会计
     */
    private String type;
    /**
     * 支付类型代码
     */
    private String zflxdm;

    private String zflxmc;

    private String ksdm;

    private String ksmc;

    /**
     * 摘要
     */
    private String zy;

    private String kmdm;

    private String kmmc;

    /**
     * 借贷标志  借/贷
     */
    private String jdbz;

    /**
     * 金额
     */
    private String je;

    private String gnkmdm;

    private String gnkmmc;

    private String jjkmdm;

    private String jjkmmc;

    /**
     * 凭证id，hrp 唯一id
     */
    private String pzid;

    /**
     * 日期
     */
    private String rq;

    private String wldwdm;

    private String wldwmc;

    private String xjlldm;

    private String xjllmc;

    private String xmdm;

    private String xmmc;

    /** 资金性质 **/
    private String zjxzdm;
}
