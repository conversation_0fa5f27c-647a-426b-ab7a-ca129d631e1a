package com.jp.med.ams.modules.changes.service.notification;

import com.jp.med.ams.modules.changes.dto.AmsBorrowingAuditNotificationDto;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;

/**
 * 资产借用消息通知服务接口
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
public interface AmsBorrowingNotificationService {

    /**
     * 发送借用申请提交通知
     * 给审核人发送有新的借用申请需要审核的通知
     *
     * @param borrowingRecord 借用记录
     */
    void sendApplicationSubmittedNotification(AmsChngBrwgVo borrowingRecord);

    /**
     * 发送借用申请通过通知
     * 给申请人发送借用申请已通过的通知
     *
     * @param borrowingRecord 借用记录
     */
    void sendApplicationApprovedNotification(AmsChngBrwgVo borrowingRecord);

    /**
     * 发送借用申请通过通知（增强版）
     * 给申请人发送借用申请已通过的通知，包含审核人信息
     *
     * @param borrowingRecord 借用记录
     * @param notificationDto 增强的通知信息
     */
    void sendApplicationApprovedNotification(AmsChngBrwgVo borrowingRecord, AmsBorrowingAuditNotificationDto notificationDto);

    /**
     * 发送借用申请拒绝通知
     * 给申请人发送借用申请被拒绝的通知
     *
     * @param borrowingRecord 借用记录
     * @param rejectReason    拒绝原因
     */
    void sendApplicationRejectedNotification(AmsChngBrwgVo borrowingRecord, String rejectReason);

    /**
     * 发送借用申请拒绝通知（增强版）
     * 给申请人发送借用申请被拒绝的通知，包含审核人信息
     *
     * @param borrowingRecord 借用记录
     * @param notificationDto 增强的通知信息
     */
    void sendApplicationRejectedNotification(AmsChngBrwgVo borrowingRecord, AmsBorrowingAuditNotificationDto notificationDto);

    /**
     * 发送借用成功通知
     * 给借用人发送资产借用成功的通知
     *
     * @param borrowingRecord 借用记录
     */
    void sendBorrowingSuccessNotification(AmsChngBrwgVo borrowingRecord);

    /**
     * 发送借用失败通知
     * 给借用人发送资产借用失败的通知
     *
     * @param borrowingRecord 借用记录
     * @param failureReason   失败原因
     */
    void sendBorrowingFailureNotification(AmsChngBrwgVo borrowingRecord, String failureReason);

    /**
     * 发送归还提醒通知
     * 在预计归还时间前提醒借用人归还资产
     *
     * @param borrowingRecord  借用记录
     * @param daysBeforeExpiry 距离到期天数
     */
    void sendReturnReminderNotification(AmsChngBrwgVo borrowingRecord, int daysBeforeExpiry);

    /**
     * 发送逾期警告通知
     * 给借用人和管理员发送资产逾期未归还的警告
     *
     * @param borrowingRecord 借用记录
     * @param overdueDays     逾期天数
     */
    void sendOverdueWarningNotification(AmsChngBrwgVo borrowingRecord, int overdueDays);

    /**
     * 发送归还确认通知
     * 给借用人发送资产归还确认的通知
     *
     * @param borrowingRecord 借用记录
     */
    void sendReturnConfirmationNotification(AmsChngBrwgVo borrowingRecord);

    /**
     * 发送归还确认通知（增强版）
     * 给借用人发送资产归还确认的通知，包含确认人信息
     *
     * @param borrowingRecord 借用记录
     * @param notificationDto 增强的通知信息
     */
    void sendReturnConfirmationNotification(AmsChngBrwgVo borrowingRecord, AmsBorrowingAuditNotificationDto notificationDto);

    /**
     * 发送续期申请通知
     * 给审核人发送借用续期申请的通知
     *
     * @param borrowingRecord       借用记录
     * @param extensionReason       续期原因
     * @param newExpectedReturnTime 新的预计归还时间
     */
    void sendExtensionRequestNotification(AmsChngBrwgVo borrowingRecord, String extensionReason, String newExpectedReturnTime);

    /**
     * 发送续期申请通过通知
     * 给申请人发送续期申请已通过的通知
     *
     * @param borrowingRecord       借用记录
     * @param newExpectedReturnTime 新的预计归还时间
     */
    void sendExtensionApprovedNotification(AmsChngBrwgVo borrowingRecord, String newExpectedReturnTime);

    /**
     * 发送续期申请拒绝通知
     * 给申请人发送续期申请被拒绝的通知
     *
     * @param borrowingRecord 借用记录
     * @param rejectReason    拒绝原因
     */
    void sendExtensionRejectedNotification(AmsChngBrwgVo borrowingRecord, String rejectReason);

    /**
     * 批量发送到期提醒通知
     * 定时任务调用，批量检查即将到期的借用记录并发送提醒
     */
    void sendBatchExpiryReminders();

    /**
     * 批量发送逾期警告通知
     * 定时任务调用，批量检查逾期的借用记录并发送警告
     */
    void sendBatchOverdueWarnings();

    /**
     * 发送系统维护通知
     * 给所有相关用户发送系统维护通知
     *
     * @param maintenanceMessage 维护消息
     * @param startTime          维护开始时间
     * @param endTime            维护结束时间
     */
    void sendSystemMaintenanceNotification(String maintenanceMessage, String startTime, String endTime);
}
