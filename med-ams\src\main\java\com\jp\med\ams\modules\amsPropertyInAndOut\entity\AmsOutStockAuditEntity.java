package com.jp.med.ams.modules.amsPropertyInAndOut.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-27 20:15:50
 */
@Data
@TableName("ams_out_stock_audit")
public class AmsOutStockAuditEntity {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 审核批次号
     */
    @TableField("bchno")
    private String bchno;

    /**
     * 资产表id
     */
    @TableField("property_id")
    private String propertyId;

    /**
     * 出库科室
     */
    @TableField("out_dept")
    private String outDept;

    /**
     * 入库科室
     */
    @TableField("in_dept")
    private String inDept;

    /**
     * 出库建人
     */
    @TableField("out_user")
    private String outUser;

    /**
     * 接收人
     */
    @TableField("rec_user")
    private String recUser;

    /**
     * 出库时间
     */
    @TableField("out_time")
    private Date outTime;

    /**
     * 接收时间
     */
    @TableField("in_time")
    private Date inTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 接收注释
     */
    @TableField("rec_remarks")
    private String recRemarks;

    /**
     * $column.comments
     */
    @TableField("prosstas")
    private String prosstas;

    /**
     * $column.comments
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * $column.comments
     */
    @TableField("active_flag")
    private String activeFlag;

}
