package com.jp.med.erp.modules.vcrGen.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.vo.EcsReimDeprTaskVo;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpVcrDetailDto;
import com.jp.med.erp.modules.vcrGen.entity.Certificate;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimSalaryTaskVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimTravelApprVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpVcrDetailVo;

import java.util.List;
import java.util.Map;

/**
 * 凭证信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
public interface ErpVcrDetailReadService extends IService<ErpVcrDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrDetailVo> queryList(ErpVcrDetailDto dto);

    /**
     * 查询生成凭证列表
     * @param dto
     * @return
     */
    List<ErpReimDetailVo> queryVcrGenList(ErpVcrDetailDto dto);

    /**
     * 查询申请信息 by Ids
     * @param dto
     * @return
     */
    List<ErpReimTravelApprVo> queryApprInfo(ErpVcrDetailDto dto);

    /**
     * 查询报销信息 by Ids
     * @param dto
     * @return
     */
    List<ErpReimDetailVo> queryReimInfo(ErpVcrDetailDto dto);

    /**
     * 查询凭证信息
     * @param dto
     * @return
     */
    Map<String,Object> queryVcr(ErpVcrDetailDto dto);

    Map<String, Object> queryAsst(Certificate dto);

    List<ErpReimDetailVo> drugToVcrList(ErpVcrDetailDto dto);

    List<ErpReimSalaryTaskVo> salaryToVcrList(ErpVcrDetailDto dto);

    List<EcsReimDeprTaskVo> deprToVcrList(ErpVcrDetailDto dto);

    List<ErpVcrSalaryConfigVo> queryToConfigSalary(ErpVcrDetailDto dto);

    String queryDrugVpzh(ErpVcrDetailDto dto);
}

