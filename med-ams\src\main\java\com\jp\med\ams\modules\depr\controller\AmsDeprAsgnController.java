package com.jp.med.ams.modules.depr.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.jp.med.ams.modules.depr.service.read.AmsDeprAsgnReadService;
import com.jp.med.ams.modules.depr.service.write.AmsDeprAsgnWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 资产折旧分配
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Api(value = "资产折旧分配", tags = "资产折旧分配")
@RestController
@RequestMapping("amsDeprAsgn")
public class AmsDeprAsgnController {

    @Autowired
    private AmsDeprAsgnReadService amsDeprAsgnReadService;

    @Autowired
    private AmsDeprAsgnWriteService amsDeprAsgnWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产折旧分配")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsDeprAsgnDto dto){
        return CommonResult.paging(amsDeprAsgnReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产折旧分配")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsDeprAsgnDto dto){
        amsDeprAsgnWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产折旧分配")
    @PostMapping("/update")
    public CommonResult<?> update(@RequestBody AmsDeprAsgnDto dto){
        amsDeprAsgnWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产折旧分配")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsDeprAsgnDto dto){
        amsDeprAsgnWriteService.removeById(dto);
        return CommonResult.success();
    }

}
