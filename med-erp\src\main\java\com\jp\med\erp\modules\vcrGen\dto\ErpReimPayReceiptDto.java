package com.jp.med.erp.modules.vcrGen.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 费用报销付款回单信息
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 16:02:12
 */
@Data
@TableName("erp_reim_pay_receipt" )
public class ErpReimPayReceiptDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 报销id */
    @TableField("reim_detail_id")
    private Integer reimDetailId;

    /** 报销id */
    @TableField(exist = false)
    private List<Integer> reimDetailIds;

    /** 对应生成辅助项id **/
    @TableField("reim_asst_id")
    private Integer reimAsstId;

    /** 文件路径 */
    @TableField("att")
    private String att;

    /** 文件名 */
    @TableField("att_name")
    private String attName;

    /** 付款日期 */
    @TableField("pay_date")
    private String payDate;

    /** 付款金额 */
    @TableField("pay_amt")
    private BigDecimal payAmt;

    /** 状态 1：识别成功 2：识别失败 3：手动修改 */
    @TableField("status")
    private String status;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("crter_time")
    private String crterTime;

    /** 更新人 */
    @TableField("upder")
    private String upder;

    /** 更新时间 */
    @TableField("upder_time")
    private String upderTime;

    /** 报销id */
    @TableField(exist = false)
    private List<Integer> ids;

    /** 字符金额字符串，用于OCR识别后放入 **/
    @TableField(exist = false)
    private String payAmtStr;

    /** 发票类型 **/
    @TableField("sup_type")
    private String supType;
}
