package com.jp.med.ams.modules.changes.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.changes.dto.AmsAllocDto;
import com.jp.med.ams.modules.changes.mapper.read.AmsAllocReadMapper;
import com.jp.med.ams.modules.changes.mapper.write.AmsAllocWriteMapper;
import com.jp.med.ams.modules.changes.service.write.AmsAllocWriteService;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.context.UserContext;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.ULIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 资产划拨
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
@Service
@Transactional(readOnly = false)
public class AmsAllocWriteServiceImpl extends ServiceImpl<AmsAllocWriteMapper, AmsAllocDto>
        implements AmsAllocWriteService {

    @Autowired
    private AmsAllocWriteMapper amsAllocWriteMapper;
    @Autowired
    private AmsAllocReadMapper amsAllocReadMapper;
    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    public void saveAlloc(AmsAllocDto dto) {
        // 1、生成审核批次号
        String ulid = ULIDUtil.generate();
        String bchno = AuditConst.AMS_ALLOC_APPLY + ulid;

        dto.setBchno(bchno);
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        // 申请中
        dto.setProsstas(MedConst.TYPE_1);

        // 划拨申请校验
        dto.setSqlAutowiredHospitalCondition(true);
        if (amsAllocReadMapper.validateAllocApply(dto)) {
            throw new AppException("当前资产处于转移或划拨中");
        }

        // 划拨申请详情
        amsAllocWriteMapper.insert(dto);

        // 处理批量申请
        amsAllocWriteMapper.insertAllocPropertyRef(dto);

        // 2、构建审核app消息
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            HrmUser hrmUser = dto.getSysUser().getHrmUser();
            String appyer = UserContext.getEmpCode();
            String appyerName = UserContext.getEmpName();

            AppMsgSup appMsgSup = new AppMsgSup();
            var title = "";
            if (StrUtil.equals(dto.getType(), "1")) {
                title = "固定资产资产划拨申请";
            } else if (StrUtil.equals(dto.getType(), "2")) {
                title = "低值易耗品资产划拨申请";
            }
            appMsgSup.setTitle(title);
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);

            appMsgSup.setContent(
                    "[" + dto.getTrafOutDeptName() + "]申请[" + dto.getFaCodeName() + "划拨到" + dto.getTrafInDeptName());

            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(bchno);
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("trafOutDeptName", "转出科室");
            map.put("trafInDeptName", "转入科室");

            map.put("faCodeName", "转出资产");
            auditPayload.setDisplayItem(map);

            var temp = new AuditDetail(bchno, dto.getAuditDetails(), appMsgSup, auditPayload,
                    OSSConst.BUCKET_AMS);
            temp.setRoutePath(dto.getRoutePath());
            // 远程调用 加入审核表
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(temp));

        }
    }

    /**
     * 删除资产划拨记录
     *
     * @param dto
     */
    @Override
    public void removeAlloc(AmsAllocDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        boolean canDel = amsAllocReadMapper.validateAllocCanDel(dto);
        if (canDel) {
            this.removeById(dto.getId());
        } else {
            throw new AppException("划拨正在进行中无法删除");
        }
    }

}
