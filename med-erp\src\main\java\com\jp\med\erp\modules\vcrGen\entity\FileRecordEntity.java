package com.jp.med.erp.modules.vcrGen.entity;

import lombok.Data;

/**
 * 文件记录表
 */
@Data
public class FileRecordEntity {

    /** id */
    private Integer id;

    /** 附件(minio路径) */
    private String att;

    /** 附件名称 */
    private String attName;

    /** 业务类型 */
    private String type;

    /** 对应业务关联主键(模块前缀+随机数) */
    private String attCode;

    /** 删除标志(0:未删除，1:已删除) */
    private String flag;
}
