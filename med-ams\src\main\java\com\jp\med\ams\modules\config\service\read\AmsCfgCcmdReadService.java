package com.jp.med.ams.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.config.dto.AmsCfgCcmdDto;
import com.jp.med.ams.modules.config.vo.AmsCfgCcmdVo;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 09:35:21
 */
public interface AmsCfgCcmdReadService extends IService<AmsCfgCcmdDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsCfgCcmdVo> queryList(AmsCfgCcmdDto dto);
}

