package com.jp.med.ams.modules.config.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 新资产分类
 * <AUTHOR>
 * @email -
 * @date 2023-12-06 11:20:53
 */
@Data
@TableName("ams_typen_cfg" )
public class AmsTypenCfgDto extends CommonQueryDto{

    /** ID */
    @TableId("id")
    private Integer id;

    /** 类型编码 */
    @TableField(value = "asset_type_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String assetTypeCode;

    /** 类型名称 */
    @TableField(value = "asset_type_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String assetTypeName;

    /** 上级编码 */
    @TableField(value = "parent_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String parentCode;

    /** 建议使用年限 */
    @TableField(value = "years",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String years;

    /** 折旧方式编码 */
    @TableField(value = "depr_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String deprCode;

    /** 计量单位 */
    @TableField(value = "unit",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String unit;

    /** 残值率 */
    @TableField(value = "resr",insertStrategy = FieldStrategy.NOT_EMPTY)
    private BigDecimal resr;

    /** 说明 */
    @TableField(value = "dscr",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String dscr;

    /** 卡片配置 */
    @TableField(value = "stock_cfg",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String stockCfg;

    /** 有效标志 */
    @TableField(value = "flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String flag;

}
