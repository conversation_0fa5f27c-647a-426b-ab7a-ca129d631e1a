package com.jp.med.ams.modules.changes.feign;

import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @BelongsProject: mednback
 * @BelongsPackage: com.jp.med.bms.modules.dispose.fegin
 * @Author: artist
 * @CreateTime: 2023-04-24
 * @Description:
 * @Version: 1.0
 */
@RefreshScope
@FeignClient(name = "SysMessageFeignService",url = "${custom.gateway.med-core-service-uri}")
public interface AmsMessageFeignService {

    /**
     * 发送消息
     * @param dto
     * @return
     */
    @PostMapping("sys/message/save")
    CommonFeignResult sendMessage(@RequestBody SysMessageDto dto);
}
