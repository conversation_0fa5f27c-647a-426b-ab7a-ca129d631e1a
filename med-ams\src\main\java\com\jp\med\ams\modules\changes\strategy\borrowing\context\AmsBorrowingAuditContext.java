package com.jp.med.ams.modules.changes.strategy.borrowing.context;

import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.common.entity.user.SysUser;

import java.util.List;
import java.util.Map;

/**
 * 资产借用审核上下文
 * 包含审核过程中需要的所有信息
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
public class AmsBorrowingAuditContext {

    /**
     * 当前审核人信息
     */
    private SysUser auditor;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 审核人科室
     */
    private String auditorDept;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 借用记录列表（批量审核时使用）
     */
    private List<AmsChngBrwgVo> borrowingRecords;

    /**
     * 借用记录映射（ID -> 记录）
     */
    private Map<Integer, AmsChngBrwgVo> borrowingRecordMap;

    /**
     * 审核类型
     * 1: 审核通过/确认
     * 2: 审核拒绝/驳回
     * 3: 续期申请
     */
    private String auditType;

    /**
     * 目标流程状态
     */
    private String targetProcessStatus;

    /**
     * 是否批量操作
     */
    private boolean batchOperation;

    /**
     * 扩展参数
     */
    private Map<String, Object> extendParams;

    // 构造函数
    public AmsBorrowingAuditContext() {
    }

    public AmsBorrowingAuditContext(SysUser auditor, String auditType) {
        this.auditor = auditor;
        this.auditType = auditType;
        if (auditor != null && auditor.getHrmUser() != null) {
            this.auditorName = auditor.getHrmUser().getEmpName();
            this.auditorDept = auditor.getHrmUser().getHrmOrgName();
        }
    }

    // Getter and Setter methods
    public SysUser getAuditor() {
        return auditor;
    }

    public void setAuditor(SysUser auditor) {
        this.auditor = auditor;
        if (auditor != null && auditor.getHrmUser() != null) {
            this.auditorName = auditor.getHrmUser().getEmpName();
            this.auditorDept = auditor.getHrmUser().getHrmOrgName();
        }
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getAuditorDept() {
        return auditorDept;
    }

    public void setAuditorDept(String auditorDept) {
        this.auditorDept = auditorDept;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    public List<AmsChngBrwgVo> getBorrowingRecords() {
        return borrowingRecords;
    }

    public void setBorrowingRecords(List<AmsChngBrwgVo> borrowingRecords) {
        this.borrowingRecords = borrowingRecords;
    }

    public Map<Integer, AmsChngBrwgVo> getBorrowingRecordMap() {
        return borrowingRecordMap;
    }

    public void setBorrowingRecordMap(Map<Integer, AmsChngBrwgVo> borrowingRecordMap) {
        this.borrowingRecordMap = borrowingRecordMap;
    }

    public String getAuditType() {
        return auditType;
    }

    public void setAuditType(String auditType) {
        this.auditType = auditType;
    }

    public String getTargetProcessStatus() {
        return targetProcessStatus;
    }

    public void setTargetProcessStatus(String targetProcessStatus) {
        this.targetProcessStatus = targetProcessStatus;
    }

    public boolean isBatchOperation() {
        return batchOperation;
    }

    public void setBatchOperation(boolean batchOperation) {
        this.batchOperation = batchOperation;
    }

    public Map<String, Object> getExtendParams() {
        return extendParams;
    }

    public void setExtendParams(Map<String, Object> extendParams) {
        this.extendParams = extendParams;
    }
}
