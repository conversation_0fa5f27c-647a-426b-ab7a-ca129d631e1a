package com.jp.med.ams.modules.changes.controller;

import com.jp.med.ams.modules.changes.dto.AmsAllocDto;
import com.jp.med.ams.modules.changes.service.read.AmsAllocReadService;
import com.jp.med.ams.modules.changes.service.write.AmsAllocWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 资产划拨
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
@Api(value = "资产划拨", tags = "资产划拨")
@RestController
@RequestMapping("amsAlloc")
public class AmsAllocController {

    @Autowired
    private AmsAllocReadService amsAllocReadService;

    @Autowired
    private AmsAllocWriteService amsAllocWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产划拨")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsAllocDto dto){
        return CommonResult.paging(amsAllocReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产划拨")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsAllocDto dto){
        amsAllocWriteService.saveAlloc(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产划拨")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsAllocDto dto){
        amsAllocWriteService.removeAlloc(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询划拨资产详情")
    @PostMapping("/propertyDetail")
    public CommonResult<?> propertyDetail(@RequestBody AmsAllocDto dto){

        return CommonResult.success(amsAllocReadService.queryPropertyDetail(dto));
    }

    @ApiOperation("查询划拨待审核数量")
    @PostMapping("/queryAuditCount")
    public CommonResult<?> queryAuditCount(@RequestBody AmsAllocDto dto){
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(amsAllocReadService.queryAuditCount(dto));
    }
}
