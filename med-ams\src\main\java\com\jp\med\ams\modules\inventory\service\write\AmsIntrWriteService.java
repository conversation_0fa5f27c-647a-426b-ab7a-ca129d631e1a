package com.jp.med.ams.modules.inventory.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;

import java.util.Map;

/**
 * 资产盘点表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
public interface AmsIntrWriteService extends IService<AmsIntrDto> {
    /**
     * 写入盘点数据
     * @param map
     */
    void addIntr(Map<String, Object> map);

    /**
     * 数据同步
     * @param dto
     */
    Integer synchronousData(AmsIntrTodoDto dto);

    /**
     * 数据同步（增强版）
     *
     * @param dto 盘点数据DTO
     * @return 包含总数量、新增数量、重复数量、新增UID列表的详细结果
     */
    Map<String, Object> synchronousDataEnhanced(AmsIntrTodoDto dto);

    AmsIntrDto startIntr(AmsIntrDto dto);
}

