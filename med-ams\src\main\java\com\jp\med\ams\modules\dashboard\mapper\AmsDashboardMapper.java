package com.jp.med.ams.modules.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.dashboard.dto.AmsDashboardFilterDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 资产盘点仪表盘Mapper
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Mapper
public interface AmsDashboardMapper extends BaseMapper<AmsDashboardFilterDto> {

    /**
     * 统计总任务数
     */
    @Select({
            "<script>",
            "SELECT COUNT(1) FROM ams_intr_task",
            "WHERE flag = '1' AND hospital_id = #{hospitalId}",
            "<if test='year != null'>",
            "AND YEAR(create_time) = #{year}",
            "</if>",
            "<if test='departments != null and departments.size() > 0'>",
            "AND dept IN",
            "<foreach collection='departments' item='dept' open='(' separator=',' close=')'>",
            "#{dept}",
            "</foreach>",
            "</if>",
            "<if test='startDate != null and endDate != null'>",
            "AND create_time BETWEEN #{startDate} AND #{endDate}",
            "</if>",
            "<if test='status != null'>",
            "AND status = #{status}",
            "</if>",
            "</script>"
    })
    Integer countTotalTasks(@Param("hospitalId") String hospitalId,
                            @Param("year") Integer year,
                            @Param("departments") List<String> departments,
                            @Param("startDate") String startDate,
                            @Param("endDate") String endDate,
                            @Param("status") String status);

    /**
     * 默认方法，使用对象参数
     */
    default Integer countTotalTasks(AmsDashboardFilterDto filterDto) {
        return countTotalTasks(filterDto.getHospitalId(), filterDto.getYear(),
                filterDto.getDepartments(), filterDto.getStartDate(),
                filterDto.getEndDate(), filterDto.getStatus());
    }

    /**
     * 获取资产统计数据
     */
    Map<String, Object> getAssetStatistics(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取完成率趋势数据
     */
    List<Map<String, Object>> getCompletionTrendData(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取科室统计数据
     */
    List<Map<String, Object>> getDepartmentStatsData(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取资产类型分布数据
     */
    List<Map<String, Object>> getAssetTypeDistributionData(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取盘盈盘亏对比数据
     */
    List<Map<String, Object>> getProfitLossComparisonData(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取月度任务统计数据
     */
    List<Map<String, Object>> getMonthlyTasksData(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取异常资产分析数据
     */
    Map<String, Object> getAbnormalAssetsData(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取表格数据
     */
    List<Map<String, Object>> getTableData(@Param("filter") AmsDashboardFilterDto filterDto);

    /**
     * 获取部门选项
     */
    @Select({
            "SELECT DISTINCT dept AS value, dept AS label",
            "FROM ams_intr_task",
            "WHERE flag = '1' AND hospital_id = #{hospitalId}",
            "AND dept IS NOT NULL AND dept != ''",
            "ORDER BY dept"
    })
    List<Map<String, Object>> getDepartmentOptions(@Param("hospitalId") String hospitalId);

    /**
     * 获取资产类型选项
     */
    @Select({
            "SELECT DISTINCT asset_type_n_code AS value, asset_type_n_name AS label",
            "FROM ams_property",
            "WHERE flag = '1' AND hospital_id = #{hospitalId}",
            "AND asset_type_n_code IS NOT NULL",
            "ORDER BY asset_type_n_name"
    })
    List<Map<String, Object>> getAssetTypeOptions(@Param("hospitalId") String hospitalId);
} 