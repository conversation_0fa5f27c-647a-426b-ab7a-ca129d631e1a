package com.jp.med.ams.modules.depr.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.depr.dto.AmsDeprDeptDto;
import com.jp.med.ams.modules.depr.vo.AmsDeprDeptVo;

import java.util.List;

/**
 * 存储部门信息，包括折旧额和其他相关数据
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-19 16:16:26
 */
public interface AmsDeprDeptReadService extends IService<AmsDeprDeptDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsDeprDeptVo> queryList(AmsDeprDeptDto dto);

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    List<AmsDeprDeptVo> queryPageList(AmsDeprDeptDto dto);
}

