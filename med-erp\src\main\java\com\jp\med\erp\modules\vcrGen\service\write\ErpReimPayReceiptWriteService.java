package com.jp.med.erp.modules.vcrGen.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimPayReceiptDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;

import java.util.List;

/**
 * 费用报销付款回单信息
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 16:02:12
 */
public interface ErpReimPayReceiptWriteService extends IService<ErpReimPayReceiptDto> {
    List<ErpReimPayReceiptVo> recogReceipt(ErpReimPayReceiptDto dto);
}

