package com.jp.med.ams.modules.amsPropertyInAndOut.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 资产入库二维码配置
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
@Data
@TableName("ams_in_qrcode_config")
public class AmsInQrcodeConfigEntity {


    @TableId("id")
	private Integer id;

	/** 名称 */
	@TableField("qr_code_name")
	private String qrCodeName;

	/** 描述 */
	@TableField("description")
	private String description;

	/** 有效开始时间 */
	@TableField("start_date")
	private Date startDate;

	/** 有效结束时间 */
	@TableField("end_date")
	private Date endDate;

	/** 二维码图片base64编码格式 */
	@TableField("qr_code_base64")
	private String qrCodeBase64;

	/** 创建人 */
	@TableField("create_user")
	private String createUser;

}
