package com.jp.med.ams.modules.config.controller;

import com.jp.med.ams.modules.config.dto.AmsStockCfgDto;
import com.jp.med.ams.modules.config.service.read.AmsStockCfgReadService;
import com.jp.med.ams.modules.config.service.write.AmsStockCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("amsStockCfg")
public class AmsStockCfgController {


    @Autowired
    private AmsStockCfgReadService amsStockCfgReadService;

    @Autowired
    private AmsStockCfgWriteService amsStockCfgWriteService;


    @PostMapping("/queryTableColumn")
    public CommonResult<?> queryTableColumn(){
        return CommonResult.success(amsStockCfgReadService.queryTableColumn());
    }

    /**
     * 列表
     */
    @ApiOperation("查询资产卡片配置")
    @PostMapping("/queryCard")
    public CommonResult<?> queryCard(@RequestBody AmsStockCfgDto dto){
        return CommonResult.success(amsStockCfgReadService.queryCard(dto));
    }

    /**
     * 删除卡片
     */
    @ApiOperation("删除资产卡片配置")
    @PostMapping("/deleteCard")
    public CommonResult<?> deleteCard(@RequestBody AmsStockCfgDto dto) {
        List<AmsStockCfgDto> list = amsStockCfgWriteService.lambdaQuery().eq(AmsStockCfgDto::getCardCode, dto.getCardCode()).list();
        for (AmsStockCfgDto amsStockCfgDto : list) {

        }
        amsStockCfgWriteService.removeBatchByIds(list.stream().map(AmsStockCfgDto::getId).collect(Collectors.toList()));
        return CommonResult.success();
    }

    /**
     * 列表
     */
    @ApiOperation("查询资产卡片配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsStockCfgDto dto){
        return CommonResult.success(amsStockCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产卡片配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsStockCfgDto dto){
        amsStockCfgWriteService.save(dto);
        return CommonResult.success();
    }


}
