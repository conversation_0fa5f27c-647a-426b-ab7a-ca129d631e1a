package com.jp.med.ams.modules.it.mapper.write;

import com.jp.med.ams.modules.it.dto.AmsItApplyDetailDto;
import com.jp.med.ams.modules.it.dto.AmsItInvtAddDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *  耗材入库日志
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItInvtAddWriteMapper extends BaseMapper<AmsItInvtAddDto> {

}
