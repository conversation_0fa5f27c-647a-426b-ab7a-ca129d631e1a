package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base;

import lombok.extern.slf4j.Slf4j;

/**
 * 资产折旧处理器抽象基类
 * 实现责任链模式的基础结构
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
public abstract class AbstractAmsPropertyDeprProcessor<T> {

    /** 下一个处理器 */
    protected AbstractAmsPropertyDeprProcessor<T> nextProcessor;

    /**
     * 设置下一个处理器
     * 
     * @param nextProcessor 下一个处理器
     * @return 下一个处理器（支持链式调用）
     */
    public AbstractAmsPropertyDeprProcessor<T> setNext(AbstractAmsPropertyDeprProcessor<T> nextProcessor) {
        this.nextProcessor = nextProcessor;
        return nextProcessor;
    }

    /**
     * 处理请求的入口方法
     * 
     * @param context 处理上下文
     */
    public final void handle(T context) {
        try {
            log.debug("开始执行处理器: {}", this.getClass().getSimpleName());

            // 执行当前处理器的业务逻辑
            doProcess(context);

            log.debug("完成执行处理器: {}", this.getClass().getSimpleName());

            // 传递给下一个处理器
            if (nextProcessor != null) {
                nextProcessor.handle(context);
            }

        } catch (Exception e) {
            log.error("处理器 {} 执行失败", this.getClass().getSimpleName(), e);
            throw e;
        }
    }

    /**
     * 具体的业务处理逻辑
     * 子类需要实现此方法
     * 
     * @param context 处理上下文
     */
    protected abstract void doProcess(T context);

    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    public String getProcessorName() {
        return this.getClass().getSimpleName();
    }
}
