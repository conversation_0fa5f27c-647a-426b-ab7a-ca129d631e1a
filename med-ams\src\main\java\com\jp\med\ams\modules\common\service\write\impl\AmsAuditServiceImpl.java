package com.jp.med.ams.modules.common.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.read.AmsOutStockAuditReadMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write.AmsOutStockAuditWriteMapper;
import com.jp.med.ams.modules.changes.feign.AmsMessageFeignService;
import com.jp.med.ams.modules.changes.mapper.write.AmsAllocWriteMapper;
import com.jp.med.ams.modules.changes.mapper.write.AmsTransferWriteMapper;
import com.jp.med.ams.modules.common.service.write.AmsAuditService;
import com.jp.med.ams.modules.property.dto.AmsScrapApplyDto;
import com.jp.med.ams.modules.property.dto.AmsScrapDto;
import com.jp.med.ams.modules.property.mapper.read.AmsScrapApplyReadMapper;
import com.jp.med.ams.modules.property.mapper.read.AmsScrapReadMapper;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import com.jp.med.ams.modules.property.mapper.write.AmsScrapApplyWriteMapper;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.util.FeignExecuteUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description //TODO
 * <AUTHOR>
 * @Date 2024/4/12 16:36
 */
@Service
@Transactional(readOnly = false)
public class AmsAuditServiceImpl implements AmsAuditService {

    @Autowired
    AmsTransferWriteMapper amsTransferWriteMapper;

    @Autowired
    AmsAllocWriteMapper amsAllocWriteMapper;

//    @Autowired
//    AmsPropertyWriteService amsPropertyWriteService;

    @Resource
    AmsOutStockAuditWriteMapper amsOutStockAuditWriteMapper;

    @Resource
    AmsScrapApplyWriteMapper amsScrapApplyWriteMapper;

    @Resource
    AmsMessageFeignService amsMessageFeignService;

    @Resource
    AmsOutStockAuditReadMapper amsOutStockAuditReadMapper;
    @Qualifier("amsScrapApplyReadMapper")
    @Autowired
    private AmsScrapApplyReadMapper amsScrapApplyReadMapper;

    @Autowired
    private AmsScrapReadMapper amsScrapReadMapper;
    @Qualifier("amsPropertyWriteMapper")
    @Autowired
    private AmsPropertyWriteMapper amsPropertyWriteMapper;

    @Override
    public void complete(AuditDetail dto) {
        String bchno = dto.getAuditBchno();
        String auditResult = dto.getAuditResult();
        if (StringUtils.isNotEmpty(auditResult) && StringUtils.isNotEmpty(bchno)) {
            switch (bchno.substring(0, 6)) {
                // 资产转移审核
                case AuditConst.AMS_TRANSFER_APPLY:
                    transfer(dto, auditResult, bchno);
                    break;
                // 划拨
                case AuditConst.AMS_ALLOC_APPLY:
                    alloc(dto, auditResult, bchno);
                    break;
                // 出库
                case AuditConst.AMS_OUT_APPLY:
                    out(dto, auditResult, bchno);
                    break;
                // 报废
                // 出库
                case AuditConst.AMS_SCRAP_APPLY:
                    scrap(dto, auditResult, bchno);
                    break;
                default:
                    break;
            }
        }
    }

    private void scrap(AuditDetail dto, String auditResult, String bchno) {
        switch (auditResult) {
            case AuditConst.STATE_COMPLETE:
                // 更新申请记录状态
                amsScrapApplyWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_3);

                // 报废资产
                LambdaQueryWrapper<AmsScrapApplyDto> eq = Wrappers.lambdaQuery(AmsScrapApplyDto.class)
                        .eq(AmsScrapApplyDto::getBchno, bchno);
                AmsScrapApplyDto amsScrapApplyDto = amsScrapApplyReadMapper.selectOne(eq);
                LambdaQueryWrapper<AmsScrapDto> scrapDtoLambdaQueryWrapper = Wrappers.lambdaQuery(AmsScrapDto.class)
                        .eq(AmsScrapDto::getApplyDocno, amsScrapApplyDto.getApplyDocno())
                        .eq(AmsScrapDto::getAllo, MedConst.ACTIVE_FLAG_1);
                List<AmsScrapDto> amsScrapDtos = amsScrapReadMapper.selectList(scrapDtoLambdaQueryWrapper);

                List<String> facodeList = amsScrapDtos.stream().map(AmsScrapDto::getFaCode)
                        .collect(Collectors.toList());
                if (!facodeList.isEmpty()) {
                    amsPropertyWriteMapper.scrap(facodeList);
                }

                break;
            case AuditConst.STATE_SUCCESS:
                // 审核节点成功 （判断节点顺序--修改申请记录状态未--确认中）
                List<AuditDetail> details = dto.getAuditDetails();
                // 获取所有未审批数据
                List<AuditDetail> notAuditDetails = details.stream()
                        .filter(auditDetail -> StringUtils.isEmpty(auditDetail.getChkTime())
                                && MedConst.TYPE_0.equals(
                                        auditDetail.getChkState()))
                        .collect(Collectors.toList());

                if (notAuditDetails.size() <= 2) {
                    // 更新申请记录状态
                    amsScrapApplyWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_2);
                }
                break;

            case AuditConst.STATE_FAIL:
                // 审核节点拒绝
                // 更新申请记录状态
                amsScrapApplyWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_4);
                break;
        }
    }

    private void out(AuditDetail dto, String auditResult, String bchno) {
        // 所有节点完成--成功
        switch (auditResult) {
            case AuditConst.STATE_COMPLETE:
                // 更新申请记录状态
                amsOutStockAuditWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_3);

                var eq = Wrappers.lambdaQuery(AmsOutStockAuditDto.class).eq(AmsOutStockAuditDto::getBchno, bchno);
                AmsOutStockAuditDto amsOutStockAuditDto = amsOutStockAuditReadMapper.selectOne(eq);
                if (amsOutStockAuditDto != null) {
                    String inUserId = String.valueOf(amsOutStockAuditDto.getInUser());
                    SysMessageDto receiveUserMessageDto = new SysMessageDto();
                    List<String> list = Collections.singletonList(inUserId);
                    String[] userIds = new String[1];
                    receiveUserMessageDto.setUsers(list.toArray(userIds));
                    receiveUserMessageDto.setTitle("资产出库接收通知");
                    receiveUserMessageDto.setPushText("有资产出库，请接收");
                    FeignExecuteUtil.execute(amsMessageFeignService.sendMessage(receiveUserMessageDto));

                }

                break;
            case AuditConst.STATE_SUCCESS:
                // // 审核节点成功 （判断节点顺序--修改申请记录状态未--确认中）
                // }
                amsOutStockAuditWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_2);

                break;

            case AuditConst.STATE_FAIL:
                // 审核节点拒绝
                // 更新申请记录状态
                amsOutStockAuditWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_4);
                break;
        }
    }

    private void alloc(AuditDetail dto, String auditResult, String bchno) {
        switch (auditResult) {
            case AuditConst.STATE_COMPLETE:
                // 更新申请记录状态
                amsAllocWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_3);
                // 更新资产使用科室
                amsAllocWriteMapper.updateAssetInfo(bchno);
                break;

            case AuditConst.STATE_SUCCESS:
                // 审核节点成功 （判断节点顺序--修改申请记录状态未--确认中）
                List<AuditDetail> details = dto.getAuditDetails();
                // 获取所有未审批数据
                List<AuditDetail> notAuditDetails = details.stream()
                        .filter(auditDetail -> StringUtils.isEmpty(auditDetail.getChkTime())
                                && MedConst.TYPE_0.equals(
                                        auditDetail.getChkState()))
                        .collect(Collectors.toList());

                if (notAuditDetails.size() <= 2) {
                    // 更新申请记录状态
                    amsAllocWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_2);
                }
                break;

            case AuditConst.STATE_FAIL:
                // 审核节点拒绝
                // 更新申请记录状态
                amsAllocWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_4);
                break;
        }
    }

    private void transfer(AuditDetail dto, String auditResult, String bchno) {
        // 所有节点完成--成功
        switch (auditResult) {
            case AuditConst.STATE_COMPLETE:
                // 更新申请记录状态
                amsTransferWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_3);
                // 更新资产使用科室
                amsTransferWriteMapper.updateAssetInfo(bchno);
                break;

            case AuditConst.STATE_SUCCESS:
                // 审核节点成功 （判断节点顺序--修改申请记录状态未--确认中）
                List<AuditDetail> details = dto.getAuditDetails();
                // 获取所有未审批数据
                List<AuditDetail> notAuditDetails = details.stream()
                        .filter(auditDetail -> StringUtils.isEmpty(auditDetail.getChkTime())
                                && MedConst.TYPE_0.equals(
                                        auditDetail.getChkState()))
                        .collect(Collectors.toList());

                if (notAuditDetails.size() <= 2) {
                    // 更新申请记录状态
                    amsTransferWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_2);
                }
                break;

            case AuditConst.STATE_FAIL:
                // 审核节点拒绝
                // 更新申请记录状态
                amsTransferWriteMapper.updateApplyProsstas(bchno, MedConst.TYPE_4);
                break;
        }
    }
}
