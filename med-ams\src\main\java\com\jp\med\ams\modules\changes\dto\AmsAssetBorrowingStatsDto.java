package com.jp.med.ams.modules.changes.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 资产借用统计查询DTO
 * 注意：用户ID和科室ID通过UserContext自动获取，无需前端传递
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AmsAssetBorrowingStatsDto", description = "资产借用统计查询DTO")
public class AmsAssetBorrowingStatsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "统计开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "统计结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "统计类型：personal-个人统计，dept-科室统计，all-全部统计")
    private String statsType;

    @ApiModelProperty(value = "时间范围类型：today-今日，week-本周，month-本月，year-本年")
    private String timeRange;

    @ApiModelProperty(value = "是否包含历史数据")
    private Boolean includeHistory;

    @ApiModelProperty(value = "资产类型代码 - 用于按资产类型统计")
    private String assetTypeCode;

    @ApiModelProperty(value = "状态过滤：1-申请中，2-审核通过，3-审核不通过，4-借用中，5-已归还")
    private String statusFilter;

    @ApiModelProperty(value = "是否只查询逾期数据")
    private Boolean onlyOverdue;

    @ApiModelProperty(value = "是否只查询即将到期数据")
    private Boolean onlyExpiring;

    @ApiModelProperty(value = "到期天数阈值（用于即将到期统计，默认3天）")
    private Integer expiringDays;

    @ApiModelProperty(value = "分组字段：dept-按科室分组，assetType-按资产类型分组，status-按状态分组")
    private String groupBy;

    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    @ApiModelProperty(value = "排序方向：asc-升序，desc-降序")
    private String orderDirection;

    @ApiModelProperty(value = "限制返回数量（用于Top N统计）")
    private Integer limitCount;

    /**
     * 获取默认的到期天数阈值
     */
    public Integer getExpiringDays() {
        return expiringDays != null ? expiringDays : 3;
    }

    /**
     * 获取默认的统计类型
     */
    public String getStatsType() {
        return statsType != null ? statsType : "personal";
    }

    /**
     * 获取默认的时间范围
     */
    public String getTimeRange() {
        return timeRange != null ? timeRange : "month";
    }

    /**
     * 获取默认的排序方向
     */
    public String getOrderDirection() {
        return orderDirection != null ? orderDirection : "desc";
    }

    /**
     * 是否包含历史数据（默认不包含）
     */
    public Boolean getIncludeHistory() {
        return includeHistory != null ? includeHistory : false;
    }

    /**
     * 是否只查询逾期数据（默认否）
     */
    public Boolean getOnlyOverdue() {
        return onlyOverdue != null ? onlyOverdue : false;
    }

    /**
     * 是否只查询即将到期数据（默认否）
     */
    public Boolean getOnlyExpiring() {
        return onlyExpiring != null ? onlyExpiring : false;
    }

    /**
     * 获取默认限制数量
     */
    public Integer getLimitCount() {
        return limitCount != null ? limitCount : 10;
    }
}
