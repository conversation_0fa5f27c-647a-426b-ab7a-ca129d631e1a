package com.jp.med.ams.modules.config.mapper.read;

import com.jp.med.ams.modules.config.dto.AmsTypenCfgDto;
import com.jp.med.ams.modules.config.vo.AmsTypenCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 新资产分类
 * <AUTHOR>
 * @email -
 * @date 2023-12-06 11:20:53
 */
@Mapper
public interface AmsTypenCfgReadMapper extends BaseMapper<AmsTypenCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsTypenCfgVo> queryList(AmsTypenCfgDto dto);

    /**
     * 通过ID查询上级/下级
     * @param dto
     * @return
     */
    List<AmsTypenCfgVo> queryParentOrChild(AmsTypenCfgDto dto);

    @Select("select * from ams_typen_cfg where parent_code is null or parent_code = ''")
    List<AmsTypenCfgVo> queryParent();
}
