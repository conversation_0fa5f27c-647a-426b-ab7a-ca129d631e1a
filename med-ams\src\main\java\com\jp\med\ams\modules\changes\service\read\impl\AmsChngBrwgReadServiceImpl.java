package com.jp.med.ams.modules.changes.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.changes.mapper.read.AmsChngBrwgReadMapper;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.ams.modules.changes.service.read.AmsChngBrwgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsChngBrwgReadServiceImpl extends ServiceImpl<AmsChngBrwgReadMapper, AmsChngBrwgDto> implements AmsChngBrwgReadService {

    @Autowired
    private AmsChngBrwgReadMapper amsChngBrwgReadMapper;

    @Override
    public List<AmsChngBrwgVo> queryList(AmsChngBrwgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        if (CollectionUtil.isNotEmpty(dto.getTime())) {
            dto.setStartTime(dto.getTime().get(0));
            dto.setEndTime(dto.getTime().get(1));
        }
        return amsChngBrwgReadMapper.queryList(dto);
    }

    @Override
    public AmsChngBrwgVo queryCount(AmsChngBrwgDto dto) {
        return  amsChngBrwgReadMapper.queryCount(dto);
    }

    @Override
    public Integer queryWarnNum(AmsChngBrwgDto dto) {
        dto.setStatus("2");
        dto.setSqlAutowiredHospitalCondition(true);
        return amsChngBrwgReadMapper.queryWarnNum(dto);
    }

}
