package com.jp.med.ams.modules.changes.strategy.borrowing.impl;

import com.jp.med.ams.modules.changes.dto.AmsBorrowingAuditNotificationDto;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.mapper.write.AmsChngBrwgWriteMapper;
import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import com.jp.med.ams.modules.changes.strategy.borrowing.AmsBorrowingAuditStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.context.AmsBorrowingAuditContext;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产借用审核通过策略
 * 处理借用申请审核通过的业务逻辑
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Slf4j
@Component
public class AmsBorrowingApprovalStrategy implements AmsBorrowingAuditStrategy {

    @Autowired
    private AmsChngBrwgWriteMapper amsChngBrwgWriteMapper;

    @Autowired
    private AmsPropertyWriteMapper amsPropertyWriteMapper;

    @Autowired
    private AmsBorrowingNotificationService notificationService;

    @Override
    public boolean executeAudit(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        log.info("🔄 执行借用审核通过策略，审核人：{}，目标状态：{}",
                context.getAuditorName(), context.getTargetProcessStatus());

        try {
            if (context.isBatchOperation()) {
                return executeBatchApproval(context, dto);
            } else {
                return executeSingleApproval(context, dto);
            }
        } catch (Exception e) {
            log.error("❌ 借用审核通过策略执行失败", e);
            return false;
        }
    }

    /**
     * 执行批量审核通过
     */
    private boolean executeBatchApproval(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        List<AmsChngBrwgDto> updateDtoList = new ArrayList<>();
        List<AmsPropertyDto> propertyDtoList = new ArrayList<>();

        // 构建批量更新数据
        for (Integer id : dto.getIds()) {
            AmsChngBrwgVo borrowingRecord = context.getBorrowingRecordMap().get(id);
            if (borrowingRecord == null) {
                log.warn("⚠️ 未找到借用记录，ID：{}", id);
                continue;
            }

            // 构建借用记录更新DTO
            AmsChngBrwgDto updateDto = buildApprovalUpdateDto(dto, id, context.getAuditTime());
            updateDtoList.add(updateDto);

            // 构建资产状态更新DTO
            AmsPropertyDto propertyDto = buildPropertyUpdateDto(borrowingRecord, context.getTargetProcessStatus());
            if (propertyDto != null) {
                propertyDtoList.add(propertyDto);
            }
        }

        // 批量更新资产状态
        if (!CollectionUtils.isEmpty(propertyDtoList)) {
            BatchUtil.batch("updateByFaCode", propertyDtoList, AmsPropertyWriteMapper.class);
        }

        // 批量更新借用记录
        if (!CollectionUtils.isEmpty(updateDtoList)) {
            BatchUtil.batch("updateById", updateDtoList, AmsChngBrwgWriteMapper.class);
        }

        // 发送批量通知
        sendBatchApprovalNotifications(context, dto);

        log.info("✅ 批量借用审核通过完成，处理记录数：{}", dto.getIds().size());
        return true;
    }

    /**
     * 执行单个审核通过
     */
    private boolean executeSingleApproval(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        // 更新借用记录
        AmsChngBrwgDto updateDto = buildApprovalUpdateDto(dto, dto.getId(), context.getAuditTime());
        int updateResult = amsChngBrwgWriteMapper.updateById(updateDto);

        if (updateResult != 1) {
            log.error("❌ 更新借用记录失败，ID：{}", dto.getId());
            return false;
        }

        // 更新资产状态（如果需要）
        if (!CollectionUtils.isEmpty(context.getBorrowingRecords())) {
            AmsChngBrwgVo borrowingRecord = context.getBorrowingRecords().get(0);
            AmsPropertyDto propertyDto = buildPropertyUpdateDto(borrowingRecord, context.getTargetProcessStatus());
            if (propertyDto != null) {
                amsPropertyWriteMapper.updateByFaCode(propertyDto);
            }

            // 发送单个通知
            sendSingleApprovalNotification(context, borrowingRecord);
        }

        log.info("✅ 单个借用审核通过完成，记录ID：{}", dto.getId());
        return true;
    }

    /**
     * 构建审核通过更新DTO
     */
    private AmsChngBrwgDto buildApprovalUpdateDto(AmsChngBrwgDto originalDto, Integer id, String auditTime) {
        AmsChngBrwgDto updateDto = new AmsChngBrwgDto();
        updateDto.setId(id);
        updateDto.setProsstas(originalDto.getProsstas());
        updateDto.setChkRemarks(originalDto.getChkRemarks());
        updateDto.setChkTime(auditTime);

        // 归还确认完成
        if (MedConst.TYPE_5.equals(originalDto.getProsstas())) {
            updateDto.setBusstas(MedConst.TYPE_3); // 已归还
            updateDto.setActRtnTime(auditTime);
        }

        return updateDto;
    }

    /**
     * 构建资产状态更新DTO
     */
    private AmsPropertyDto buildPropertyUpdateDto(AmsChngBrwgVo borrowingRecord, String targetProcessStatus) {
        AmsPropertyDto propertyDto = new AmsPropertyDto();
        propertyDto.setFaCode(borrowingRecord.getFaCode());

        if (MedConst.TYPE_4.equals(targetProcessStatus)) {
            // 借用审核通过，设置资产状态为借用
            propertyDto.setAssetStatus(MedConst.ASSET_STATUS_2);
            return propertyDto;
        } else if (MedConst.TYPE_5.equals(targetProcessStatus)) {
            // 归还确认完成，设置资产状态为在库
            propertyDto.setAssetStatus(MedConst.ASSET_STATUS_1);
            return propertyDto;
        }

        return null;
    }

    /**
     * 发送批量审核通过通知
     */
    private void sendBatchApprovalNotifications(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        try {
            for (Integer id : dto.getIds()) {
                AmsChngBrwgVo borrowingRecord = context.getBorrowingRecordMap().get(id);
                if (borrowingRecord != null) {
                    AmsBorrowingAuditNotificationDto notificationDto =
                            AmsBorrowingAuditNotificationDto.buildApprovalNotification(
                                    borrowingRecord, context.getAuditorName(),
                                    context.getAuditorDept(), context.getAuditTime());

                    if (MedConst.TYPE_4.equals(context.getTargetProcessStatus())) {
                        notificationService.sendApplicationApprovedNotification(borrowingRecord, notificationDto);
                    } else if (MedConst.TYPE_5.equals(context.getTargetProcessStatus())) {
                        notificationService.sendReturnConfirmationNotification(borrowingRecord, notificationDto);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("⚠️ 发送批量审核通过通知失败", e);
        }
    }

    /**
     * 发送单个审核通过通知
     */
    private void sendSingleApprovalNotification(AmsBorrowingAuditContext context, AmsChngBrwgVo borrowingRecord) {
        try {
            AmsBorrowingAuditNotificationDto notificationDto =
                    AmsBorrowingAuditNotificationDto.buildApprovalNotification(
                            borrowingRecord, context.getAuditorName(),
                            context.getAuditorDept(), context.getAuditTime());

            if (MedConst.TYPE_4.equals(context.getTargetProcessStatus())) {
                notificationService.sendApplicationApprovedNotification(borrowingRecord, notificationDto);
            } else if (MedConst.TYPE_5.equals(context.getTargetProcessStatus())) {
                notificationService.sendReturnConfirmationNotification(borrowingRecord, notificationDto);
            }
        } catch (Exception e) {
            log.warn("⚠️ 发送单个审核通过通知失败", e);
        }
    }

    @Override
    public String getStrategyType() {
        return "APPROVAL";
    }

    @Override
    public boolean validateAuditParams(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        if (context == null || context.getAuditor() == null) {
            log.error("❌ 审核上下文或审核人信息为空");
            return false;
        }

        if (dto == null) {
            log.error("❌ 审核数据为空");
            return false;
        }

        if (context.isBatchOperation() && CollectionUtils.isEmpty(dto.getIds())) {
            log.error("❌ 批量操作时ID列表为空");
            return false;
        }

        if (!context.isBatchOperation() && dto.getId() == null) {
            log.error("❌ 单个操作时ID为空");
            return false;
        }

        return true;
    }
}
