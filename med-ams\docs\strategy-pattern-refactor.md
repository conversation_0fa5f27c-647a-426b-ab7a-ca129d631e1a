# 资产入库功能策略模式重构文档

## 概述

本文档描述了使用策略模式重构资产入库功能的设计和实现。重构的目标是提高代码的可维护性、可扩展性和可测试性。

## 重构前的问题

原有的 `AmsPropertyWriteServiceImpl` 类存在以下问题：

1. **代码复杂度高**：单个方法 `addProperty` 包含了所有资产类型的处理逻辑，超过 200 行代码
2. **违反开闭原则**：新增资产类型需要修改现有代码
3. **职责不清晰**：一个类承担了多种资产类型的处理逻辑
4. **难以测试**：复杂的条件分支导致测试覆盖困难
5. **代码重复**：不同资产类型之间存在重复的处理逻辑

## 策略模式设计

### 核心组件

#### 1. 策略接口 (AmsPropertyInboundStrategy)

定义了所有入库策略必须实现的方法：

- `validateAssetData()` - 验证资产数据
- `generateAssetCodes()` - 生成资产编码
- `setAssetSpecificProperties()` - 设置资产特定属性
- `preProcessInbound()` - 入库前处理
- `postProcessInbound()` - 入库后处理

#### 2. 具体策略实现

- `AmsPropertyFixedAssetInboundStrategy` - 固定资产入库策略 (type=1)
- `AmsPropertyNonFixedAssetInboundStrategy` - 非固定资产入库策略 (type=2)
- `AmsPropertySubAssetInboundStrategy` - 子项资产入库策略 (type=3)
- `AmsPropertyPreWarehouseInboundStrategy` - 预入库资产策略 (type=4)
- `AmsPropertyConsumableInboundStrategy` - 低值易耗资产策略 (type=8)

#### 3. 策略工厂 (AmsPropertyInboundStrategyFactory)

负责管理和提供不同类型的资产入库策略：

- 自动注册所有策略实现
- 根据资产类型返回对应策略
- 提供策略支持检查和描述信息

#### 4. 策略上下文 (AmsPropertyInboundContext)

协调不同的入库策略，处理通用的入库逻辑：

- 策略选择和执行
- 入库前信息校验补全 (`checkAndCompleteInfo`)
- 盘盈处理逻辑
- 通用业务逻辑处理
- 异常处理和日志记录

#### 5. 重构后的服务层

- `AmsPropertyInboundWriteService` - 新的入库服务接口
- `AmsPropertyInboundWriteServiceImpl` - 使用策略模式的服务实现

## 类图

```mermaid
classDiagram
    class AmsPropertyInboundStrategy {
        <<interface>>
        +getSupportedAssetType() String
        +validateAssetData(dto) void
        +generateAssetCodes(dto) void
        +setAssetSpecificProperties(dto) void
        +preProcessInbound(dto, isConfirmAdd) void
        +postProcessInbound(dto, isConfirmAdd) void
    }

    class AmsPropertyFixedAssetInboundStrategy {
        +getSupportedAssetType() String
        +validateAssetData(dto) void
        +generateAssetCodes(dto) void
        +setAssetSpecificProperties(dto) void
    }

    class AmsPropertyNonFixedAssetInboundStrategy {
        +getSupportedAssetType() String
        +validateAssetData(dto) void
        +generateAssetCodes(dto) void
        +setAssetSpecificProperties(dto) void
    }

    class AmsPropertyInboundStrategyFactory {
        -strategies List~AmsPropertyInboundStrategy~
        -strategyMap Map~String, AmsPropertyInboundStrategy~
        +getStrategy(assetType) AmsPropertyInboundStrategy
        +isSupported(assetType) boolean
        +getSupportedAssetTypes() Set~String~
    }

    class AmsPropertyInboundContext {
        -strategyFactory AmsPropertyInboundStrategyFactory
        +executeInbound(dto, isConfirmAdd) void
        -handleInventoryProfit(dto) void
        -handleConfirmInbound(dto) void
    }

    AmsPropertyInboundStrategy <|.. AmsPropertyFixedAssetInboundStrategy
    AmsPropertyInboundStrategy <|.. AmsPropertyNonFixedAssetInboundStrategy
    AmsPropertyInboundStrategyFactory --> AmsPropertyInboundStrategy
    AmsPropertyInboundContext --> AmsPropertyInboundStrategyFactory
```

## 重构优势

### 1. 符合 SOLID 原则

- **单一职责原则**：每个策略类只负责一种资产类型的处理
- **开闭原则**：新增资产类型只需添加新的策略实现，无需修改现有代码
- **里氏替换原则**：所有策略实现都可以互相替换
- **接口隔离原则**：策略接口只包含必要的方法
- **依赖倒置原则**：高层模块依赖抽象而不是具体实现

### 2. 提高可维护性

- 代码结构清晰，职责分明
- 每个策略类代码量适中，易于理解和维护
- 减少了代码重复和复杂的条件分支

### 3. 增强可扩展性

- 新增资产类型只需实现新的策略类
- 可以独立修改某种资产类型的处理逻辑
- 支持运行时动态选择策略

### 4. 改善可测试性

- 每个策略可以独立测试
- 减少了测试用例的复杂度
- 提高了测试覆盖率

## 核心功能实现

### 入库前信息校验补全

在策略模式重构中，原有的 `checkAndCompleteInfo` 功能被集成到了 `AmsPropertyInboundContext` 中，确保所有资产类型都能享受统一的基础属性初始化：

```java
private void checkAndCompleteInfo(AmsPropertyDto dto) {
    // 设置激活标志为非激活
    if (dto.getIsChk() == null) {
        dto.setIsChk(MedConst.ACTIVE_FLAG_0);
    }

    // 初始化 残值为 原值，净值为 原值，累计折旧为 0
    if (dto.getAssetNav() != null) {
        if (dto.getRv() == null) {
            dto.setRv(dto.getAssetNav());  // 残值 = 原值
        }
        if (dto.getNbv() == null) {
            dto.setNbv(dto.getAssetNav()); // 净值 = 原值
        }
    }

    if (dto.getDep() == null) {
        dto.setDep(BigDecimal.ZERO);       // 累计折旧 = 0
    }
}
```

这个功能在每次资产入库时自动执行，确保数据的完整性和一致性。

### 记录和附件处理

策略模式重构中，原有的 `handelRecords` 功能被完整集成到了 `AmsPropertyInboundContext` 中：

```java
private void handleRecords(AmsPropertyDto dto, Boolean isConfirmAdd) {
    // 1. 查询现有记录
    // 2. 根据确认添加标志选择处理方式
    if (isConfirmAdd) {
        handleConfirmAddRecords(dto, recordsVos);  // 确认添加时更新记录状态
    } else {
        handleNewAddRecords(dto, recordsVos);      // 新增时处理文件上传和记录管理
    }
}
```

**主要功能**：

- **文件上传**: 自动上传附件到 OSS 存储
- **记录管理**: 新增、更新、删除资产相关记录
- **状态维护**: 维护记录的激活状态和关联关系
- **批量操作**: 使用批量操作提高性能

## 使用示例

### 基本用法

```java
@Autowired
private AmsPropertyInboundWriteService inboundService;

// 固定资产入库
AmsPropertyDto fixedAsset = new AmsPropertyDto();
fixedAsset.setType("1");
fixedAsset.setAssetName("测试固定资产");
fixedAsset.setAssetNav(new BigDecimal("50000"));
inboundService.saveInboundData(fixedAsset);

// 批量入库
AmsPropertyDto batchAsset = new AmsPropertyDto();
batchAsset.setType("2");
batchAsset.setStockNumber(10);
inboundService.saveInboundData(batchAsset);
```

### 检查策略支持

```java
@Autowired
private AmsPropertyInboundWriteService inboundService;

// 检查是否支持某种资产类型
if (inboundService.isSupportedAssetType("1")) {
    // 执行入库操作
    inboundService.saveInboundData(dto);
}

// 获取策略描述
String description = inboundService.getStrategyDescription("1");
```

## 兼容性

重构后的代码保持了与原有接口的兼容性：

- `AmsPropertyWriteService.saveData()` 方法内部使用新的策略模式实现
- `AmsPropertyWriteService.saveTemp()` 方法内部使用新的策略模式实现
- 原有的调用方式无需修改

## 测试

提供了完整的测试用例：

- 单元测试：测试每个策略的独立功能
- 集成测试：测试策略工厂和上下文的协调工作
- 兼容性测试：确保与原有接口的兼容性

## 未来扩展

基于策略模式的设计，可以轻松扩展以下功能：

1. 新增资产类型支持
2. 添加资产入库的前置/后置处理器
3. 实现资产入库的审批流程
4. 支持自定义的资产编码规则
5. 添加资产入库的事件通知机制

## 总结

通过策略模式重构，资产入库功能的代码质量得到了显著提升，为后续的功能扩展和维护奠定了良好的基础。重构遵循了面向对象设计原则，提高了代码的可读性、可维护性和可扩展性。
