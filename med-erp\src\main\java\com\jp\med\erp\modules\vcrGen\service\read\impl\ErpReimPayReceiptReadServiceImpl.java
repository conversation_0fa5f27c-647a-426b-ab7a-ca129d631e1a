package com.jp.med.erp.modules.vcrGen.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.erp.modules.vcrGen.mapper.read.ErpReimPayReceiptReadMapper;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimPayReceiptDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import com.jp.med.erp.modules.vcrGen.service.read.ErpReimPayReceiptReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class ErpReimPayReceiptReadServiceImpl extends ServiceImpl<ErpReimPayReceiptReadMapper, ErpReimPayReceiptDto> implements ErpReimPayReceiptReadService {

    @Autowired
    private ErpReimPayReceiptReadMapper erpReimPayReceiptReadMapper;

    @Override
    public List<ErpReimPayReceiptVo> queryList(ErpReimPayReceiptDto dto) {
        return erpReimPayReceiptReadMapper.queryList(dto);
    }

    @Override
    public List<ErpReimPayReceiptVo> queryPageList(ErpReimPayReceiptDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpReimPayReceiptReadMapper.queryList(dto);
    }
}
