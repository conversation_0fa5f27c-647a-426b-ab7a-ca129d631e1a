package com.jp.med.erp.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskVo;

import java.util.List;

/**
 * 应发工资报销任务
 *
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
public interface ErpReimSalaryTaskReadService extends IService<ErpReimSalaryTaskDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<ErpReimSalaryTaskVo> queryList(ErpReimSalaryTaskDto dto);

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    List<ErpReimSalaryTaskVo> queryPageList(ErpReimSalaryTaskDto dto);
}

