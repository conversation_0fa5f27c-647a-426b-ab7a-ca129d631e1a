package com.jp.med.ams.modules.it.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItApplyDetailDto;
import com.jp.med.ams.modules.it.service.read.AmsItApplyDetailReadService;
import com.jp.med.ams.modules.it.service.write.AmsItApplyDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 耗材申请详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Api(value = "耗材申请详情", tags = "耗材申请详情")
@RestController
@RequestMapping("amsItApplyDetail")
public class AmsItApplyDetailController {

    @Autowired
    private AmsItApplyDetailReadService amsItApplyDetailReadService;

    @Autowired
    private AmsItApplyDetailWriteService amsItApplyDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询耗材申请详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItApplyDetailDto dto){
        return CommonResult.success(amsItApplyDetailReadService.getList(dto));
    }
    @ApiOperation("admin查询耗材申请详情")
    @PostMapping("/adminList")
    public CommonResult<?> adminList(@RequestBody AmsItApplyDetailDto dto){
        return CommonResult.success(amsItApplyDetailReadService.adminList(dto));
    }

    @ApiOperation("分页查询耗材申请详情")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody AmsItApplyDetailDto dto){
        return CommonResult.paging(amsItApplyDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增耗材申请详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsItApplyDetailDto dto){
        amsItApplyDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改耗材申请详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItApplyDetailDto dto){
        amsItApplyDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除耗材申请详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItApplyDetailDto dto){
        amsItApplyDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
