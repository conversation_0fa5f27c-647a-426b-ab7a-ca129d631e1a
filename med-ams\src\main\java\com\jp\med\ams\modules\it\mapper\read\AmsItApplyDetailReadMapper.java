package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItApplyDetailDto;
import com.jp.med.ams.modules.it.vo.AmsItApplyDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 耗材申请详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItApplyDetailReadMapper extends BaseMapper<AmsItApplyDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItApplyDetailVo> queryList(AmsItApplyDetailDto dto);

    List<AmsItApplyDetailVo> adminList(AmsItApplyDetailDto dto);
}
