# 资产变更记录服务重构文档

## 重构目标

1. **解决批量更新性能问题**：将多次单独的资产状态更新改为批量更新
2. **使用设计模式改善代码结构**：使用策略模式和模板方法模式重构混乱的业务逻辑
3. **提高代码可维护性**：分离不同类型的变更处理逻辑
4. **完善审批功能**：重构审批方法，添加取消审批处理

## 设计模式应用

### 1. 策略模式 (Strategy Pattern)

**目的**：根据不同的变更类型选择不同的处理策略

#### 变更记录处理策略

- `ChangeRecordProcessor` - 策略接口
- `AbstractChangeRecordProcessor` - 抽象策略类
- `ScrapBatchProcessor` - 批量报废处理策略
- `NormalChangeProcessor` - 普通变更处理策略
- `AssetSplitProcessor` - 资产拆分处理策略

#### 审批处理策略

- `ApprovalProcessor` - 审批策略接口
- `AbstractApprovalProcessor` - 抽象审批策略类
- `ScrapApprovalProcessor` - 报废审批处理策略
- `NormalApprovalProcessor` - 普通审批处理策略
- `AssetSplitApprovalProcessor` - 拆分审批处理策略

### 2. 模板方法模式 (Template Method Pattern)

**目的**：定义变更记录处理的标准流程

**实现**：

- `AbstractChangeRecordProcessor.process()` - 模板方法
- 子类重写 `createPropertyUpdate()` 方法实现具体逻辑

### 3. 工厂模式 (Factory Pattern)

**目的**：根据 DTO 自动选择合适的处理器

**实现**：

- `ChangeRecordProcessorFactory` - 处理器工厂

## 核心组件

### 1. 处理上下文 (ProcessContext)

```java
public class ProcessContext {
    private final int maxChgCode;
    private final String formattedDateTime;
    private final AmsPropertyReadServiceImpl amsPropertyReadService;
    private final AmsPropertyWriteServiceImpl amsPropertyWriteService;
}
```

### 2. 批量更新服务 (BatchPropertyUpdateService)

```java
@Service
public class BatchPropertyUpdateService {
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateProperties(List<AmsPropertyDto> propertyUpdates)
}
```

### 3. 处理器策略（按优先级排序）

- **批量报废处理器**（@Order(1)）：直接更新资产状态为已注销
- **资产拆分处理器**（@Order(2)）：专门处理拆分逻辑
- **普通变更处理器**（@Order(999)）：通过审批流程处理，作为默认处理器

#### 处理器优先级说明

使用 Spring 的`@Order`注解控制处理器的执行顺序：

1. **数字越小，优先级越高**
2. **具体的处理器优先于通用的处理器**
3. **NormalChangeProcessor**设置为最低优先级（999），确保它作为默认处理器最后执行

这样可以避免通用处理器覆盖具体处理器的问题。

## 重构前后对比

### 重构前问题

1. 所有逻辑混在一个方法中，难以维护
2. 批量报废时多次调用单独更新，性能差
3. 不同类型的变更处理逻辑耦合严重

### 重构后优势

1. **性能提升**：批量更新替代多次单独更新
2. **代码清晰**：每种变更类型有独立的处理器
3. **易于扩展**：新增变更类型只需添加新的处理器
4. **职责分离**：变更记录创建、资产状态更新分离

## 使用示例

### 批量报废

```java
AmsChgRcdDto dto = new AmsChgRcdDto();
dto.setBatchProcessing("1");
dto.setRedcWay("5"); // 报废
dto.setFaCodes(Arrays.asList("FA001", "FA002", "FA003"));

// 自动选择ScrapBatchProcessor处理
// 批量更新所有资产状态为已注销
Object result = amsChgRcdWriteService.saveAmsChgRcd(dto);
```

### 普通变更

```java
AmsChgRcdDto dto = new AmsChgRcdDto();
dto.setBatchProcessing("0");
dto.setRedcWay("1"); // 出售
dto.setFaCodes(Arrays.asList("FA001"));

// 自动选择NormalChangeProcessor处理
// 创建变更记录，等待审批
Object result = amsChgRcdWriteService.saveAmsChgRcd(dto);
```

### 审批处理

#### 通过审批

```java
AmsChgRcdDto approvalDto = new AmsChgRcdDto();
approvalDto.setPass(true); // 审批通过
approvalDto.setIds(Arrays.asList(1, 2, 3)); // 变更记录ID

// 自动选择合适的审批处理器
// 批量更新资产状态
amsChgRcdWriteService.approval(approvalDto);
```

#### 取消审批

```java
AmsChgRcdDto approvalDto = new AmsChgRcdDto();
approvalDto.setPass(false); // 取消审批
approvalDto.setIds(Arrays.asList(1, 2, 3)); // 变更记录ID

// 自动选择合适的审批处理器
// 恢复资产状态，清除减少方式
amsChgRcdWriteService.approval(approvalDto);
```

## 扩展指南

### 添加新的变更类型处理器

1. 继承 `AbstractChangeRecordProcessor`
2. 实现 `supports()` 方法判断是否支持该类型
3. 重写 `createPropertyUpdate()` 方法实现具体逻辑
4. 添加 `@Component` 注解让 Spring 自动注册

```java
@Component
public class NewChangeProcessor extends AbstractChangeRecordProcessor {

    @Override
    public boolean supports(AmsChgRcdDto dto) {
        return "NEW_TYPE".equals(dto.getRedcWay());
    }

    @Override
    protected AmsPropertyDto createPropertyUpdate(AmsChgRcdDto dto, String faCode, ProcessContext context) {
        // 实现具体的更新逻辑
        return null;
    }
}
```

## 测试

运行测试类验证重构效果：

```bash
# 测试变更记录处理
mvn test -Dtest=AmsChgRcdWriteServiceImplTest

# 测试审批处理器
mvn test -Dtest=ApprovalProcessorTest
```
