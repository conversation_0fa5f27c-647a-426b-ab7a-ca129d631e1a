package com.jp.med.ams.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.vo.SelectOptionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.config.mapper.read.AmsBasicCfgReadMapper;
import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import com.jp.med.ams.modules.config.service.read.AmsBasicCfgReadService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class AmsBasicCfgReadServiceImpl extends ServiceImpl<AmsBasicCfgReadMapper, AmsBasicCfgDto> implements AmsBasicCfgReadService {

    @Autowired
    private AmsBasicCfgReadMapper amsBasicCfgReadMapper;

    @Override
    public List<AmsBasicCfgVo> queryList(AmsBasicCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsBasicCfgReadMapper.queryList(dto);
    }


    @Override
    public void checkOnly(AmsBasicCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<AmsBasicCfgVo> basicCfgVos = amsBasicCfgReadMapper.queryList(dto);
        if (!basicCfgVos.isEmpty()){
            throw new AppException("当前编码已存在");
        }
    }

    @Override
    public Map<String, List<SelectOptionVo>> queryConfig(AmsBasicCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<AmsBasicCfgVo> basicCfgVos = amsBasicCfgReadMapper.queryList(dto);
        if (basicCfgVos.isEmpty()){
            return null;
        }
        Map<String, List<AmsBasicCfgVo>> collect = basicCfgVos.stream().collect(Collectors.groupingBy(AmsBasicCfgVo::getType));
        Map<String, List<SelectOptionVo>> resultMap = new HashMap<>();
        for (Map.Entry<String, List<AmsBasicCfgVo>> entry : collect.entrySet()) {
            List<SelectOptionVo> optionVos = new ArrayList<>();
            for (AmsBasicCfgVo amsBasicCfgVo : entry.getValue()) {
                SelectOptionVo selectOptionVo = new SelectOptionVo();
                selectOptionVo.setValue(amsBasicCfgVo.getCode());
                selectOptionVo.setLabel(amsBasicCfgVo.getName());
                optionVos.add(selectOptionVo);
            }
            resultMap.put(entry.getKey(), optionVos);
        }
        return resultMap;
    }

}
