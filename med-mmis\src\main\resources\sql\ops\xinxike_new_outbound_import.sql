-- =================================================================================================
-- 信息科新出库数据导入脚本：mmis_temp_xinxike_outbound_six → mmis_outbound_apply & mmis_outbound_apply_details
-- 功能：将信息科新的出库数据按业务逻辑分组导入到正式出库申请表中
-- 作者：信息科数据迁移
-- 创建时间：2025年6月
-- 数据来源：mmis_temp_xinxike_outbound_six.sql
-- =================================================================================================

-- 开始事务处理，确保数据一致性
BEGIN;

-- =================================================================================================
-- 第一步：数据预处理和清洗 🔍
-- =================================================================================================

-- 首先添加必要的字段到临时表（如果不存在）
ALTER TABLE mmis_temp_xinxike_outbound_six
ADD COLUMN IF NOT EXISTS out_target_org_id varchar(100),
ADD COLUMN IF NOT EXISTS out_appyer varchar(50),
ADD COLUMN IF NOT EXISTS mat_unique_code varchar(100),
ADD COLUMN IF NOT EXISTS item_num varchar(50),
ADD COLUMN IF NOT EXISTS meter_code varchar(50),
ADD COLUMN IF NOT EXISTS create_time varchar(50),
ADD COLUMN IF NOT EXISTS bill_date date,
ADD COLUMN IF NOT EXISTS opter varchar(50),
ADD COLUMN IF NOT EXISTS opter_org varchar(50),
ADD COLUMN IF NOT EXISTS wrhs_addr varchar(50),
ADD COLUMN IF NOT EXISTS item_count numeric(5,2),
ADD COLUMN IF NOT EXISTS hospital_id varchar(50),
ADD COLUMN IF NOT EXISTS is_deleted int,
ADD COLUMN IF NOT EXISTS chk_state varchar(1),
ADD COLUMN IF NOT EXISTS appy_org_id varchar(50);

-- 清理空行数据
DELETE FROM mmis_temp_xinxike_outbound_six
WHERE name IS NULL OR TRIM(name) = '';

-- 统计源表记录数
SELECT
    '源表总记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_xinxike_outbound_six;

-- =================================================================================================
-- 第二步：员工信息转换（通过hrm_employee_info联表查询）👤
-- =================================================================================================

-- 更新员工信息：通过员工编号从hrm_employee_info表查询员工姓名
UPDATE mmis_temp_xinxike_outbound_six t
SET out_appyer = e.emp_name
FROM hrm_employee_info e
WHERE t.out_emp = e.emp_code
  AND e.hospital_id = 'zjxrmyy'
  AND e.is_deleted = 0
  AND t.out_emp IS NOT NULL
  AND t.out_emp != '';

-- 查看员工信息匹配情况
SELECT
    '通过员工编号匹配成功的记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_xinxike_outbound_six
WHERE out_emp IS NOT NULL AND out_emp != '' AND out_appyer IS NOT NULL;

SELECT
    '员工编号匹配失败的记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_xinxike_outbound_six
WHERE out_emp IS NOT NULL AND out_emp != '' AND out_appyer IS NULL;

-- 显示匹配失败的员工编号（用于排查）
SELECT DISTINCT
    '匹配失败的员工编号' as desc_info,
    out_emp as emp_code
FROM mmis_temp_xinxike_outbound_six
WHERE out_emp IS NOT NULL AND out_emp != '' AND out_appyer IS NULL
ORDER BY out_emp;

-- =================================================================================================
-- 第三步：科室ID匹配（通过hrm_org联表查询）🏢
-- =================================================================================================

-- 更新科室组织ID：通过科室名称从hrm_org表查询组织ID
-- 优先匹配完全相同的科室名称
UPDATE mmis_temp_xinxike_outbound_six t
SET out_target_org_id = o.org_id
FROM hrm_org o
WHERE t.out_org = o.org_name 
  AND o.hospital_id = 'zjxrmyy'
  AND o.active_flag = '1'
  AND t.out_org IS NOT NULL 
  AND t.out_org != '';

-- 对于没有完全匹配的科室，尝试模糊匹配
UPDATE mmis_temp_xinxike_outbound_six t
SET out_target_org_id = o.org_id
FROM hrm_org o
WHERE t.out_target_org_id IS NULL
  AND t.out_org IS NOT NULL 
  AND t.out_org != ''
  AND o.hospital_id = 'zjxrmyy'
  AND o.active_flag = '1'
  AND (
      o.org_name LIKE '%' || t.out_org || '%' OR
      t.out_org LIKE '%' || o.org_name || '%'
  );

-- 对于仍然没有匹配的科室，尝试更宽泛的匹配规则
UPDATE mmis_temp_xinxike_outbound_six t
SET out_target_org_id = o.org_id
FROM hrm_org o
WHERE t.out_target_org_id IS NULL
  AND t.out_org IS NOT NULL 
  AND t.out_org != ''
  AND o.hospital_id = 'zjxrmyy'
  AND o.active_flag = '1'
  AND (
      -- 去掉常见后缀进行匹配
      REPLACE(REPLACE(REPLACE(o.org_name, '门诊', ''), '住院', ''), '病区', '') LIKE '%' || REPLACE(REPLACE(REPLACE(t.out_org, '门诊', ''), '住院', ''), '病区', '') || '%' OR
      REPLACE(REPLACE(REPLACE(t.out_org, '门诊', ''), '住院', ''), '病区', '') LIKE '%' || REPLACE(REPLACE(REPLACE(o.org_name, '门诊', ''), '住院', ''), '病区', '') || '%'
  );

-- 查看科室匹配情况
SELECT 
    '通过科室名称匹配成功的记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL AND out_org != '' AND out_target_org_id IS NOT NULL;

SELECT
    '科室名称匹配失败的记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL AND out_org != '' AND out_target_org_id IS NULL;

-- 显示匹配失败的科室名称（用于排查）
SELECT DISTINCT
    '匹配失败的科室名称' as desc_info,
    out_org as org_name
FROM mmis_temp_xinxike_outbound_six
WHERE out_org IS NOT NULL AND out_org != '' AND out_target_org_id IS NULL
ORDER BY out_org;

-- 对于仍然无法匹配的科室，设置默认值为信息科
UPDATE mmis_temp_xinxike_outbound_six
SET out_target_org_id = '521001'  -- 默认为信息科
WHERE out_target_org_id IS NULL;

-- =================================================================================================
-- 第四步：物资信息补充 📦
-- =================================================================================================

-- 根据物资名称匹配物资唯一编码、物品编号、规格型号、参考价格
UPDATE mmis_temp_xinxike_outbound_six t
SET 
    mat_unique_code = a.mat_unique_code,
    item_num = a.code,
    modspec = COALESCE(t.modspec, a.modspec),
    price = CASE 
        WHEN t.price IS NOT NULL AND t.price ~ '^[0-9]+\.?[0-9]*$' 
        THEN CAST(t.price AS numeric)
        ELSE a.ref_price 
    END,
    meter_code = COALESCE(a.mtr_type, '022')
FROM mmis_aset_info_assist a
WHERE t.name = a.name;

-- =================================================================================================
-- 第五步：日期处理 📅
-- =================================================================================================

-- 根据备注字段解析日期（假设remark包含月份信息）
UPDATE mmis_temp_xinxike_outbound_six t
SET 
    create_time = CASE 
        WHEN t.remark = '12月' THEN '2024-12-25 12:00:00'
        WHEN t.remark = '1月' THEN '2025-01-01 12:00:00'
        WHEN t.remark = '2月' THEN '2025-02-01 12:00:00'
        WHEN t.remark = '3月' THEN '2025-03-01 12:00:00'
        WHEN t.remark = '4月' THEN '2025-04-01 12:00:00'
        WHEN t.remark = '5月' THEN '2025-05-01 12:00:00'
        ELSE '2025-01-01 12:00:00'
    END,
    bill_date = CASE 
        WHEN t.remark = '12月' THEN '2024-12-25'::date
        WHEN t.remark = '1月' THEN '2025-01-01'::date
        WHEN t.remark = '2月' THEN '2025-02-01'::date
        WHEN t.remark = '3月' THEN '2025-03-01'::date
        WHEN t.remark = '4月' THEN '2025-04-01'::date
        WHEN t.remark = '5月' THEN '2025-05-01'::date
        ELSE '2025-01-01'::date
    END;

-- 更新其他固定字段
UPDATE mmis_temp_xinxike_outbound_six
SET 
    opter = '0080',
    opter_org = '521001',
    wrhs_addr = '0003',
    item_count = 1,
    hospital_id = 'zjxrmyy',
    is_deleted = 0,
    chk_state = '1',
    appy_org_id = '521001';

-- =================================================================================================
-- 第六步：数据验证统计 📊
-- =================================================================================================

-- 统计有效记录数量
SELECT 
    '有效记录数（基本信息完整）' as desc_info,
    COUNT(*) as count_val
FROM mmis_temp_xinxike_outbound_six
WHERE out_target_org_id IS NOT NULL
  AND name IS NOT NULL AND name != ''
  AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0;

-- 预览分组统计：查看将要生成多少个出库单
SELECT
    '预计生成的出库单数量' as desc_info,
    COUNT(*) as count_val
FROM (
    SELECT DISTINCT
        out_target_org_id,
        bill_date,
        hospital_id,
        remark
    FROM mmis_temp_xinxike_outbound_six
    WHERE out_target_org_id IS NOT NULL
      AND name IS NOT NULL AND name != ''
      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
) grouped_preview;

-- =================================================================================================
-- 第七步：生成单据号序列 📋
-- =================================================================================================

-- 创建临时序列表，用于生成出库申请单据号
CREATE TEMP TABLE temp_outbound_doc_sequence AS
WITH source_groups AS (
    SELECT DISTINCT
        out_target_org_id,
        bill_date,
        hospital_id,
        remark
    FROM mmis_temp_xinxike_outbound_six
    WHERE out_target_org_id IS NOT NULL
      AND name IS NOT NULL AND name != ''
      AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
),
grouped_data_with_min_create_time AS (
    SELECT
        sg.out_target_org_id,
        sg.bill_date,
        sg.hospital_id,
        sg.remark,
        MIN(ti.create_time) as master_create_time
    FROM source_groups sg
    JOIN mmis_temp_xinxike_outbound_six ti ON
        sg.out_target_org_id = ti.out_target_org_id AND
        sg.bill_date = ti.bill_date AND
        sg.hospital_id = ti.hospital_id AND
        sg.remark = ti.remark
    WHERE ti.out_target_org_id IS NOT NULL
      AND ti.name IS NOT NULL AND ti.name != ''
      AND ti.num IS NOT NULL AND ti.num ~ '^[0-9]+\.?[0-9]*$' AND CAST(ti.num AS numeric) > 0
    GROUP BY sg.out_target_org_id, sg.bill_date, sg.hospital_id, sg.remark
),
numbered_applications AS (
  SELECT
      gds.*,
      ROW_NUMBER() OVER (ORDER BY gds.bill_date, gds.out_target_org_id, gds.remark) as seq_num
  FROM grouped_data_with_min_create_time gds
)
SELECT
    nr.*,
    'CKD' || TO_CHAR(nr.bill_date, 'YYYYMMDD') || LPAD(nr.seq_num::text, 3, '0') as docment_num
FROM numbered_applications nr;

-- 验证单据号唯一性
SELECT 
    '生成的单据号数量' as desc_info,
    COUNT(*) as count_val
FROM temp_outbound_doc_sequence;

-- =================================================================================================
-- 第八步：插入出库申请主表 (mmis_outbound_apply) 🏗️
-- =================================================================================================

INSERT INTO mmis_outbound_apply (
    appy_org_id,          -- 申请科室ID
    appyer,               -- 申请人
    chk_state,            -- 审核状态
    bill_date,            -- 开单日期
    docment_num,          -- 单据号
    out_taget_org,        -- 出给具体的科室名称（注意：原表字段名拼写是taget）
    out_appyer,           -- 目标科室的申请人
    out_target_org_id,    -- 出库给具体科室的科室ID
    hos_campus,           -- 所属园区
    hospital_id,          -- 组织ID
    is_deleted,           -- 逻辑删除标志
    create_time,          -- 创建时间
    crter,                -- 创建人
    update_time,          -- 修改时间
    updtr,                -- 修改人
    inv_type,             -- 出入库类型
    wrhs_code,            -- 仓库代码
    out_status,           -- 出库状态
    apply_remark,         -- 申请备注
    out_emp,              -- 出库操作人
    out_org_id,           -- 出库执行人部门
    out_time              -- 出库时间
)
SELECT 
    '521001',                                              -- 申请科室ID（信息科）
    'SYS_IMPORT',                                          -- 申请人
    '1',                                                   -- 审核状态（'1'已审核）
    tds.bill_date::varchar,                                -- 开单日期
    tds.docment_num,                                       -- 生成的单据号
    (SELECT org_name FROM hrm_org WHERE org_id = tds.out_target_org_id AND hospital_id = 'zjxrmyy' LIMIT 1), -- 科室名称
    '信息科数据导入',                                       -- 目标科室申请人
    tds.out_target_org_id,                                 -- 出库给具体科室的科室ID
    '0',                                                   -- 所属园区
    tds.hospital_id,                                       -- 组织ID（'zjxrmyy'）
    0,                                                     -- 逻辑删除标志（0-未删除）
    tds.master_create_time,                                -- 创建时间（跟随原数据）
    'SYS_IMPORT',                                          -- 创建人（系统导入）
    tds.master_create_time,                                -- 修改时间（跟随原数据）
    'SYS_IMPORT',                                          -- 修改人（系统导入）
    'C2',                                                  -- 出入库类型（C2类型）
    '0003',                                                -- 仓库代码（信息科指定仓库）
    '1',                                                   -- 出库状态（1-已出库待确认）
    '信息科历史数据导入批次：' || tds.remark,              -- 申请备注
    '0080',                                                -- 出库操作人（默认0080）
    '521001',                                              -- 出库执行人部门（默认521001）
    tds.master_create_time                                 -- 出库时间（跟随原数据）
FROM temp_outbound_doc_sequence tds;

-- 验证主表插入结果
SELECT 
    '主表插入记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_outbound_apply
WHERE crter = 'SYS_IMPORT';

-- =================================================================================================
-- 第九步：插入出库申请明细表 (mmis_outbound_apply_details) 📦
-- =================================================================================================

INSERT INTO mmis_outbound_apply_details (
    apply_id,             -- 申请ID（关联主表）
    item_num,             -- 货号/物品编号
    name,                 -- 品名
    modspec,              -- 规格型号
    wrhs_addr,            -- 库位代码
    meter_code,           -- 计量方式
    item_count,           -- 每件细数
    num,                  -- 最终申请数量
    price,                -- 单价
    amt,                  -- 金额
    remark,               -- 物资备注
    mat_unique_code,      -- 物资唯一编码
    crter,                -- 创建人
    create_time,          -- 创建时间
    hospital_id,          -- 组织ID
    is_deleted,           -- 逻辑删除标志
    update_time,          -- 修改时间
    updtr                 -- 修改人
)
SELECT 
    oa.id,                                                                    -- 关联主表ID
    COALESCE(temp.item_num, 'UNKNOWN'),                                       -- 物品编号
    temp.name,                                                                -- 品名
    temp.modspec,                                                             -- 规格型号
    '0003',                                                                   -- 库位代码（默认库位）
    COALESCE(temp.meter_code, '个'),                                          -- 计量方式（默认'个'）
    COALESCE(temp.item_count, 1.00),                                          -- 每件细数（默认1）
    CASE 
        WHEN temp.num IS NOT NULL AND temp.num ~ '^[0-9]+\.?[0-9]*$' 
        THEN CAST(temp.num AS numeric(20,6))
        ELSE 0 
    END,                                                                      -- 申请数量
    CASE 
        WHEN temp.price IS NOT NULL AND temp.price ~ '^[0-9]+\.?[0-9]*$' 
        THEN CAST(temp.price AS numeric(20,6))
        ELSE 0 
    END,                                                                      -- 单价
    CASE 
        WHEN temp.num IS NOT NULL AND temp.num ~ '^[0-9]+\.?[0-9]*$' AND temp.price IS NOT NULL AND temp.price ~ '^[0-9]+\.?[0-9]*$'
        THEN CAST(temp.num AS numeric(20,6)) * CAST(temp.price AS numeric(20,6))
        ELSE 0 
    END,                                                                      -- 金额（数量*单价）
    '导入批次：' || COALESCE(temp.remark, ''),                                -- 物资备注
    temp.mat_unique_code,                                                     -- 物资唯一编码
    'SYS_IMPORT',                                                             -- 创建人
    temp.create_time,                                                         -- 创建时间（跟随原数据）
    temp.hospital_id,                                                         -- 组织ID
    0,                                                                        -- 逻辑删除标志
    temp.create_time,                                                         -- 修改时间（跟随原数据）
    'SYS_IMPORT'                                                              -- 修改人
FROM mmis_temp_xinxike_outbound_six temp
INNER JOIN temp_outbound_doc_sequence tds_join ON 
    tds_join.out_target_org_id = temp.out_target_org_id AND
    tds_join.bill_date = temp.bill_date AND
    tds_join.hospital_id = temp.hospital_id AND
    tds_join.remark = temp.remark
INNER JOIN mmis_outbound_apply oa ON oa.docment_num = tds_join.docment_num 
    AND oa.crter = 'SYS_IMPORT'
WHERE temp.out_target_org_id IS NOT NULL 
  AND temp.name IS NOT NULL AND temp.name != ''
  AND temp.num IS NOT NULL AND temp.num ~ '^[0-9]+\.?[0-9]*$' AND CAST(temp.num AS numeric) > 0;

-- 验证明细表插入结果
SELECT 
    '明细表插入记录数' as desc_info,
    COUNT(*) as count_val
FROM mmis_outbound_apply_details
WHERE crter = 'SYS_IMPORT';

-- =================================================================================================
-- 第十步：数据完整性验证 ✅
-- =================================================================================================

-- 验证主表与明细表关联完整性
SELECT 
    '主表记录数 (本次导入)' as desc_info,
    COUNT(*) as count_val
FROM mmis_outbound_apply
WHERE crter = 'SYS_IMPORT';

SELECT 
    '明细表记录数 (本次导入)' as desc_info,
    COUNT(*) as count_val
FROM mmis_outbound_apply_details
WHERE crter = 'SYS_IMPORT';

-- 检查是否有明细记录缺失主表关联
SELECT 
    '孤立明细记录数（无主表关联, 本次导入）' as desc_info,
    COUNT(*) as count_val
FROM mmis_outbound_apply_details d
WHERE d.crter = 'SYS_IMPORT'
  AND NOT EXISTS (
      SELECT 1 FROM mmis_outbound_apply a 
      WHERE a.id = d.apply_id AND a.crter = 'SYS_IMPORT'
  );

-- 金额汇总验证
SELECT 
    '明细表总金额 (本次导入)' as desc_info,
    ROUND(SUM(d.amt), 2) as total_amount
FROM mmis_outbound_apply_details d
INNER JOIN mmis_outbound_apply a ON a.id = d.apply_id
WHERE a.crter = 'SYS_IMPORT';

-- =================================================================================================
-- 第十一步：抽样验证结果 🔍
-- =================================================================================================

-- 显示导入成功的出库申请单示例
SELECT 
    '导入成功的出库申请单示例' as desc_info,
    a.id,
    a.docment_num,
    a.out_target_org_id,
    a.bill_date,
    COUNT(d.id) as detail_count,
    ROUND(SUM(d.amt), 2) as total_amount
FROM mmis_outbound_apply a
LEFT JOIN mmis_outbound_apply_details d ON a.id = d.apply_id AND d.crter = 'SYS_IMPORT'
WHERE a.crter = 'SYS_IMPORT'
GROUP BY a.id, a.docment_num, a.out_target_org_id, a.bill_date
ORDER BY a.id
LIMIT 10;

-- 显示明细记录示例
SELECT 
    '明细记录示例' as desc_info,
    d.apply_id,
    d.item_num,
    d.name,
    d.num,
    d.price,
    d.amt,
    d.meter_code,
    a.docment_num
FROM mmis_outbound_apply_details d
INNER JOIN mmis_outbound_apply a ON a.id = d.apply_id
WHERE d.crter = 'SYS_IMPORT'
ORDER BY d.apply_id, d.id
LIMIT 10;

-- =================================================================================================
-- 第十二步：清理和完成 🧹
-- =================================================================================================

-- 删除临时序列表
DROP TABLE IF EXISTS temp_outbound_doc_sequence;

-- 显示最终统计
SELECT 
    '=== 数据导入完成统计 ===' as desc_info,
    '' as value;

SELECT 
    '成功导入出库申请单数量' as desc_info,
    COUNT(*)::text as value
FROM mmis_outbound_apply
WHERE crter = 'SYS_IMPORT';

SELECT 
    '成功导入出库申请明细数量' as desc_info,
    COUNT(*)::text as value
FROM mmis_outbound_apply_details
WHERE crter = 'SYS_IMPORT';

SELECT 
    '总出库金额 (基于明细)' as desc_info,
    ROUND(SUM(d.amt), 2)::text as value
FROM mmis_outbound_apply_details d
INNER JOIN mmis_outbound_apply a ON a.id = d.apply_id
WHERE a.crter = 'SYS_IMPORT';

-- 提交事务
COMMIT;

-- =================================================================================================
-- 脚本执行完成 ✅
-- 说明：
-- 1. 本脚本通过联表查询hrm_employee_info和hrm_org获取员工和科室信息，避免硬编码
-- 2. 按照业务逻辑(科室、日期、备注)进行了分组，生成了标准的出库申请单据
-- 3. 所有导入记录的crter字段标记为'SYS_IMPORT'，便于识别和管理
-- 4. 已完成数据完整性验证，确保主表与明细表正确关联
-- 5. 如需回滚，可通过crter='SYS_IMPORT' 条件删除所有导入记录
-- ================================================================================================= 