package com.jp.med.erp.modules.vcrGen.vo;

import com.jp.med.common.entity.audit.AuditCommonRes;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
@Data
public class ErpDrugReimDetailVo extends AuditCommonRes {

	/** id */
	private Long id;

	/** 供货单位 */
	private String spler;

	/** 合计金额小写(元) */
	private BigDecimal sum;

	/** 合计金额大写 */
	private String capSum;

	/** 付款说明 */
	private String payIstr;

	/** 审核批次号 */
	private String auditBchno;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String craeteTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 创建者 */
	private String crterName;

	/** 审核状态，1：审核完成且通过，2：审核失败，3：审核中（全部未开始或开始部分） */
	private String auditState;

	/** 期号 */
	private String issue;

	/** 审核标识 */
	private String auditFlag;

	/** 上级审核批次号 */
	private String parentAuditBchno;

	/**
	 * 状态 1：审核中 2：审核成功 3：审核失败 4：待付款 5：已付款
	 */
	private String status;

	/** 付款单附件code **/
	private String attCode;

	/** 药品付款批次id **/
	private Integer drugPayId;

	/**
	 * 付款类型 1：非集采 2：集采
	 */
	private String drugPayType;

	/**
	 * 支付方式 1：现金
	 */
	private String payMethod;

	/** 上传付款单说明 */
	private String payIstr2;

}
