package com.jp.med.ams.modules.it.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItInvtApplyDto;
import com.jp.med.ams.modules.it.service.read.AmsItInvtApplyReadService;
import com.jp.med.ams.modules.it.service.write.AmsItInvtApplyWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 信息科库房耗材申请主表
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Api(value = "信息科库房耗材申请主表", tags = "信息科库房耗材申请主表")
@RestController
@RequestMapping("amsItInvtApply")
public class AmsItInvtApplyController {

    @Autowired
    private AmsItInvtApplyReadService amsItInvtApplyReadService;

    @Autowired
    private AmsItInvtApplyWriteService amsItInvtApplyWriteService;

    @ApiOperation("耗材出库")
    @PostMapping("/outInvt")
    public CommonResult<?> outInvt(@RequestBody AmsItInvtApplyDto dto){
        amsItInvtApplyWriteService.outInvt(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询耗材出库信息")
    @PostMapping("/productOutInvt")
    public CommonResult<?> productOutInvt(@RequestBody AmsItInvtApplyDto dto){
        return CommonResult.paging(amsItInvtApplyReadService.productOutInvt(dto));
    }

    /**
     * 修改
     */
    @ApiOperation("审核耗材申请")
    @PutMapping("/checkApply")
    public CommonResult<?> checkApply(@RequestBody AmsItInvtApplyDto dto){
        amsItInvtApplyWriteService.checkApply(dto);
        return CommonResult.success();
    }

    /**
     * 列表
     */
    @ApiOperation("查询信息科库房耗材申请主表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody AmsItInvtApplyDto dto){
        return CommonResult.paging(amsItInvtApplyReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询信息科库房耗材申请主表")
    @PostMapping("/adminPageList")
    public CommonResult<?> adminPageList(@RequestBody AmsItInvtApplyDto dto){
        return CommonResult.paging(amsItInvtApplyReadService.adminQueryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增信息科库房耗材申请主表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsItInvtApplyDto dto){
        amsItInvtApplyWriteService.saveApplyDetail(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改信息科库房耗材申请主表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItInvtApplyDto dto){
        amsItInvtApplyWriteService.updateApplyById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除信息科库房耗材申请主表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItInvtApplyDto dto){
        amsItInvtApplyWriteService.deleteApplyById(dto);
        return CommonResult.success();
    }

    /**
     * 撤销申请
     */
    @ApiOperation("撤销申请")
    @DeleteMapping("/back")
    public CommonResult<?> back(@RequestBody AmsItInvtApplyDto dto){
        amsItInvtApplyWriteService.backApplyById(dto);
        return CommonResult.success();
    }

}
