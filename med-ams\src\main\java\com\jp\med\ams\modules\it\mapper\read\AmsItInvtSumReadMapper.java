package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItInvtSumDto;
import com.jp.med.ams.modules.it.vo.AmsItApplyDetailVo;
import com.jp.med.ams.modules.it.vo.AmsItInvtSumVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 耗材库存汇总
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItInvtSumReadMapper extends BaseMapper<AmsItInvtSumDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItInvtSumVo> queryList(AmsItInvtSumDto dto);

    List<AmsItInvtSumVo> modelList(AmsItInvtSumDto dto);

    List<AmsItInvtSumVo> querySum(AmsItInvtSumDto dto);

    List<AmsItInvtSumVo> queryInvtSum(AmsItInvtSumDto dto);

    List<AmsItInvtSumVo> queryAlarmList(AmsItInvtSumDto dto);

    List<AmsItInvtSumVo> queryCheckedList(AmsItInvtSumDto dto);


    List<AmsItInvtSumVo> queryAlarmListDetial(AmsItInvtSumDto dto);
}
