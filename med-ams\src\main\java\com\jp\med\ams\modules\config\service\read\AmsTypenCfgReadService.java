package com.jp.med.ams.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.config.dto.AmsTypenCfgDto;
import com.jp.med.ams.modules.config.vo.AmsTypenCfgVo;

import java.util.List;

/**
 * 新资产分类
 * <AUTHOR>
 * @email -
 * @date 2023-12-06 11:20:53
 */
public interface AmsTypenCfgReadService extends IService<AmsTypenCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsTypenCfgVo> queryList(AmsTypenCfgDto dto);

    List<AmsTypenCfgVo> queryNormal(AmsTypenCfgDto dto);
}

