package com.jp.med.ams.modules.depr.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.depr.mapper.read.AmsDeprAsgnReadMapper;
import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.jp.med.ams.modules.depr.vo.AmsDeprAsgnVo;
import com.jp.med.ams.modules.depr.service.read.AmsDeprAsgnReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsDeprAsgnReadServiceImpl extends ServiceImpl<AmsDeprAsgnReadMapper, AmsDeprAsgnDto> implements AmsDeprAsgnReadService {

    @Autowired
    private AmsDeprAsgnReadMapper amsDeprAsgnReadMapper;

    @Override
    public List<AmsDeprAsgnVo> queryList(AmsDeprAsgnDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsDeprAsgnReadMapper.queryList(dto);
    }

}
