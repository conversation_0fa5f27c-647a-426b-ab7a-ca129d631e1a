package com.jp.med.erp.modules.vcrGen.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.erp.modules.vcrGen.dto.ErpReimPayReceiptDto;
import com.jp.med.erp.modules.vcrGen.service.read.ErpReimPayReceiptReadService;
import com.jp.med.erp.modules.vcrGen.service.write.ErpReimPayReceiptWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 费用报销付款回单信息
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 16:02:12
 */
@Api(value = "费用报销付款回单信息", tags = "费用报销付款回单信息")
@RestController
@RequestMapping("erpReimPayReceipt")
public class ErpReimPayReceiptController {

    @Autowired
    private ErpReimPayReceiptReadService erpReimPayReceiptReadService;

    @Autowired
    private ErpReimPayReceiptWriteService erpReimPayReceiptWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询费用报销付款回单信息")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody ErpReimPayReceiptDto dto){
        return CommonResult.paging(erpReimPayReceiptReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询费用报销付款回单信息")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody ErpReimPayReceiptDto dto){
        return CommonResult.success(erpReimPayReceiptReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增费用报销付款回单信息")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody ErpReimPayReceiptDto dto){
        erpReimPayReceiptWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改费用报销付款回单信息")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody ErpReimPayReceiptDto dto){
        erpReimPayReceiptWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除费用报销付款回单信息")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody ErpReimPayReceiptDto dto){
        erpReimPayReceiptWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 识别付款回单票据
     * @param dto
     * @return
     */
    @ApiOperation("识别付款回单票据")
    @PostMapping("/recogReceipt")
    public CommonResult<?> recogReceipt(@RequestBody ErpReimPayReceiptDto dto) {
        return CommonResult.success(erpReimPayReceiptWriteService.recogReceipt(dto));
    }



}
