package com.jp.med.ams.modules.changes.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.service.write.impl.AmsPropertyWriteServiceImpl;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量资产更新服务
 * 负责批量更新资产状态，避免多次单独调用
 */
@Service
@Slf4j
public class BatchPropertyUpdateService {

    @Autowired
    private AmsPropertyWriteServiceImpl amsPropertyWriteService;

    /**
     * 批量更新资产状态
     * 
     * @param propertyUpdates 需要更新的资产列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateProperties(List<AmsPropertyDto> propertyUpdates) {
        log.info("批量更新资产状态");
        if (propertyUpdates == null || propertyUpdates.isEmpty()) {
            return;
        }

        // 按更新类型分组，相同更新操作的资产可以批量处理
        Map<String, List<AmsPropertyDto>> groupedUpdates = propertyUpdates.stream()
                .collect(Collectors.groupingBy(this::getUpdateKey));

        for (Map.Entry<String, List<AmsPropertyDto>> entry : groupedUpdates.entrySet()) {
            List<AmsPropertyDto> sameTypeUpdates = entry.getValue();
            batchUpdateSameType(sameTypeUpdates);
        }
    }

    /**
     * 批量更新相同类型的资产
     */
    private void batchUpdateSameType(List<AmsPropertyDto> updates) {
        if (updates.isEmpty()) {
            return;
        }

        // 取第一个作为模板，获取更新字段
        AmsPropertyDto template = updates.get(0);
        List<String> faCodes = updates.stream()
                .map(AmsPropertyDto::getFaCode)
                .collect(Collectors.toList());

        // 构建批量更新条件
        LambdaUpdateWrapper<AmsPropertyDto> updateWrapper = Wrappers.lambdaUpdate(AmsPropertyDto.class);

        // 设置更新字段
        if (template.getIsCanc() != null) {
            updateWrapper.set(AmsPropertyDto::getIsCanc, template.getIsCanc());
        }
        if (template.getRedcWay() != null) {
            updateWrapper.set(AmsPropertyDto::getRedcWay, template.getRedcWay());
        }

        // 设置更新条件：资产编码在列表中
        updateWrapper.in(AmsPropertyDto::getFaCode, faCodes);

        // 执行批量更新
        amsPropertyWriteService.update(updateWrapper);
    }

    /**
     * 生成更新类型的键，用于分组
     */
    private String getUpdateKey(AmsPropertyDto dto) {
        return String.format("%s_%s",
                dto.getIsCanc() != null ? dto.getIsCanc() : "null",
                dto.getRedcWay() != null ? dto.getRedcWay() : "null");
    }
}
