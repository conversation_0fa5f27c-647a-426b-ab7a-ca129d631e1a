package com.jp.med.erp.modules.vcrGen.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 费用报销付款回单信息
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 16:02:12
 */
@Data
public class ErpReimPayReceiptVo {

	/** id */
	private Integer id;

	/** 报销id */
	private Integer reimDetailId;

	/** 对应生成辅助项id **/
	private Integer reimAsstId;

	/** 文件路径 */
	private String att;

	/** 文件名 */
	private String attName;

	/** 付款日期 */
	private String payDate;

	/** 付款金额 */
	private BigDecimal payAmt;

	/** 状态 1：识别成功 2：识别失败 3：手动修改 */
	private String status;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String crterTime;

	/** 更新人 */
	private String upder;

	/** 更新时间 */
	private String upderTime;

}
