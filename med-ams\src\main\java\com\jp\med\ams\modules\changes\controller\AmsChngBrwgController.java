package com.jp.med.ams.modules.changes.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.service.read.AmsChngBrwgReadService;
import com.jp.med.ams.modules.changes.service.write.AmsChngBrwgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 资产借用信息表
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 16:01:14
 */
@Api(value = "资产借用信息表", tags = "资产借用信息表")
@RestController
@RequestMapping("amsChngBrwg")
public class AmsChngBrwgController {

    @Autowired
    private AmsChngBrwgReadService amsChngBrwgReadService;

    @Autowired
    private AmsChngBrwgWriteService amsChngBrwgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产借用信息表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsChngBrwgDto dto){
        return CommonResult.paging(amsChngBrwgReadService.queryList(dto));
    }

    /**
     * 查询借用待审核数量
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询借用待审核数量")
    @PostMapping("/queryWarnNum")
    public CommonResult<?> queryLeaveApplyWarnNum(@RequestBody AmsChngBrwgDto dto) {
        return CommonResult.success(amsChngBrwgReadService.queryWarnNum(dto));
    }


    @ApiOperation("查询汇总")
    @PostMapping("/queryCount")
    public CommonResult<?> queryCount(@RequestBody AmsChngBrwgDto dto){
        return CommonResult.success(amsChngBrwgReadService.queryCount(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产借用信息表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsChngBrwgDto dto){

        amsChngBrwgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产借用信息表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsChngBrwgDto dto){
        amsChngBrwgWriteService.updateStatus(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改状态")
    @PostMapping("/updateData")
    public CommonResult<?> updateData(@RequestBody AmsChngBrwgDto dto){
        amsChngBrwgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产借用信息表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsChngBrwgDto dto){
        amsChngBrwgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
