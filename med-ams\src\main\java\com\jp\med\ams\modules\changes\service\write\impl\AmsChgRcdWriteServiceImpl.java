package com.jp.med.ams.modules.changes.service.write.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.mapper.write.AmsChgRcdWriteMapper;
import com.jp.med.ams.modules.changes.service.BatchPropertyUpdateService;
import com.jp.med.ams.modules.changes.service.write.AmsChgRcdWriteService;
import com.jp.med.ams.modules.changes.strategy.ChangeRecordProcessor;
import com.jp.med.ams.modules.changes.strategy.ChangeRecordProcessorFactory;
import com.jp.med.ams.modules.changes.strategy.ProcessContext;
import com.jp.med.ams.modules.changes.strategy.approval.ApprovalContext;
import com.jp.med.ams.modules.changes.strategy.approval.ApprovalProcessor;
import com.jp.med.ams.modules.changes.strategy.approval.ApprovalProcessorFactory;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.service.read.AmsMonthlyDepreciationPostingReadService;
import com.jp.med.ams.modules.property.service.read.impl.AmsPropertyReadServiceImpl;
import com.jp.med.ams.modules.property.service.write.impl.AmsPropertyWriteServiceImpl;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;

import cn.hutool.core.util.StrUtil;

/**
 * 资产变更记录表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
@Service
@Transactional(readOnly = false)
public class AmsChgRcdWriteServiceImpl extends ServiceImpl<AmsChgRcdWriteMapper, AmsChgRcdDto>
        implements AmsChgRcdWriteService {

    @Resource
    private AmsPropertyReadServiceImpl amsPropertyReadService;

    @Resource
    private AmsChgRcdWriteMapper amsChgRcdWriteMapper;

    @Resource
    private AmsPropertyWriteServiceImpl amsPropertyWriteService;

    @Resource
    private AmsMonthlyDepreciationPostingReadService amsMonthlyDepreciationPostingReadService;

    @Autowired
    private ChangeRecordProcessorFactory processorFactory;

    @Autowired
    private BatchPropertyUpdateService batchPropertyUpdateService;

    @Autowired
    private ApprovalProcessorFactory approvalProcessorFactory;

    @Override
    public boolean remove(AmsChgRcdDto amsChgRcdDto) {
        return false;
    }

    /**
     * 保存资产变更记录
     * 使用策略模式重构，支持不同类型的变更处理
     * 
     * @param dto 变更记录DTO
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveAmsChgRcd(AmsChgRcdDto dto) {
        try {
            // 1. 准备处理上下文
            ProcessContext context = createProcessContext();

            // 2. 获取合适的处理器
            ChangeRecordProcessor processor = processorFactory.getProcessor(dto);

            // 3. 执行处理
            ChangeRecordProcessor.ProcessResult result = processor.process(dto, context);

            if (!result.isSuccess()) {
                throw new AppException(result.getMessage());
            }

            // 4. 批量保存变更记录
            if (result.getChangeRecords() != null && !result.getChangeRecords().isEmpty()) {
                BatchUtil.batch(result.getChangeRecords(), AmsChgRcdWriteMapper.class);
            }

            // 5. 批量更新资产状态
            if (result.getPropertyUpdates() != null && !result.getPropertyUpdates().isEmpty()) {
                batchPropertyUpdateService.batchUpdateProperties(result.getPropertyUpdates());
            }

            return "处理成功";

        } catch (Exception e) {
            throw new AppException("保存变更记录失败: " + e.getMessage());
        }
    }

    /**
     * 创建处理上下文
     */
    private ProcessContext createProcessContext() {
        int maxChgCode = this.baseMapper.getMaxChgCode();
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDateTime = now.format(formatter);

        return new ProcessContext(maxChgCode, formattedDateTime,
                amsPropertyReadService, amsPropertyWriteService);
    }

    /**
     * 审批资产变更记录
     * 使用策略模式重构，支持不同类型的审批处理，包括取消审批
     * 
     * @param dto 包含审批信息的数据传输对象
     * @throws AppException 如果审批过程中出现错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approval(AmsChgRcdDto dto) {
        try {
            // 1. 检测固定资产扎帐审批操作
            if (StrUtil.equals("1", dto.getType())) {
                if (amsMonthlyDepreciationPostingReadService.isExistPostingLock()) {
                    throw new AppException("扎帐期间不允许审批固定资产变更");
                }
            }

            // 2. 验证参数
            List<Integer> ids = dto.getIds();
            if (ids == null || ids.isEmpty()) {
                throw new AppException("ID 列表不能为空");
            }

            // 3. 获取变更记录
            List<AmsChgRcdDto> changeRecords = this.baseMapper.selectBatchIds(ids);
            if (changeRecords.isEmpty()) {
                throw new AppException("变动记录不存在");
            }

            // 4. 创建审批上下文
            ApprovalContext context = createApprovalContext();

            // 5. 处理每个变更记录的审批
            List<AmsPropertyDto> allPropertyUpdates = new ArrayList<>();

            for (AmsChgRcdDto changeRecord : changeRecords) {
                // 获取合适的审批处理器
                ApprovalProcessor processor = approvalProcessorFactory.getProcessor(changeRecord, dto);

                // 执行审批处理
                ApprovalProcessor.ApprovalResult result = processor.process(changeRecord, dto, context);

                if (!result.isSuccess()) {
                    throw new AppException(result.getMessage());
                }

                // 收集资产更新信息
                if (result.getPropertyUpdates() != null) {
                    allPropertyUpdates.addAll(result.getPropertyUpdates());
                }
            }

            // 6. 批量更新资产状态
            if (!allPropertyUpdates.isEmpty()) {
                batchPropertyUpdateService.batchUpdateProperties(allPropertyUpdates);
            }

            // 7. 批量更新审批状态
            if (dto.getPass() != null && dto.getPass()) {
                amsChgRcdWriteMapper.approval(dto);
            } else {
                amsChgRcdWriteMapper.unApproval(dto);
            }

        } catch (Exception e) {
            throw new AppException("审批处理失败: " + e.getMessage());
        }
    }

    /**
     * 创建审批处理上下文
     */
    private ApprovalContext createApprovalContext() {
        return new ApprovalContext(amsPropertyWriteService);
    }

}
