package com.jp.med.ams.modules.changes.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.changes.dto.AmsAllocDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 资产划拨
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
@Mapper
public interface AmsAllocWriteMapper extends BaseMapper<AmsAllocDto> {

    void updateAssetInfo(String bchno);

    /**
     * 更新资产划拨业务状态
     *
     * @param bchno
     * @param prosstas
     */
    void updateApplyProsstas(@Param("bchno") String bchno, @Param("prosstas") String prosstas);

    /**
     * 新增资产划拨属性关联
     * @param amsAllocDto
     * @return
     */
    int insertAllocPropertyRef(AmsAllocDto amsAllocDto);



}
