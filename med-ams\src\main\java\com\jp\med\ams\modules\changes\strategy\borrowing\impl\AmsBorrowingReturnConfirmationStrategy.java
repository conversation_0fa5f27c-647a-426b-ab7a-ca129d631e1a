package com.jp.med.ams.modules.changes.strategy.borrowing.impl;

import com.jp.med.ams.modules.changes.dto.AmsBorrowingAuditNotificationDto;
import com.jp.med.ams.modules.changes.dto.AmsChngBrwgDto;
import com.jp.med.ams.modules.changes.mapper.write.AmsChngBrwgWriteMapper;
import com.jp.med.ams.modules.changes.service.notification.AmsBorrowingNotificationService;
import com.jp.med.ams.modules.changes.strategy.borrowing.AmsBorrowingAuditStrategy;
import com.jp.med.ams.modules.changes.strategy.borrowing.context.AmsBorrowingAuditContext;
import com.jp.med.ams.modules.changes.vo.AmsChngBrwgVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产借用归还确认策略
 * 处理借用资产归还确认的业务逻辑
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Slf4j
@Component
public class AmsBorrowingReturnConfirmationStrategy implements AmsBorrowingAuditStrategy {

    @Autowired
    private AmsChngBrwgWriteMapper amsChngBrwgWriteMapper;

    @Autowired
    private AmsPropertyWriteMapper amsPropertyWriteMapper;

    @Autowired
    private AmsBorrowingNotificationService notificationService;

    @Override
    public boolean executeAudit(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        log.info("🔄 执行借用归还确认策略，确认人：{}", context.getAuditorName());

        try {
            if (context.isBatchOperation()) {
                return executeBatchReturnConfirmation(context, dto);
            } else {
                return executeSingleReturnConfirmation(context, dto);
            }
        } catch (Exception e) {
            log.error("❌ 借用归还确认策略执行失败", e);
            return false;
        }
    }

    /**
     * 执行批量归还确认
     */
    private boolean executeBatchReturnConfirmation(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        List<AmsChngBrwgDto> updateDtoList = new ArrayList<>();
        List<AmsPropertyDto> propertyDtoList = new ArrayList<>();

        // 构建批量更新数据
        for (Integer id : dto.getIds()) {
            AmsChngBrwgVo borrowingRecord = context.getBorrowingRecordMap().get(id);
            if (borrowingRecord == null) {
                log.warn("⚠️ 未找到借用记录，ID：{}", id);
                continue;
            }

            // 构建借用记录更新DTO
            AmsChngBrwgDto updateDto = buildReturnConfirmationUpdateDto(dto, id, context.getAuditTime());
            updateDtoList.add(updateDto);

            // 构建资产状态更新DTO（归还后资产状态改为在库）
            AmsPropertyDto propertyDto = new AmsPropertyDto();
            propertyDto.setFaCode(borrowingRecord.getFaCode());
            propertyDto.setAssetStatus(MedConst.ASSET_STATUS_1); // 在库
            propertyDtoList.add(propertyDto);
        }

        // 批量更新资产状态
        if (!CollectionUtils.isEmpty(propertyDtoList)) {
            BatchUtil.batch("updateByFaCode", propertyDtoList, AmsPropertyWriteMapper.class);
        }

        // 批量更新借用记录
        if (!CollectionUtils.isEmpty(updateDtoList)) {
            BatchUtil.batch("updateById", updateDtoList, AmsChngBrwgWriteMapper.class);
        }

        // 发送批量归还确认通知
        sendBatchReturnConfirmationNotifications(context, dto);

        log.info("✅ 批量借用归还确认完成，处理记录数：{}", dto.getIds().size());
        return true;
    }

    /**
     * 执行单个归还确认
     */
    private boolean executeSingleReturnConfirmation(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        // 更新借用记录
        AmsChngBrwgDto updateDto = buildReturnConfirmationUpdateDto(dto, dto.getId(), context.getAuditTime());
        int updateResult = amsChngBrwgWriteMapper.updateById(updateDto);

        if (updateResult != 1) {
            log.error("❌ 更新借用记录失败，ID：{}", dto.getId());
            return false;
        }

        // 更新资产状态
        if (!CollectionUtils.isEmpty(context.getBorrowingRecords())) {
            AmsChngBrwgVo borrowingRecord = context.getBorrowingRecords().get(0);

            AmsPropertyDto propertyDto = new AmsPropertyDto();
            propertyDto.setFaCode(borrowingRecord.getFaCode());
            propertyDto.setAssetStatus(MedConst.ASSET_STATUS_1); // 在库
            amsPropertyWriteMapper.updateByFaCode(propertyDto);

            // 发送单个归还确认通知
            sendSingleReturnConfirmationNotification(context, borrowingRecord);
        }

        log.info("✅ 单个借用归还确认完成，记录ID：{}", dto.getId());
        return true;
    }

    /**
     * 构建归还确认更新DTO
     */
    private AmsChngBrwgDto buildReturnConfirmationUpdateDto(AmsChngBrwgDto originalDto, Integer id, String auditTime) {
        AmsChngBrwgDto updateDto = new AmsChngBrwgDto();
        updateDto.setId(id);
        updateDto.setProsstas(MedConst.TYPE_5); // 归还确认完成
        updateDto.setBusstas(MedConst.TYPE_3); // 已归还
        updateDto.setChkRemarks(originalDto.getChkRemarks());
        updateDto.setChkTime(auditTime);
        updateDto.setActRtnTime(auditTime); // 实际归还时间
        return updateDto;
    }

    /**
     * 发送批量归还确认通知
     */
    private void sendBatchReturnConfirmationNotifications(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        try {
            for (Integer id : dto.getIds()) {
                AmsChngBrwgVo borrowingRecord = context.getBorrowingRecordMap().get(id);
                if (borrowingRecord != null) {
                    AmsBorrowingAuditNotificationDto notificationDto =
                            AmsBorrowingAuditNotificationDto.buildReturnConfirmationNotification(
                                    borrowingRecord, context.getAuditorName(),
                                    context.getAuditorDept(), context.getAuditTime());

                    notificationService.sendReturnConfirmationNotification(borrowingRecord, notificationDto);
                }
            }
        } catch (Exception e) {
            log.warn("⚠️ 发送批量归还确认通知失败", e);
        }
    }

    /**
     * 发送单个归还确认通知
     */
    private void sendSingleReturnConfirmationNotification(AmsBorrowingAuditContext context, AmsChngBrwgVo borrowingRecord) {
        try {
            AmsBorrowingAuditNotificationDto notificationDto =
                    AmsBorrowingAuditNotificationDto.buildReturnConfirmationNotification(
                            borrowingRecord, context.getAuditorName(),
                            context.getAuditorDept(), context.getAuditTime());

            notificationService.sendReturnConfirmationNotification(borrowingRecord, notificationDto);
        } catch (Exception e) {
            log.warn("⚠️ 发送单个归还确认通知失败", e);
        }
    }

    @Override
    public String getStrategyType() {
        return "RETURN_CONFIRMATION";
    }

    @Override
    public boolean validateAuditParams(AmsBorrowingAuditContext context, AmsChngBrwgDto dto) {
        if (context == null || context.getAuditor() == null) {
            log.error("❌ 审核上下文或审核人信息为空");
            return false;
        }

        if (dto == null) {
            log.error("❌ 审核数据为空");
            return false;
        }

        if (context.isBatchOperation() && CollectionUtils.isEmpty(dto.getIds())) {
            log.error("❌ 批量操作时ID列表为空");
            return false;
        }

        if (!context.isBatchOperation() && dto.getId() == null) {
            log.error("❌ 单个操作时ID为空");
            return false;
        }

        // 验证目标状态是否为归还确认
        if (!MedConst.TYPE_5.equals(context.getTargetProcessStatus())) {
            log.error("❌ 归还确认策略的目标状态应为5（归还确认完成）");
            return false;
        }

        return true;
    }
}
