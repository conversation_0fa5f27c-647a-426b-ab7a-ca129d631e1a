package com.jp.med.ams.modules.it.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 信息科库房耗材申请主表
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
@TableName("ams_it_invt_apply")
public class AmsItInvtApplyEntity {


    @TableId("id")
	private Integer id;

	/** 申请科室 */
	@TableField("apply_org")
	private String applyOrg;

	/** 申请理由 */
	@TableField("apply_remark")
	private String applyRemark;

	/** 审核备注 */
	@TableField("chk_remark")
	private String chkRemark;

	/** 审核状态(0:待审核,1:审核通过,2:审核拒绝) */
	@TableField("status")
	private String status;

	/** 审核人 */
	@TableField("chker")
	private String chker;

	/** 审核时间 */
	@TableField("chk_time")
	private String chkTime;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 更新人 */
	@TableField("updtr")
	private String updtr;

	/** 更新时间 */
	@TableField("update_time")
	private String updateTime;

	/** 删除人 */
	@TableField("delter")
	private String delter;

	/** 删除时间 */
	@TableField("delete_time")
	private String deleteTime;

	/** 有效标志(1:删除) */
	@TableField("active_flag")
	private String activeFlag;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
