package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary;

import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.mapper.read.AmsBasicCfgReadMapper;
import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprProcessContext;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 资金来源配置处理器
 * 负责初始化资金来源配置和基础数据
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertySourceConfigProcessor extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprProcessContext> {

        @Autowired
        private AmsBasicCfgReadMapper amsBasicCfgReadMapper;

        @Override
        protected void doProcess(AmsPropertyDeprProcessContext context) {
                log.debug("开始处理资金来源配置");

                // 1. 查询资金来源配置
                AmsBasicCfgDto amsBasicCfgDto = new AmsBasicCfgDto();
                amsBasicCfgDto.setType("资金来源");
                List<AmsBasicCfgVo> sourceList = amsBasicCfgReadMapper.queryList(amsBasicCfgDto);
                context.setSourceList(sourceList);

                // 2. 构建资金来源代码映射
                Map<String, String> sourceCodeMap = sourceList.stream()
                                .collect(Collectors.toMap(
                                                AmsBasicCfgVo::getCode, // 使用 code 作为键
                                                AmsBasicCfgVo::getName // 使用 name 作为值
                                ));
                context.setSourceCodeMap(sourceCodeMap);

                // 3. 获取财政补助资金代码
                String finaSubsidyCode = sourceList.stream()
                                .filter(item -> item.getName().equals("财政补助资金"))
                                .findFirst()
                                .map(AmsBasicCfgVo::getCode)
                                .orElse(null);
                context.setFinaSubsidyCode(finaSubsidyCode);

                // 4. 计算月份最后一天
                String ym = context.getYm();
                String lastDay = ym + String.format("%02d",
                                YearMonth.parse(ym, DateTimeFormatter.ofPattern("yyyyMM")).lengthOfMonth());
                LocalDate lastDayOfMonth = LocalDate.parse(lastDay, DateTimeFormatter.ofPattern("yyyyMMdd"));
                context.setLastDayOfMonth(lastDayOfMonth);

                log.debug("资金来源配置处理完成，共加载 {} 个资金来源", sourceList.size());
        }
}
