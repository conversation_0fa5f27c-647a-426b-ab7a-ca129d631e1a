package com.jp.med.ams.modules.it.mapper.write;

import com.jp.med.ams.modules.it.dto.AmsItApplyDetailDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtApplyDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 耗材申请详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItApplyDetailWriteMapper extends BaseMapper<AmsItApplyDetailDto> {
    void batchSave(List<AmsItApplyDetailDto> applyDetail);

    void deleteByUpdate(AmsItInvtApplyDto dto);
}
