package com.jp.med.ams.modules.amsPropertyInAndOut.vo;

import lombok.Data;

/**
 * 资产入库信息填写配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-22 15:34:39
 */
@Data
public class AmsInStockConfigVo {

    /**
     * id
     */
    private Integer id;

    /**
     * 入库科室
     */
    private String outDept;
    private String outDeptName;

    /**
     * 字段
     */
    private String fld;

    /**
     * 是否必填(1：必填)
     */
    private String mustl;

    /**
     * 字段类型（1：资产字段）
     */
    private String fldType;

    /* 项目字段数量*/
    private String count;

}
