package com.jp.med.ams.modules.it.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.service.read.AmsItInvtCfgReadService;
import com.jp.med.ams.modules.it.service.write.AmsItInvtCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 信息科库房耗材配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Api(value = "信息科库房耗材配置", tags = "信息科库房耗材配置")
@RestController
@RequestMapping("amsItInvtCfg")
public class AmsItInvtCfgController {

    @Autowired
    private AmsItInvtCfgReadService amsItInvtCfgReadService;

    @Autowired
    private AmsItInvtCfgWriteService amsItInvtCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询信息科库房耗材配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsItInvtCfgDto dto){
        return CommonResult.success(amsItInvtCfgReadService.cfgList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询信息科库房耗材配置")
    @PostMapping("/queryList")
    public CommonResult<?> queryList(@RequestBody AmsItInvtCfgDto dto){
        return CommonResult.paging(amsItInvtCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增信息科库房耗材配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsItInvtCfgDto dto){
        amsItInvtCfgWriteService.saveDto(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改信息科库房耗材配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsItInvtCfgDto dto){
        amsItInvtCfgWriteService.updateCfgById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除信息科库房耗材配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsItInvtCfgDto dto){
        amsItInvtCfgWriteService.removeCfgById(dto);
        return CommonResult.success();
    }

}
