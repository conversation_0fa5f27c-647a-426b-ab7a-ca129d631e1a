package com.jp.med.ams.modules.inventory.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrDetailReadMapper;
import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrReadMapper;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrDetailWriteMapper;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrWriteMapper;
import com.jp.med.ams.modules.inventory.service.InventoryLockService;
import com.jp.med.ams.modules.inventory.service.write.AmsIntrWriteService;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产盘点主表写服务实现类
 * <p>
 * 处理资产盘点主记录 (`ams_intr`) 的创建以及盘点数据的同步操作。
 * </p>
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Service
@Transactional(readOnly = false)
public class AmsIntrWriteServiceImpl extends ServiceImpl<AmsIntrWriteMapper, AmsIntrDto>
        implements AmsIntrWriteService {

    @Resource
    private AmsIntrWriteMapper amsIntrWriteMapper;
    @Qualifier("amsIntrDetailReadMapper")
    @Autowired
    private AmsIntrDetailReadMapper amsIntrDetailReadMapper;

    @Resource
    private AmsIntrReadMapper amsIntrReadMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private InventoryLockService inventoryLockService;

    /**
     * 添加盘点记录及明细 (此方法似乎用于特定场景下的数据接入)。
     * <p>
     * 1. 从传入的 `map` 中提取数据 (`data`)、任务 ID (`taskId`) 和医院 ID (`hospitalId`)。
     * 2. 如果 `data` 为空，则直接返回。
     * 3. 创建并设置 `AmsIntrDto` (盘点主记录) 的同步时间、总数量和医院 ID。
     * 4. 插入 `ams_intr` 记录。
     * 5. 获取插入后生成的盘点主记录 ID (`intrId`)。
     * 6. 遍历 `data` 中的 key (似乎是 UID)，为每个 key 创建一个 `AmsIntrDetailDto` (盘点明细)。
     * 7. 设置明细的 UID、关联的 `intrId` 和 `taskId`。
     * 8. 批量插入 `ams_intr_detail` 记录。
     * </p>
     *
     * @param map 包含盘点数据 (`data`)、任务 ID (`taskId`) 和医院 ID (`hospitalId`) 的 Map。
     */
    @Override
    public void addIntr(Map<String, Object> map) {
        AmsIntrDto intrDto = new AmsIntrDto();
        Map<String, Object> result = (Map<String, Object>) map.get("data");
        if (result.isEmpty()) {
            return;
        }
        String taskId = String.valueOf(map.get("taskId"));
        String hospitalId = String.valueOf(map.get("hospitalId"));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        intrDto.setSynctime(sdf.format(new Date()));
        intrDto.setTotlcnt(result.size());
        intrDto.setHospitalId(hospitalId);
        amsIntrWriteMapper.insert(intrDto);
        List<AmsIntrDetailDto> detailDtoList = new ArrayList<>();
        for (String key : result.keySet()) {
            AmsIntrDetailDto intrDetailDto = new AmsIntrDetailDto();
            intrDetailDto.setUid(key);
            intrDetailDto.setIntrId(intrDto.getId());
            intrDetailDto.setTaskId(Integer.parseInt(taskId));
            detailDtoList.add(intrDetailDto);
        }
        BatchUtil.batch("insert", detailDtoList, AmsIntrDetailWriteMapper.class);
    }

    /**
     * 同步盘点明细数据。
     * <p>
     * 1. 校验 `intrId` 是否为空。
     * 2. (注释掉的代码：原意可能是使用 Redis 防止短时间内重复同步同一任务)。
     * 3. 获取传入的 UID 列表 (`dto.list`) 并去重。
     * 4. 校验 `intrId` 对应的盘点主记录是否存在。
     * 5. 查询该盘点任务 (`intrId`, `taskId`) 下已经存在的 UID 列表 (`oldUidList`)。
     * 6. 过滤传入的 UID 列表，找出本次新增的 UID (`newUidList`)。
     * 7. 遍历 `newUidList`，为每个新增的 UID 创建 `AmsIntrDetailDto` 对象。
     * 8. 设置明细的 `taskId`, `intrId`, `isManual=0` (非手动录入) 和 `uid`。
     * 9. 批量插入新增的盘点明细记录。
     * 10. 返回本次同步后该任务的总盘点数量 (已存在数量 + 新增数量)。
     * </p>
     *
     * @param dto 包含盘点主记录 ID (`intrId`)、任务 ID (`taskId`) 和本次扫描到的 UID 列表 (`list`) 的
     *            DTO。
     * @return 本次同步后该任务的总盘点明细数量。
     * @throws AppException 如果 `intrId` 为空或对应的盘点任务不存在。
     */
    @Override
    public Integer synchronousData(AmsIntrTodoDto dto) {
        if (dto.getIntrId() == null) {
            throw new AppException("盘点任务id is null");
        }
        // if
        // (Boolean.TRUE.equals(stringRedisTemplate.hasKey(StrUtil.format("synchronousData::{}::{}",
        // dto.getIntrId(), dto.getTaskId())))) {
        // return 0;
        // } else {
        // stringRedisTemplate.opsForValue().set(StrUtil.format("synchronousData::{}::{}",
        // dto.getIntrId(), dto.getTaskId()), "1", 60L, TimeUnit.SECONDS);

        // }

        List<String> detailDtoList = dto.getList();
        Set<String> detailIdSet = new HashSet<>(detailDtoList);

        AmsIntrDto amsIntrDto = amsIntrReadMapper.selectById(dto.getIntrId());
        if (amsIntrDto == null) {
            throw new AppException("盘点任务不存在");
        }
        List<AmsIntrDetailDto> insertAmsIntrDetailDtos = new ArrayList<>();
        // Pattern pattern = Pattern.compile("\\d{12}");
        // 去重 在一次任务中只需要盘点一次
        LambdaQueryWrapper<AmsIntrDetailDto> eq = new QueryWrapper<AmsIntrDetailDto>()
                .lambda()
                .select(AmsIntrDetailDto::getUid)
                .eq(AmsIntrDetailDto::getIntrId, dto.getIntrId())
                .eq(AmsIntrDetailDto::getTaskId, dto.getTaskId())
                .groupBy(AmsIntrDetailDto::getUid);
        // 已盘点列表
        List<AmsIntrDetailDto> intrDetailDtos = amsIntrDetailReadMapper.selectList(eq);
        List<String> oldUidList = intrDetailDtos.stream().map(item -> removeLeadingZeros(item.getUid()))
                .collect(Collectors.toList());
        Set<String> newUidList = detailIdSet.stream()
                .filter(item -> !oldUidList.contains(item))
                .collect(Collectors.toSet());
        for (String uid : newUidList) {
            String id = removeLeadingZeros(uid);
            // Matcher matcher = pattern.matcher(str);
            if (!id.isEmpty()) {
                AmsIntrDetailDto detailDto = new AmsIntrDetailDto();
                detailDto.setTaskId(dto.getTaskId());
                detailDto.setIntrId(dto.getIntrId());
                detailDto.setIsManual(0);
                detailDto.setUid(uid);
                insertAmsIntrDetailDtos.add(detailDto);
            }
        }

        BatchUtil.batch("insert", insertAmsIntrDetailDtos, AmsIntrDetailWriteMapper.class);

        // 已盘点数量+新增数量
        return intrDetailDtos.size() + newUidList.size();
    }

    /**
     * 同步盘点明细数据（重构优化版）
     * <p>
     * 优化内容：
     * 1. 简化UID映射逻辑，提升性能
     * 2. 减少数据库查询次数
     * 3. 优化批量插入操作
     * 4. 统一返回格式，便于前端处理
     *
     * @param dto 包含盘点主记录ID、任务ID和UID列表的DTO
     * @return 包含详细同步结果的Map
     * @throws AppException 如果参数无效或盘点任务不存在
     * <AUTHOR>
     * @date 2025-01-19
     */
    @Override
    public Map<String, Object> synchronousDataEnhanced(AmsIntrTodoDto dto) {
        // 使用分布式锁防止并发同步
        return inventoryLockService.executeWithLock(
                "sync:" + dto.getTaskId(),
                30L,
                () -> doSynchronousDataEnhanced(dto)
        );
    }

    /**
     * 执行同步数据的核心逻辑（带锁保护）
     */
    private Map<String, Object> doSynchronousDataEnhanced(AmsIntrTodoDto dto) {
        // 1. 参数校验
        validateSyncRequest(dto);

        // 2. 预处理UID数据
        SyncDataContext context = prepareSyncData(dto);

        // 3. 查询已存在的记录
        Set<String> existingUids = getExistingUids(dto.getIntrId(), dto.getTaskId());

        // 4. 计算需要插入的新记录
        Set<String> newUids = calculateNewUids(context.normalizedUids, existingUids);

        // 5. 批量插入新记录
        if (!newUids.isEmpty()) {
            batchInsertDetails(dto, context.uidMapping, newUids);
        }

        // 6. 构建返回结果
        return buildSyncResult(existingUids, newUids, context.normalizedUids);
    }

    /**
     * 校验同步请求参数
     */
    private void validateSyncRequest(AmsIntrTodoDto dto) {
        if (dto.getIntrId() == null) {
            throw new AppException("盘点任务ID不能为空");
        }

        if (dto.getTaskId() == null) {
            throw new AppException("任务ID不能为空");
        }

        if (dto.getList() == null || dto.getList().isEmpty()) {
            throw new AppException("UID列表不能为空");
        }

        // 校验盘点任务是否存在
        AmsIntrDto amsIntrDto = amsIntrReadMapper.selectById(dto.getIntrId());
        if (amsIntrDto == null) {
            throw new AppException("盘点任务不存在");
        }
    }

    /**
     * 预处理同步数据
     */
    private SyncDataContext prepareSyncData(AmsIntrTodoDto dto) {
        Map<String, String> uidMapping = new HashMap<>();
        Set<String> normalizedUids = new HashSet<>();

        for (String originalUid : dto.getList()) {
            String normalizedUid = removeLeadingZeros(originalUid);
            uidMapping.put(normalizedUid, originalUid);
            normalizedUids.add(normalizedUid);
        }

        return new SyncDataContext(uidMapping, normalizedUids);
    }

    /**
     * 获取已存在的UID列表
     */
    private Set<String> getExistingUids(Integer intrId, Integer taskId) {
        return amsIntrDetailReadMapper.selectList(
                        new QueryWrapper<AmsIntrDetailDto>().lambda()
                                .select(AmsIntrDetailDto::getUid)
                                .eq(AmsIntrDetailDto::getIntrId, intrId)
                                .eq(AmsIntrDetailDto::getTaskId, taskId)
                ).stream()
                .map(item -> removeLeadingZeros(item.getUid()))
                .collect(Collectors.toSet());
    }

    /**
     * 计算需要插入的新UID
     */
    private Set<String> calculateNewUids(Set<String> normalizedUids, Set<String> existingUids) {
        return normalizedUids.stream()
                .filter(uid -> !existingUids.contains(uid))
                .collect(Collectors.toSet());
    }

    /**
     * 批量插入盘点明细
     */
    private void batchInsertDetails(AmsIntrTodoDto dto, Map<String, String> uidMapping, Set<String> newUids) {
        List<AmsIntrDetailDto> insertList = newUids.stream()
                .map(normalizedUid -> {
                    AmsIntrDetailDto detail = new AmsIntrDetailDto();
                    detail.setTaskId(dto.getTaskId());
                    detail.setIntrId(dto.getIntrId());
                    detail.setIsManual(0);
                    detail.setUid(uidMapping.get(normalizedUid)); // 使用原始UID格式
                    return detail;
                })
                .collect(Collectors.toList());

        BatchUtil.batch("insert", insertList, AmsIntrDetailWriteMapper.class);
    }

    /**
     * 构建同步结果
     */
    private Map<String, Object> buildSyncResult(Set<String> existingUids, Set<String> newUids, Set<String> allRequestUids) {
        int totalCount = existingUids.size() + newUids.size();
        int duplicateCount = allRequestUids.size() - newUids.size();

        // 合并所有已盘点的UID
        Set<String> allInventoriedUids = new HashSet<>(existingUids);
        allInventoriedUids.addAll(newUids);

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("newCount", newUids.size());
        result.put("duplicateCount", duplicateCount);
        result.put("newUids", new ArrayList<>(newUids));
        result.put("allInventoriedUids", new ArrayList<>(allInventoriedUids));

        return result;
    }

    /**
     * 移除字符串前导零。
     *
     * @param str 输入字符串
     * @return 移除前导零后的字符串
     */
    public String removeLeadingZeros(String str) {
        if (str == null || str.isEmpty()) {
            return "";
        }
        int i = 0;
        while (i < str.length() && str.charAt(i) == '0') {
            i++;
        }
        return str.substring(i);
    }

    /**
     * 同步数据上下文类
     */
    private static class SyncDataContext {
        final Map<String, String> uidMapping;
        final Set<String> normalizedUids;

        SyncDataContext(Map<String, String> uidMapping, Set<String> normalizedUids) {
            this.uidMapping = uidMapping;
            this.normalizedUids = normalizedUids;
        }
    }

    /**
     * 开始一个新的盘点，创建盘点主记录。
     * <p>
     * 1. 创建一个新的 `AmsIntrDto` 对象。
     * 2. 设置同步时间为当前时间。
     * 3. 设置初始总数量为 0。
     * 4. 设置关联的任务 ID (`taskId`)、医院 ID (`hospitalId`) 和操作员 (`opter`)。
     * 5. 插入 `ams_intr` 记录。
     * 6. 返回创建的 `AmsIntrDto` 对象 (包含生成的 ID)。
     * </p>
     *
     * @param dto 包含任务 ID (`taskId`)、医院 ID (`hospitalId`) 和当前用户信息 (`sysUser`) 的 DTO。
     * @return 创建的盘点主记录 DTO (包含生成的 ID)。
     */
    @Override
    public AmsIntrDto startIntr(AmsIntrDto dto) {

        AmsIntrDto intrDto = new AmsIntrDto();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        intrDto.setSynctime(sdf.format(new Date()));
        intrDto.setTotlcnt(0);
        intrDto.setTaskId(dto.getTaskId());
        intrDto.setHospitalId(dto.getHospitalId());
        intrDto.setOpter(dto.getSysUser().getUsername());
        amsIntrWriteMapper.insert(intrDto);

        return intrDto;
    }
}
