package com.jp.med.ams.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产配置表
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:47:25
 */
@Data
public class AmsBasicCfgVo {

	/** ID */
	private Integer id;

	/** 编码 */
	private String code;

	/** 名称 */
	private String name;

	/** 说明 */
	private String dscr;

	/** 类型 */
	private String type;

	/** 状态 */
	private String flag;

}
