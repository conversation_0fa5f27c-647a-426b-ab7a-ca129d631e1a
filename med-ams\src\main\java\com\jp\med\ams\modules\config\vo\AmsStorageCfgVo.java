package com.jp.med.ams.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 存放地点配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-12 10:23:40
 */
@Data
public class AmsStorageCfgVo {

	/** ID */
	private Integer id;

	/** 存放地点编码 */
	private String storageAreaCode;

	/** 存放地点 */
	private String storageArea;

	/** 上级编码 */
	private String parentId;

	/** 医疗机构编码 */
	private String hospitalId;

	/** 有效标志 */
	private String activeFlag;

}
