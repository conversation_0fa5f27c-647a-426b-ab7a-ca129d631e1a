package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary2;

import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import com.jp.med.ams.modules.depr.vo.AmsPropertyDepr2Vo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 上月独有数据处理器
 * 负责处理上月存在但本月不存在的数据
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertyLastMonthOnlyProcessor
        extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> {

    @Override
    protected void doProcess(AmsPropertyDeprSummary2Context context) {
        log.debug("开始处理上月独有数据");

        // 处理上月存在但本月不存在的数据
        handleLastMonthOnlyData(
                context.getCurrentMonthPropertyDepr2Vos(),
                context.getCurrentMonthMap(),
                context.getLastMonthMap(),
                context.isUseTypeNCode());

        log.debug("上月独有数据处理完成");
    }

    /**
     * 处理上月独有数据
     * 
     * @param currentMonthPropertyDepr2Vos 本月折旧数据列表
     * @param currentMonthMap              本月数据映射
     * @param lastMonthMap                 上月数据映射
     * @param useTypeNCode                 是否使用新类型代码
     */
    private void handleLastMonthOnlyData(java.util.List<AmsPropertyDepr2Vo> currentMonthPropertyDepr2Vos,
            Map<String, AmsPropertyDepr2Vo> currentMonthMap,
            Map<String, AmsPropertyDepr2Vo> lastMonthMap,
            boolean useTypeNCode) {

        // 反向遍历上月 Map 的 Key
        for (Map.Entry<String, AmsPropertyDepr2Vo> entry : lastMonthMap.entrySet()) {
            String key = entry.getKey();
            AmsPropertyDepr2Vo lastMonthVo = entry.getValue();

            // 如果本月没有这个 Key，说明是上月独有的数据
            if (!currentMonthMap.containsKey(key)) {
                // 创建一个新的 VO，表示本月为0，上月有值的情况
                AmsPropertyDepr2Vo newVo = new AmsPropertyDepr2Vo();
                BeanUtils.copyProperties(lastMonthVo, newVo);

                // 设置本月数据为0
                newVo.setMonthDeprAmt(BigDecimal.ZERO);
                newVo.setDeprAmt(BigDecimal.ZERO);
                newVo.setDeprAmtSum(BigDecimal.ZERO);
                newVo.setDeprRate(BigDecimal.ZERO);

                // 设置上月数据
                newVo.setLastMonthDeprAmt(lastMonthVo.getMonthDeprAmt());

                // 计算变化总额（本月0 - 上月值 = 负的上月值）
                BigDecimal change = BigDecimal.ZERO;
                if (lastMonthVo.getMonthDeprAmt() != null) {
                    change = lastMonthVo.getMonthDeprAmt().negate();
                }
                newVo.setLastMonthDeprAmtChange(change);

                // 添加到本月列表中
                currentMonthPropertyDepr2Vos.add(newVo);

                // 同时添加到本月映射中，避免重复处理
                currentMonthMap.put(key, newVo);
            }
        }
    }
}
