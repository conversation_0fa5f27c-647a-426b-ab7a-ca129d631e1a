package com.jp.med.ams.modules.config.mapper.read;

import com.jp.med.ams.modules.config.dto.AmsCfgCcmdDto;
import com.jp.med.ams.modules.config.vo.AmsCfgCcmdVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-08-30 09:35:21
 */
@Mapper
public interface AmsCfgCcmdReadMapper extends BaseMapper<AmsCfgCcmdDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsCfgCcmdVo> queryList(AmsCfgCcmdDto dto);
}
