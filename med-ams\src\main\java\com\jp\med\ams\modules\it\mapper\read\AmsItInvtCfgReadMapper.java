package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 信息科库房耗材配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItInvtCfgReadMapper extends BaseMapper<AmsItInvtCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItInvtCfgVo> queryList(AmsItInvtCfgDto dto);

    AmsItInvtCfgVo selectByName(AmsItInvtCfgDto dto);

    List<AmsItInvtCfgVo> queryConsumableList(AmsItInvtCfgDto dto);

    List<AmsItInvtCfgVo> queryAlarmList(AmsItInvtCfgDto dto);

    Integer queryMaxId();
}
