package com.jp.med.ams.modules.inventory.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 待盘点资产表
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 13:56:27
 */
@Data
@TableName("ams_intr_todo" )
public class AmsIntrTodoDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 盘点任务ID */
    @TableField(value = "task_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private Integer taskId;

    /** 固定资产ID */
    @TableField(value = "uid",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String uid;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField(exist = false)
    private List<String> list;

    /**
     * 盘点任务id
     */
    @TableField(exist = false)
    private Integer intrId;

}
