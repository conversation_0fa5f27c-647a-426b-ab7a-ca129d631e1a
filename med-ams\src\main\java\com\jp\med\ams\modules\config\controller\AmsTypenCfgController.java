package com.jp.med.ams.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.config.dto.AmsTypenCfgDto;
import com.jp.med.ams.modules.config.service.read.AmsTypenCfgReadService;
import com.jp.med.ams.modules.config.service.write.AmsTypenCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 新资产分类
 * <AUTHOR>
 * @email -
 * @date 2023-12-06 11:20:53
 */
@Api(value = "新资产分类", tags = "新资产分类")
@RestController
@RequestMapping("amsTypenCfg")
public class AmsTypenCfgController {

    @Autowired
    private AmsTypenCfgReadService amsTypenCfgReadService;

    @Autowired
    private AmsTypenCfgWriteService amsTypenCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询新资产分类")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsTypenCfgDto dto){
        return CommonResult.success(amsTypenCfgReadService.queryList(dto));
    }

    @ApiOperation("查询新资产分类")
    @PostMapping("/queryNormal")
    public CommonResult<?> queryNormal(@RequestBody AmsTypenCfgDto dto){
        return CommonResult.success(amsTypenCfgReadService.queryNormal(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增新资产分类")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsTypenCfgDto dto){
        amsTypenCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改新资产分类")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsTypenCfgDto dto){
        amsTypenCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除新资产分类")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsTypenCfgDto dto){
        amsTypenCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
