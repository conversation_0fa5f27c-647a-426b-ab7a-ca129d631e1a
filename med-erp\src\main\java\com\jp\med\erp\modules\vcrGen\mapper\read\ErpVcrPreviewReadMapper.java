package com.jp.med.erp.modules.vcrGen.mapper.read;

import com.jp.med.erp.modules.vcrGen.dto.ErpVcrPreviewDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpVcrPreviewVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 凭证预览映射表
 * <AUTHOR>
 * @email -
 * @date 2025-03-11 17:00:33
 */
@Mapper
public interface ErpVcrPreviewReadMapper extends BaseMapper<ErpVcrPreviewDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrPreviewVo> queryList(ErpVcrPreviewDto dto);
}
