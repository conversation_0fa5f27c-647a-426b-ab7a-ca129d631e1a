package com.jp.med.ams.modules.changes.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 资产变更记录表
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
@Data
@TableName("ams_chg_rcd")
public class AmsChgRcdEntity {

	/** ID */
	@TableId("id")
	private Integer id;

	/** 变动编号 */
	@TableField("chg_no")
	private Integer chgNo;

	/** 固定资产码 */
	@TableField("fa_code")
	private String faCode;

	/** 变动前值 */
	@TableField("chg_before")
	private String chgBefore;

	/** 变动后值 */
	@TableField("chg_after")
	private String chgAfter;

	/** 变更时间 */
	@TableField("chg_date")
	private String chgDate;

	/** 变更人 */
	@TableField("chger")
	private String chger;

	/** 审核人 */
	@TableField("chker")
	private String chker;

	/** 变动类型 */
	@TableField("chg_type")
	private String chgType;

	/** 变动原因 */
	@TableField("chg_rea")
	private String chgRea;

	/** 备注 */
	@TableField("memo")
	private String memo;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

}
