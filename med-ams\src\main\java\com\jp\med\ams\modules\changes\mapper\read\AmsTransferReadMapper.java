package com.jp.med.ams.modules.changes.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.changes.dto.AmsTransferDto;
import com.jp.med.ams.modules.changes.vo.AmsTransferVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产转移
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 09:39:08
 */
@Mapper
public interface AmsTransferReadMapper extends BaseMapper<AmsTransferDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsTransferVo> queryList(AmsTransferDto dto);

    /**
     * 提交转移申请校验
     * @param dto
     * @return
     */
    boolean validateTransferApply(AmsTransferDto dto);

    /*
     * 判断是否能能删除转移记录
     * */
    boolean validateTransferCanDel(AmsTransferDto dto);

    List<AmsPropertyDto> selectTransferPropertyRefFacode(AmsTransferDto dot);

    /**
     * 批量查询转移记录对应的资产代码
     *
     * @param transferIds 转移记录ID列表
     * @return 转移记录关联的资产代码列表，包含transferId信息
     */
    List<AmsPropertyDto> batchSelectTransferPropertyRefFacode(@Param("transferIds") List<Long> transferIds);

    /**
     * 查询正在划拨转移的资产
     */
    List<AmsPropertyDto> queryTransferAndAllocProcessProperty(AmsPropertyDto dto);

    Integer queryAuditCount(AmsTransferDto dto);
}
