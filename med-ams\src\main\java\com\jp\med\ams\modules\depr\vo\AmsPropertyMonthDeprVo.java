package com.jp.med.ams.modules.depr.vo;

import com.jp.med.common.config.jackson.JsonBigDecimalFormatAnn;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 折旧(摊销)分配
 */
@Data
public class AmsPropertyMonthDeprVo {

    /**
     * ID
     */
    private Integer id;

    private String faCodes;

    private String deptUseCode;

    private String deptUseName;


    private List<BigDecimal> monthDepr;
    // 一月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal jan;

    // 二月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal feb;

    // 三月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal mar;

    // 四月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal apr;

    // 五月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal may;

    // 六月折旧/摊销

    @JsonBigDecimalFormatAnn
    private BigDecimal jun;

    // 七月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal jul;

    // 八月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal aug;

    // 九月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal sep;

    // 十月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal oct;

    // 十一月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal nov;

    // 十二月折旧/摊销
    @JsonBigDecimalFormatAnn
    private BigDecimal dec;

    // 合计
    @JsonBigDecimalFormatAnn
    private BigDecimal total;


}
