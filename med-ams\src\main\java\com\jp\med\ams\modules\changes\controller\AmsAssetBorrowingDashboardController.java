package com.jp.med.ams.modules.changes.controller;

import com.jp.med.ams.modules.changes.dto.AmsAssetBorrowingStatsDto;
import com.jp.med.ams.modules.changes.service.read.AmsAssetBorrowingDashboardService;
import com.jp.med.ams.modules.changes.vo.AmsAssetBorrowingDashboardVo;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资产借用管理控制器
 * 提供资产借用的基础数据查询功能
 *
 * <AUTHOR> Assistant
 * @email -
 * @date 2024-01-20 10:00:00
 */
@Slf4j
@Api(value = "资产借用管理", tags = "资产借用管理")
@RestController
@RequestMapping("borrowing/dashboard")
public class AmsAssetBorrowingDashboardController {

    @Autowired
    private AmsAssetBorrowingDashboardService dashboardService;

    /**
     * 获取最近活动列表
     */
    @ApiOperation("获取最近活动列表")
    @PostMapping("/activities")
    public CommonResult<List<AmsAssetBorrowingDashboardVo.RecentActivity>> getRecentActivities(
            @RequestBody AmsAssetBorrowingStatsDto dto) {
        log.info("📝 获取最近活动列表: {}", dto);
        try {
            List<AmsAssetBorrowingDashboardVo.RecentActivity> result = dashboardService.getRecentActivities(dto);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("❌ 获取最近活动列表失败", e);
            return CommonResult.failed("获取最近活动列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取过期提醒列表
     */
    @ApiOperation("获取过期提醒列表")
    @PostMapping("/alerts")
    public CommonResult<List<AmsAssetBorrowingDashboardVo.ExpiryReminder>> getExpiryReminders(
            @RequestBody AmsAssetBorrowingStatsDto dto) {
        log.info("⏰ 获取过期提醒列表: {}", dto);
        try {
            List<AmsAssetBorrowingDashboardVo.ExpiryReminder> result = dashboardService.getExpiryReminders(dto);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("❌ 获取过期提醒列表失败", e);
            return CommonResult.failed("获取过期提醒列表失败: " + e.getMessage());
        }
    }

}
