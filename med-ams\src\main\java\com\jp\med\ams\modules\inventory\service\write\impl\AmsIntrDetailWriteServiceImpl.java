package com.jp.med.ams.modules.inventory.service.write.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrDetailWriteMapper;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrTodoWriteMapper;
import com.jp.med.ams.modules.inventory.service.write.AmsIntrDetailWriteService;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import com.jp.med.common.util.BatchUtil;

import cn.hutool.core.util.StrUtil;

/**
 * 盘点数据明细表写服务实现类
 * <p>
 * 处理资产盘点明细记录 (`ams_intr_detail`) 的批量插入、盘盈删除和清理操作。
 * </p>
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Service
@Transactional(readOnly = false)
public class AmsIntrDetailWriteServiceImpl extends ServiceImpl<AmsIntrDetailWriteMapper, AmsIntrDetailDto>
        implements AmsIntrDetailWriteService {

    @Resource
    private AmsPropertyWriteMapper amsPropertyWriteMapper;
    @Qualifier("amsIntrTodoWriteMapper")
    @Autowired
    private AmsIntrTodoWriteMapper amsIntrTodoWriteMapper;

    @Resource
    private AmsIntrDetailWriteMapper amsIntrDetailWriteMapper;

    /**
     * 批量插入盘点明细记录。
     * <p>
     * 1. 遍历传入的 UID 列表 (`dto.uids`)。
     * 2. 为每个 UID 创建一个 `AmsIntrDetailDto` 对象。
     * 3. 设置 UID、任务 ID (`taskId`) 和是否手动录入标志 (`isManual`)。
     * 4. 将创建的对象添加到结果列表中。
     * 5. 使用 `BatchUtil` 批量插入盘点明细记录。
     * </p>
     *
     * @param dto 包含 UID 列表 (`uids`)、任务 ID (`taskId`) 和手动标志 (`isManual`) 的 DTO。
     */
    @Override
    public void intr(AmsIntrDetailDto dto) {
        List<String> uids = dto.getUids();
        List<AmsIntrDetailDto> resultList = new ArrayList<>();
        for (String uid : uids) {
            AmsIntrDetailDto detailDto = new AmsIntrDetailDto();
            detailDto.setUid(uid);
            detailDto.setTaskId(dto.getTaskId());
            detailDto.setIsManual(dto.getIsManual());
            resultList.add(detailDto);
        }
        BatchUtil.batch(resultList, AmsIntrDetailWriteMapper.class);
    }

    /**
     * 删除盘盈相关的记录。
     * <p>
     * 1. 校验传入的 UID 是否为空。
     * 2. 根据 UID 和 `is_manual=2` (盘盈) 条件删除 `ams_intr_detail` 表中的盘盈明细记录。
     * 3. 根据 UID 删除 `ams_property` 表中对应的盘盈资产记录。
     * 4. 根据 `dto.key` (推测是 `ams_intr_todo` 的主键) 删除对应的待办事项记录。
     * </p>
     *
     * @param dto 包含要删除盘盈记录的 UID 和关联的待办事项 key (`key`) 的 DTO。
     */
    @Override
    public void deleteProfit(AmsIntrDetailDto dto) {
        if (StrUtil.isNotBlank(dto.getUid())) {
            QueryWrapper<AmsIntrDetailDto> amsIntrDetailDtoQueryWrapper = new QueryWrapper<>();
            amsIntrDetailDtoQueryWrapper.eq("uid", dto.getUid());
            amsIntrDetailDtoQueryWrapper.eq("is_manual", 2);

            // 删除盘盈记录
            baseMapper.delete(amsIntrDetailDtoQueryWrapper);

            // 删除盘盈非固定资产
            QueryWrapper<AmsPropertyDto> amsPropertyDtoQueryWrapper = new QueryWrapper<>();
            amsPropertyDtoQueryWrapper.eq("uid", dto.getUid());
            amsPropertyWriteMapper.delete(amsPropertyDtoQueryWrapper);
            //

            // baseMapper.deleteById(dto.getId());
            // 删除盘点todo
            // Wrappers.lambdaQuery(AmsIntrDetailDto)
            amsIntrTodoWriteMapper.deleteById(dto.getKey());
        }

    }

    /**
     * 清理指定任务下、指定 UID 列表中的非盘盈盘点明细记录。
     * <p>
     * 构建删除条件：
     * - UID 在传入的 `uids` 列表中。
     * - 任务 ID (`taskId`) 等于传入的 `dto.taskId`。
     * - 手动标志 (`isManual`) 不等于 2 (即不是盘盈) 或者为 NULL。
     * 执行删除操作。
     * </p>
     *
     * @param dto 包含要清理的 UID 列表 (`uids`) 和任务 ID (`taskId`) 的 DTO。
     */
    @Override
    public void cleanIntr(AmsIntrDetailDto dto) {
        List<String> uids = dto.getUids();
        LambdaQueryWrapper<AmsIntrDetailDto> lambda = new QueryWrapper<AmsIntrDetailDto>().lambda();
        lambda.in(AmsIntrDetailDto::getUid, uids);
        lambda.eq(AmsIntrDetailDto::getTaskId, dto.getTaskId());
        lambda.and(wrapper -> wrapper.ne(AmsIntrDetailDto::getIsManual, 2)
                .or()
                .isNull(AmsIntrDetailDto::getIsManual));
        amsIntrDetailWriteMapper.delete(lambda);
    }
}
