package com.jp.med.ams.modules.config.mapper.read;

import com.jp.med.ams.modules.config.dto.AmsDeprCfgDto;
import com.jp.med.ams.modules.config.vo.AmsDeprCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 资产折旧配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:09:03
 */
@Mapper
public interface AmsDeprCfgReadMapper extends BaseMapper<AmsDeprCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsDeprCfgVo> queryList(AmsDeprCfgDto dto);
}
