package com.jp.med.erp.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.erp.modules.config.mapper.read.ErpReimSalaryTaskReadMapper;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDto;
import com.jp.med.erp.modules.config.vo.ErpReimSalaryTaskVo;
import com.jp.med.erp.modules.config.service.read.ErpReimSalaryTaskReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class ErpReimSalaryTaskReadServiceImpl extends ServiceImpl<ErpReimSalaryTaskReadMapper, ErpReimSalaryTaskDto> implements ErpReimSalaryTaskReadService {

    @Autowired
    private ErpReimSalaryTaskReadMapper erpReimSalaryTaskReadMapper;

    @Override
    public List<ErpReimSalaryTaskVo> queryList(ErpReimSalaryTaskDto dto) {
        return erpReimSalaryTaskReadMapper.queryList(dto);
    }

    @Override
    public List<ErpReimSalaryTaskVo> queryPageList(ErpReimSalaryTaskDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpReimSalaryTaskReadMapper.queryList(dto);
    }

}
