package com.jp.med.ams.modules.changes.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.changes.mapper.read.AmsChgRcdReadMapper;
import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.vo.AmsChgRcdVo;
import com.jp.med.ams.modules.changes.service.read.AmsChgRcdReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsChgRcdReadServiceImpl extends ServiceImpl<AmsChgRcdReadMapper, AmsChgRcdDto> implements AmsChgRcdReadService {

    @Autowired
    private AmsChgRcdReadMapper amsChgRcdReadMapper;

    @Override
    public List<AmsChgRcdVo> queryList(AmsChgRcdDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsChgRcdReadMapper.queryList(dto);
    }

}
