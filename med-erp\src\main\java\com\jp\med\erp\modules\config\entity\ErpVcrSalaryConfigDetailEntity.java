package com.jp.med.erp.modules.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 财务核算-工资凭证科目映射配置明细
 * <AUTHOR>
 * @email -
 * @date 2024-08-22 09:54:23
 */
@Data
@TableName("erp_vcr_salary_config_detail")
public class ErpVcrSalaryConfigDetailEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 工资配置id */
	@TableField("salary_config_id")
	private Integer salaryConfigId;

	/** 会计科目代码 */
	@TableField("actig_sub_code")
	private String actigSubCode;

	/** 会计科目名称 */
	@TableField("actig_sub_name")
	private String actigSubName;

	/** 科目类型  1.会计 2.预算 */
	@TableField("actig_sys")
	private String actigSys;

	/** 科室代码 */
	@TableField("dept_code")
	private String deptCode;

	/** 科室名称 */
	@TableField("dept_name")
	private String deptName;

	/** 往来单位代码 */
	@TableField("rel_co_code")
	private String relCoCode;

	/** 往来单位名称 */
	@TableField("rel_co_name")
	private String relCoName;

	/** 功能科目代码 */
	@TableField("fun_sub_code")
	private String funSubCode;

	/** 功能科目名称 */
	@TableField("fun_sub_name")
	private String funSubName;

	/** 经济科目代码 */
	@TableField("econ_sub_code")
	private String econSubCode;

	/** 经济科目名称 */
	@TableField("econ_sub_name")
	private String econSubName;

	/** 项目代码 */
	@TableField("proj_code")
	private String projCode;

	/** 项目名称 */
	@TableField("proj_name")
	private String projName;

	/** 现金流量代码 */
	@TableField("cash_flow_code")
	private String cashFlowCode;

	/** 现金流量名称 */
	@TableField("cash_flow_name")
	private String cashFlowName;

	/** 金额类型 1:借 2:贷 */
	@TableField("actig_amt_type")
	private String actigAmtType;

	/** 摘要 */
	@TableField("abst")
	private String abst;

}
