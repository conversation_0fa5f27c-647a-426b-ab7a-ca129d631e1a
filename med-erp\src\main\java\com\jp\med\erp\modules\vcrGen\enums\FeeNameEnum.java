package com.jp.med.erp.modules.vcrGen.enums;

import io.seata.common.util.StringUtils;

/**
 * 凭证类型赌赢费用名称
 */
public enum FeeNameEnum {
    VCR_TRAVEL("1","101","差旅费"),
    VCR_TRAINING("2","102","培训费"),
    VCR_REIM("3","000","其他费用"),
    VCR_SHARE("4","103","分摊费用"),
    VCR_SALARY("5","104","工资费用"),
    VCR_CONTRACT("6","105","合同费用"),
    VCR_DEPR("7","106","折旧费用"),
    VCR_PURC("8","107","零星采购费用"),

    VCR_WZ_PURC("10","110","物资采购费用"),

    VCR_LABOR_SERVE("11","111","其他费用(无发票)"),

    VCR_TRANSACTIONS("12","112","往来支付"),

    LOAN_REIM("13","113","借款报销");


    private String type;

    private String payTypeCode;

    private String feeName;

    FeeNameEnum(String type,String payTypeCode, String feeName) {
        this.type = type;
        this.payTypeCode = payTypeCode;
        this.feeName = feeName;
    }

    public String getType() {
        return type;
    }

    public String getPayTypeCode() {
        return payTypeCode;
    }

    public String getFeeName() {
        return feeName;
    }

    public static FeeNameEnum getByType(String type) {
        for (FeeNameEnum status : FeeNameEnum.values()) {
            if (StringUtils.equals(status.getType(),type)) {
                return status;
            }
        }
        return null;
    }
}
