package com.jp.med.ams.modules.config.service.write.impl;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.jp.med.ams.modules.config.vo.AmsStockCfgVo;
import com.jp.med.common.util.BatchUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.config.mapper.write.AmsStockCfgWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsStockCfgDto;
import com.jp.med.ams.modules.config.service.write.AmsStockCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产卡片配置
 * <AUTHOR>
 * @email -
 * @date 2023-10-10 15:21:09
 */
@Service
@Transactional(readOnly = false)
public class AmsStockCfgWriteServiceImpl extends ServiceImpl<AmsStockCfgWriteMapper, AmsStockCfgDto> implements AmsStockCfgWriteService {

    @Autowired
    private AmsStockCfgWriteMapper amsStockCfgWriteMapper;

    @Override
    public boolean save(AmsStockCfgDto amsStockCfgDto){
        List<AmsStockCfgDto> amsStockCfgDtos = new ArrayList<>();
        String id;
        if (StringUtils.isEmpty(amsStockCfgDto.getCardCode())) {
            id = UuidUtils.generateUuid();
        }else {
            id = amsStockCfgDto.getCardCode();
            amsStockCfgWriteMapper.deleteByCode(amsStockCfgDto);
        }
        List<String> flds = amsStockCfgDto.getFlds();
        for (AmsStockCfgVo amsStockCfgVo : amsStockCfgDto.getList()) {
            if (flds.contains(amsStockCfgVo.getColumnName())){
                AmsStockCfgDto cfgDto = new AmsStockCfgDto();
                cfgDto.setFld(amsStockCfgVo.getColumnName());
                cfgDto.setMustl(amsStockCfgVo.getMustl());
                cfgDto.setCardCode(id);
                cfgDto.setCardName(amsStockCfgDto.getCardName());
                amsStockCfgDtos.add(cfgDto);
            }
        }
        BatchUtil.batch(amsStockCfgDtos, AmsStockCfgWriteMapper.class);
        return true;
    }
}
