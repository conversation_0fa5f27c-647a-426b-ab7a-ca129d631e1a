package com.jp.med.ams.modules.depr.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.jp.med.ams.modules.depr.vo.AmsDeprAsgnVo;

import java.util.List;

/**
 * 资产折旧分配
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
public interface AmsDeprAsgnReadService extends IService<AmsDeprAsgnDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsDeprAsgnVo> queryList(AmsDeprAsgnDto dto);
}

