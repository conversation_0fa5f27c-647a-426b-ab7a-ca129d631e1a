package com.jp.med.ams.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.config.mapper.write.AmsStorageCfgWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsStorageCfgDto;
import com.jp.med.ams.modules.config.service.write.AmsStorageCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 存放地点配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-12 10:23:40
 */
@Service
@Transactional(readOnly = false)
public class AmsStorageCfgWriteServiceImpl extends ServiceImpl<AmsStorageCfgWriteMapper, AmsStorageCfgDto> implements AmsStorageCfgWriteService {
}
