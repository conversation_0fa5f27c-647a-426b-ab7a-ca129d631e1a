package com.jp.med.ams.modules.config.service.write.impl;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.config.mapper.write.AmsLablCfgWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsLablCfgDto;
import com.jp.med.ams.modules.config.service.write.AmsLablCfgWriteService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * 资产标签配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 17:36:48
 */
@Service
@Transactional(readOnly = false)
public class  AmsLablCfgWriteServiceImpl extends ServiceImpl<AmsLablCfgWriteMapper, AmsLablCfgDto> implements AmsLablCfgWriteService {

    @Resource
    private AmsLablCfgWriteMapper amsLablCfgWriteMapper;

    @Override
    public boolean save(AmsLablCfgDto dto) {
        MultipartFile imgFile = dto.getImgFile();
        MultipartFile cardFile = dto.getCardFile();
        if (!Objects.isNull(imgFile)){
            String imgPath = OSSUtil.uploadFile(OSSConst.BUCKET_AMS, OSSConst.AMS_BASE_INFO_PATH, imgFile);
            dto.setImg(imgPath);
        }
        if (!Objects.isNull(cardFile)){
            String cardPath = OSSUtil.uploadFile(OSSConst.BUCKET_AMS, OSSConst.AMS_BASE_INFO_PATH, cardFile);
            dto.setAttachment(cardPath);
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dto.setCreateTime(sdf.format(new Date()));
            dto.setCrter(dto.getUsername());
            amsLablCfgWriteMapper.insert(dto);
        }catch (Exception e){
            OSSUtil.removeFile(OSSConst.BUCKET_AMS, dto.getImg());
            OSSUtil.removeFile(OSSConst.BUCKET_AMS, dto.getAttachment());
            throw new AppException("新增失败");
        }
        return true;
    }

    @Override
    public boolean updateById(AmsLablCfgDto dto){
        MultipartFile imgFile = dto.getImgFile();
        MultipartFile cardFile = dto.getCardFile();
        String img = dto.getImg();
        String attachment = dto.getAttachment();
        if (!Objects.isNull(imgFile)){
            String imgPath = OSSUtil.uploadFile(OSSConst.BUCKET_AMS, OSSConst.AMS_BASE_INFO_PATH, imgFile);
            dto.setImg(imgPath);
        }
        if (!Objects.isNull(cardFile)){
            String cardPath = OSSUtil.uploadFile(OSSConst.BUCKET_AMS, OSSConst.AMS_BASE_INFO_PATH, cardFile);
            dto.setAttachment(cardPath);
        }
        try {
            amsLablCfgWriteMapper.updateById(dto);
//            if (!Objects.isNull(imgFile)){
//                OSSUtil.removeFile(OSSConst.BUCKET_AMS,img);
//            }
//            if (!Objects.isNull(cardFile)){
//                OSSUtil.removeFile(OSSConst.BUCKET_AMS, attachment);
//            }
        }catch (Exception e){
//            OSSUtil.removeFile(OSSConst.BUCKET_AMS, dto.getImg());
//            OSSUtil.removeFile(OSSConst.BUCKET_AMS, dto.getAttachment());
            log.error(e.toString());
            throw new AppException("修改失败");
        }
        return true;
    }
}
