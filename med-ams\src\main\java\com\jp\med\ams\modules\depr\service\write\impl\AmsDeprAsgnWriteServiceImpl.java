package com.jp.med.ams.modules.depr.service.write.impl;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.depr.mapper.write.AmsDeprAsgnWriteMapper;
import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.jp.med.ams.modules.depr.service.write.AmsDeprAsgnWriteService;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资产折旧分配
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Service
@Transactional(readOnly = false)
public class AmsDeprAsgnWriteServiceImpl extends ServiceImpl<AmsDeprAsgnWriteMapper, AmsDeprAsgnDto>
        implements AmsDeprAsgnWriteService {

    @Resource
    private AmsDeprAsgnWriteMapper amsDeprAsgnWriteMapper;

    @Resource
    private AmsPropertyWriteMapper amsPropertyWriteMapper;

    @Override
    public boolean updateById(AmsDeprAsgnDto dto) {
        List<AmsDeprAsgnDto> list = dto.getList();

        // 1. 直接删除旧数据，而不是将activeFlag置为0
        int count = amsDeprAsgnWriteMapper.deleteByFaCode(dto.getFaCode());

        // 2. 准备新数据
        for (AmsDeprAsgnDto amsDeprAsgnDto : list) {
            amsDeprAsgnDto.setFaCode(dto.getFaCode());
            amsDeprAsgnDto.setModiRea("手动修改");
            amsDeprAsgnDto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
            amsDeprAsgnDto.setOpter(dto.getSysUser().getUsername());
            amsDeprAsgnDto.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        }

        // 3. 批量插入新数据
        BatchUtil.batch(list, AmsDeprAsgnWriteMapper.class);

        // 4. 同步更新资产使用科室
        // 将所有科室ID连接为字符串，以逗号分隔
        if (list != null && !list.isEmpty()) {
            String deptUse = list.stream()
                    .map(AmsDeprAsgnDto::getOrgId)
                    .collect(Collectors.joining(","));

            // 更新资产表的deptUse字段
            amsPropertyWriteMapper.updateDeptUseByFaCode(dto.getFaCode(), deptUse);
        }

        return true;
    }
}
