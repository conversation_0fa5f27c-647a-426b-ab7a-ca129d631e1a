package com.jp.med.ams.modules.inventory.mapper.read;

import com.jp.med.ams.modules.inventory.dto.AmsIntrDto;
import com.jp.med.ams.modules.inventory.vo.AmsIntrVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 资产盘点表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Mapper
public interface AmsIntrReadMapper extends BaseMapper<AmsIntrDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsIntrVo> queryList(AmsIntrDto dto);
}
