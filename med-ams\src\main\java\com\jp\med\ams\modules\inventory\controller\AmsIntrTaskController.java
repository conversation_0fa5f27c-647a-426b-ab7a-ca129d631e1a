package com.jp.med.ams.modules.inventory.controller;

import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrTaskReadService;
import com.jp.med.ams.modules.inventory.service.write.AmsIntrTaskWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 资产盘点任务
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 10:12:34
 */
@Api(value = "资产盘点任务", tags = "资产盘点任务")
@RestController
@RequestMapping("amsIntrTask")
public class AmsIntrTaskController {

    @Autowired
    private AmsIntrTaskReadService amsIntrTaskReadService;

    @Autowired
    private AmsIntrTaskWriteService amsIntrTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产盘点任务")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsIntrTaskDto dto){
        return CommonResult.paging(amsIntrTaskReadService.queryList(dto));
    }

    @ApiOperation("查询资产盘点情况")
    @PostMapping("/queryIntr")
    public CommonResult<?> queryIntr(@RequestBody AmsIntrTaskDto dto){
        return CommonResult.success(amsIntrTaskReadService.queryIntr(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产盘点任务")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsIntrTaskDto dto){
        dto.setStatus("0");
        amsIntrTaskWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产盘点任务")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsIntrTaskDto dto){
        amsIntrTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    @ApiOperation("完成盘点")
    @PostMapping("/compltetIntr")
    public CommonResult<?> compltetIntr(@RequestBody AmsIntrTaskDto dto){
        amsIntrTaskWriteService.compltetIntr(dto);
        return CommonResult.success();
    }


    /**
     * 删除
     */
    @ApiOperation("删除资产盘点任务")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsIntrTaskDto dto){
        amsIntrTaskWriteService.removeById(dto);
        return CommonResult.success();
    }


    /**
     * 对比盘点任务资产与当前资产差异
     */
    @ApiOperation("对比盘点任务资产与当前资产差异")
    @PostMapping("/diff")
    public CommonResult<?> diff(@RequestBody AmsIntrTaskDto dto) {
        return CommonResult.success(amsIntrTaskReadService.diff(dto));
    }




}
