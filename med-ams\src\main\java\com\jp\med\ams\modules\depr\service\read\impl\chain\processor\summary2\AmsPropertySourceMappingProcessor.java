package com.jp.med.ams.modules.depr.service.read.impl.chain.processor.summary2;

import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.mapper.read.AmsBasicCfgReadMapper;
import com.jp.med.ams.modules.config.vo.AmsBasicCfgVo;
import com.jp.med.ams.modules.depr.service.read.impl.chain.context.AmsPropertyDeprSummary2Context;
import com.jp.med.ams.modules.depr.service.read.impl.chain.processor.base.AbstractAmsPropertyDeprProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 资金来源映射处理器
 * 负责获取资金来源配置并创建映射
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class AmsPropertySourceMappingProcessor
        extends AbstractAmsPropertyDeprProcessor<AmsPropertyDeprSummary2Context> {

    @Autowired
    private AmsBasicCfgReadMapper amsBasicCfgReadMapper;

    @Override
    protected void doProcess(AmsPropertyDeprSummary2Context context) {
        log.debug("开始处理资金来源映射");

        // 获取资金来源映射
        AmsBasicCfgDto amsBasicCfgDto = new AmsBasicCfgDto();
        amsBasicCfgDto.setType("资金来源");
        List<AmsBasicCfgVo> sourceList = amsBasicCfgReadMapper.queryList(amsBasicCfgDto);
        context.setSourceList(sourceList);

        Map<String, String> sourceCodeMap = sourceList.stream()
                .collect(Collectors.toMap(
                        AmsBasicCfgVo::getCode,
                        AmsBasicCfgVo::getName,
                        (existing, replacement) -> existing));
        context.setSourceCodeMap(sourceCodeMap);

        String finaSubsidyCode = sourceList.stream()
                .filter(item -> item.getName().equals("财政补助资金"))
                .findFirst()
                .map(AmsBasicCfgVo::getCode)
                .orElse(null);
        context.setFinaSubsidyCode(finaSubsidyCode);

        // 添加往年财政补助资金映射
        if (finaSubsidyCode != null) {
            sourceCodeMap.put("wl" + finaSubsidyCode, "往年财政补助资金");
        }

        log.debug("资金来源映射处理完成，共加载 {} 个资金来源", sourceList.size());
    }
}
