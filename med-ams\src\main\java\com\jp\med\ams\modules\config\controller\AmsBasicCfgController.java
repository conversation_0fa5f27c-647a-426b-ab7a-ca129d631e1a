package com.jp.med.ams.modules.config.controller;

import com.jp.med.ams.modules.config.mapper.read.AmsBasicCfgReadMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.service.read.AmsBasicCfgReadService;
import com.jp.med.ams.modules.config.service.write.AmsBasicCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashMap;
import java.util.List;


/**
 * 资产配置表
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:47:25
 */
@Api(value = "资产配置表", tags = "资产配置表")
@RestController
@RequestMapping("amsBasicCfg")
public class AmsBasicCfgController {

    @Autowired
    private AmsBasicCfgReadService amsBasicCfgReadService;
    @Autowired

    private AmsBasicCfgReadMapper amsBasicCfgReadMapper;

    @Autowired
    private AmsBasicCfgWriteService amsBasicCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产配置表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsBasicCfgDto dto){
        return CommonResult.paging(amsBasicCfgReadService.queryList(dto));
    }

    @ApiOperation("查询配置表")
    @PostMapping("/queryConfig")
    public CommonResult<?> queryConfig(@RequestBody AmsBasicCfgDto dto){
        return CommonResult.success(amsBasicCfgReadService.queryConfig(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产配置表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsBasicCfgDto dto){
        amsBasicCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产配置表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsBasicCfgDto dto){
        amsBasicCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产配置表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsBasicCfgDto dto){
        amsBasicCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("判断是否唯一")
    @PostMapping("/checkOnly")
    public CommonResult<?> checkOnly(@RequestBody AmsBasicCfgDto dto){
        amsBasicCfgReadService.checkOnly(dto);
        return CommonResult.success();
    }


    @ApiOperation("资金性质assetFundingNature")
    @PostMapping("/assetFundingNature")
    public CommonResult<?> assetFundingNature(@RequestBody AmsBasicCfgDto dto) {
        List<HashMap<String, Object>> list = amsBasicCfgReadMapper.queryEcsItemCfgList(dto);
        return CommonResult.success(list);
    }

}
