package com.jp.med.ams.modules.depr.vo;

import com.jp.med.common.config.jackson.JsonBigDecimalFormatAnn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 折旧(摊销)分配
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmsPropertyDepr2Vo {

    /**
     * ID
     */
    private Integer id;

    /**
     * 资产代码
     */
    private String faCodes;

    /**
     * 科室代码
     */
    private String deptUseCode;

    /**
     * 科室名称
     */
    private String deptUseName;

    /**
     * 资产类型代码
     */
    private String assetTypeCode;

    /**
     * 资产类型名称
     */
    private String assetTypeName;

    /**
     * 来源代码
     */
    private String sourceCode;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 本月计提原值
     */
    @JsonBigDecimalFormatAnn
    private BigDecimal monthDeprAmt;

    /**
     * 上月计提原值
     */
    @JsonBigDecimalFormatAnn
    private BigDecimal lastMonthDeprAmt;
    /**
     * 上月原值变化
     */
    @JsonBigDecimalFormatAnn
    private BigDecimal lastMonthDeprAmtChange;

    /**
     * 月 折旧/摊销额
     */
    @JsonBigDecimalFormatAnn
    private BigDecimal deprAmt;

    /**
     * 月折旧/摊销率%
     */
    // @JsonBigDecimalFormatAnn
    private BigDecimal deprRate;

    /**
     * 累计折旧/摊销
     */
    @JsonBigDecimalFormatAnn
    private BigDecimal deprAmtSum;

    /**
     * 是否为合计行
     */
    private Boolean isSummary;


    /*
    是否为摊销资产
     */
    private Boolean isAmortizedAssets;

    public AmsPropertyDepr2Vo(BigDecimal deprAmtSum, BigDecimal deprAmt, BigDecimal lastMonthDeprAmt,
                              BigDecimal lastMonthDeprAmtChange, BigDecimal monthDeprAmt) {
        this.setDeprAmtSum(deprAmtSum);
        this.setDeprAmt(deprAmt);
        this.setLastMonthDeprAmt(lastMonthDeprAmt);
        this.setLastMonthDeprAmtChange(lastMonthDeprAmtChange);
        this.setMonthDeprAmt(monthDeprAmt);
    }

    public static AmsPropertyDepr2Vo summarize(List<AmsPropertyDepr2Vo> assets) {
        BigDecimal deprAmtSum = BigDecimal.ZERO;
        BigDecimal deprAmt = BigDecimal.ZERO;
        BigDecimal lastMonthDeprAmt = BigDecimal.ZERO;
        BigDecimal lastMonthDeprAmtChange = BigDecimal.ZERO;
        BigDecimal monthDeprAmt = BigDecimal.ZERO;

        for (AmsPropertyDepr2Vo asset : assets) {
            if (asset.getDeprAmtSum() != null) {
                deprAmtSum = deprAmtSum.add(asset.getDeprAmtSum());
            }
            if (asset.getDeprAmt() != null) {
                deprAmt = deprAmt.add(asset.getDeprAmt());
            }
            if (asset.getLastMonthDeprAmt() != null) {
                lastMonthDeprAmt = lastMonthDeprAmt.add(asset.getLastMonthDeprAmt());
            }
            if (asset.getLastMonthDeprAmtChange() != null) {
                lastMonthDeprAmtChange = lastMonthDeprAmtChange.add(asset.getLastMonthDeprAmtChange());
            }
            if (asset.getMonthDeprAmt() != null) {
                monthDeprAmt = monthDeprAmt.add(asset.getMonthDeprAmt());
            }
        }

        AmsPropertyDepr2Vo result = new AmsPropertyDepr2Vo(deprAmtSum, deprAmt, lastMonthDeprAmt,
                lastMonthDeprAmtChange, monthDeprAmt);
        result.setIsSummary(true);
        return result;
    }

    @Data
    @AllArgsConstructor
    public static class Summary {
        private BigDecimal deprAmtSum;
        private BigDecimal deprAmt;
        private BigDecimal lastMonthDeprAmt;
        private BigDecimal lastMonthDeprAmtChange;
        private BigDecimal monthDeprAmt;
    }
}
