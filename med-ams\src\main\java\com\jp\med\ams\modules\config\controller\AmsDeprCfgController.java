package com.jp.med.ams.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.config.dto.AmsDeprCfgDto;
import com.jp.med.ams.modules.config.service.read.AmsDeprCfgReadService;
import com.jp.med.ams.modules.config.service.write.AmsDeprCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 资产折旧配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:09:03
 */
@Api(value = "资产折旧配置", tags = "资产折旧配置")
@RestController
@RequestMapping("amsDeprCfg")
public class AmsDeprCfgController {

    @Autowired
    private AmsDeprCfgReadService amsDeprCfgReadService;

    @Autowired
    private AmsDeprCfgWriteService amsDeprCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产折旧配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsDeprCfgDto dto){
        return CommonResult.paging(amsDeprCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产折旧配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsDeprCfgDto dto){
        amsDeprCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产折旧配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsDeprCfgDto dto){
        amsDeprCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产折旧配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsDeprCfgDto dto){
        amsDeprCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
