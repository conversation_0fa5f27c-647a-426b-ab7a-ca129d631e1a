-- =================================================================================================
-- 快速数据检查脚本：快速验证源表数据状态
-- 功能：简单快速地检查源表是否存在数据，以及基本的数据质量
-- 使用方法：直接在数据库中执行此脚本
-- =================================================================================================

-- 检查入库源表
SELECT 
    '📦 入库源表检查' as check_item,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_inport_storage_six')
        THEN '✅ 表存在'
        ELSE '❌ 表不存在'
    END as table_status,
    COALESCE((
        SELECT COUNT(*) 
        FROM mmis_temp_inport_storage_six
    ), 0) as total_records,
    COALESCE((
        SELECT COUNT(*) 
        FROM mmis_temp_inport_storage_six 
        WHERE name IS NOT NULL AND TRIM(name) != ''
          AND supplier_name IS NOT NULL AND TRIM(supplier_name) != ''
          AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
    ), 0) as valid_records;

-- 检查出库源表
SELECT 
    '📤 出库源表检查' as check_item,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_xinxike_outbound_six')
        THEN '✅ 表存在'
        ELSE '❌ 表不存在'
    END as table_status,
    COALESCE((
        SELECT COUNT(*) 
        FROM mmis_temp_xinxike_outbound_six
    ), 0) as total_records,
    COALESCE((
        SELECT COUNT(*) 
        FROM mmis_temp_xinxike_outbound_six 
        WHERE name IS NOT NULL AND TRIM(name) != ''
          AND out_org IS NOT NULL AND TRIM(out_org) != ''
          AND num IS NOT NULL AND num ~ '^[0-9]+\.?[0-9]*$' AND CAST(num AS numeric) > 0
    ), 0) as valid_records;

-- 检查关联表状态
SELECT 
    '🔗 物资信息表检查' as check_item,
    '✅ 系统表' as table_status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN ref_price IS NOT NULL AND ref_price > 0 THEN 1 END) as valid_records
FROM mmis_aset_info_assist;

SELECT 
    '👥 员工信息表检查' as check_item,
    '✅ 系统表' as table_status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN emp_code IS NOT NULL AND emp_name IS NOT NULL THEN 1 END) as valid_records
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy' AND is_deleted = 0;

SELECT 
    '🏢 组织信息表检查' as check_item,
    '✅ 系统表' as table_status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN org_id IS NOT NULL AND org_name IS NOT NULL THEN 1 END) as valid_records
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' AND active_flag = '1';

-- 如果源表存在，显示数据样例
-- 入库数据样例
SELECT 
    '📦 入库数据样例' as sample_type,
    name,
    supplier_name,
    num,
    price,
    remark
FROM mmis_temp_inport_storage_six 
WHERE name IS NOT NULL AND TRIM(name) != ''
LIMIT 3;

-- 出库数据样例
SELECT 
    '📤 出库数据样例' as sample_type,
    name,
    out_org,
    out_emp,
    num,
    price,
    remark
FROM mmis_temp_xinxike_outbound_six 
WHERE name IS NOT NULL AND TRIM(name) != ''
LIMIT 3;

-- 导入可行性快速评估
SELECT 
    '🎯 导入可行性评估' as assessment,
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_inport_storage_six')
        THEN '❌ 入库源表不存在'
        WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mmis_temp_xinxike_outbound_six')
        THEN '❌ 出库源表不存在'
        WHEN (SELECT COUNT(*) FROM mmis_temp_inport_storage_six) = 0 
         AND (SELECT COUNT(*) FROM mmis_temp_xinxike_outbound_six) = 0
        THEN '❌ 源表无数据'
        WHEN (SELECT COUNT(*) FROM mmis_temp_inport_storage_six WHERE name IS NOT NULL AND supplier_name IS NOT NULL) > 0
          OR (SELECT COUNT(*) FROM mmis_temp_xinxike_outbound_six WHERE name IS NOT NULL AND out_org IS NOT NULL) > 0
        THEN '✅ 可以执行导入'
        ELSE '⚠️ 数据质量需要检查'
    END as result;
