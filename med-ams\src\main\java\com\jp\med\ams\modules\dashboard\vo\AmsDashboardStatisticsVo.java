package com.jp.med.ams.modules.dashboard.vo;

import lombok.Data;

import java.util.Map;

/**
 * 仪表盘统计数据VO
 * 用于完整的仪表盘数据导出和展示
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class AmsDashboardStatisticsVo {

    /**
     * 指标数据
     */
    private AmsDashboardMetricsVo metrics;

    /**
     * 图表数据
     */
    private AmsDashboardChartVo charts;

    /**
     * 统计时间范围
     */
    private String dateRange;

    /**
     * 统计年度
     */
    private Integer year;

    /**
     * 统计科室
     */
    private String departments;

    /**
     * 统计资产类型
     */
    private String assetTypes;

    /**
     * 生成时间
     */
    private String generateTime;

    /**
     * 生成人
     */
    private String generator;

    /**
     * 统计汇总信息
     */
    private Map<String, Object> summary;

    /**
     * 构建统计汇总信息
     */
    public void buildSummary() {
        if (metrics != null) {
            summary = new java.util.HashMap<>();
            summary.put("totalTasks", metrics.getTotalTasks());
            summary.put("completionRate", metrics.getCompletionRate() + "%");
            summary.put("totalAssets", metrics.getTotalAssets());
            summary.put("inventoriedRate",
                    metrics.getTotalAssets() > 0 ?
                            String.format("%.1f%%", metrics.getInventoriedAssets() * 100.0 / metrics.getTotalAssets()) :
                            "0%");
            summary.put("profitAssets", metrics.getProfitAssets());
            summary.put("lossAssets", metrics.getLossAssets());
        }
    }
} 