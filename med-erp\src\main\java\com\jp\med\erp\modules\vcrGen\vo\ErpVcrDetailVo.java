package com.jp.med.erp.modules.vcrGen.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 凭证信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-07 23:49:36
 */
@Data
public class ErpVcrDetailVo {

	/** id */
	private Integer id;

	/** 凭证类型 */
	private String type;

	/** 凭证号id,u8生成 */
	private String idpzh;

	/** 会计期间 */
	private String kjqj;

	/** 凭证号 */
	private String pzh;

	/** 财务凭证金额 */
	private BigDecimal pzje;

	/** 预算凭证金额 */
	private BigDecimal yspzje;

	/** 凭证状态 1.仅保存 2.已生成 */
	private String status;

	/** 报销分类大类 报销  **/
	private String supType;

	private List<ErpVcrItemDetailVo> detail;

}
